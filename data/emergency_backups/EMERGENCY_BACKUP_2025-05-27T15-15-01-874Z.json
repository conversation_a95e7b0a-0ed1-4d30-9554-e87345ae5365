{"timestamp": "2025-05-27T15:15:01.874Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.03689621132619, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9223681599999999, "confidence": -4.134258073889501, "energy": 1, "focus": 0.8658178289608749, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748358849828, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15054654493134, "neuronActivity": 0.5216246035948481}, "emotionalHistory": [{"timestamp": 1748358429430, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.49000463839825914}, {"timestamp": 1748358444427, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01490004638399, "neuronActivity": 0.49000463839825914}, {"timestamp": 1748358459428, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01490004638399, "neuronActivity": 0.49000463839825914}, {"timestamp": 1748358474428, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02980009276797, "neuronActivity": 0.49000463839825914}, {"timestamp": 1748358489428, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02980009276797, "neuronActivity": 0.49000463839825914}, {"timestamp": 1748358504427, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04470013915196, "neuronActivity": 0.49000463839825914}, {"timestamp": 1748358519429, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04470013915196, "neuronActivity": 0.49000463839825914}, {"timestamp": 1748358534448, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04470013915196, "neuronActivity": 0.49000463839825914}, {"timestamp": 1748358549448, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05968469257819, "neuronActivity": 0.4984553426236112}, {"timestamp": 1748358564455, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05968469257819, "neuronActivity": 0.4984553426236112}, {"timestamp": 1748358579490, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07466924600442, "neuronActivity": 0.4984553426236112}, {"timestamp": 1748358594491, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08969605295178, "neuronActivity": 0.5026806947362872}, {"timestamp": 1748358609633, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08969605295178, "neuronActivity": 0.5026806947362872}, {"timestamp": 1748358624713, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10472285989914, "neuronActivity": 0.5026806947362872}, {"timestamp": 1748358640397, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10472285989914, "neuronActivity": 0.5026806947362872}, {"timestamp": 1748358655904, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11987471964821, "neuronActivity": 0.5151859749066303}, {"timestamp": 1748358672010, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11987471964821, "neuronActivity": 0.5151859749066303}, {"timestamp": 1748358688139, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13502657939728, "neuronActivity": 0.5151859749066303}, {"timestamp": 1748358705886, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13502657939728, "neuronActivity": 0.5151859749066303}, {"timestamp": 1748358727365, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14017843914634, "neuronActivity": 0.5151859749066303}, {"timestamp": 1748358749379, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14017843914634, "neuronActivity": 0.5151859749066303}, {"timestamp": 1748358786869, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1453302988954, "neuronActivity": 0.5151859749066303}, {"timestamp": 1748358849828, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15054654493134, "neuronActivity": 0.5216246035948481}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:11:45 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:12:07 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:12:29 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:13:06 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:14:09 AM", "mood": "curious"}], "circadianRhythm": 0.12528544391101837, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8705004924824354, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.8123880364987437, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9822572880064331, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9471146705154977, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748358415699, "expiresAt": 1748359015699}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.954173861750444, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748358415700, "expiresAt": 1748359015700}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.9186006424142352, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748358453806, "expiresAt": 1748359053806}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8705004924824354, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.8123880364987437, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9822572880064331, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9471146705154977, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748358415699, "expiresAt": 1748359015699}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.954173861750444, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748358415700, "expiresAt": 1748359015700}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.9186006424142352, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748358453806, "expiresAt": 1748359053806}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.153006629505208, "efficiency": 0.8291803977703125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748358806470, "totalBoostPower": 12.918039777031249, "averageEnergy": 1, "averageStability": 0.9141724986112982, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748358474383, "memoryEfficiency": 0, "thermalStability": 0.9735538130005201, "cpuUsage": 0.3990234375, "responseTime": 298.8138021360911, "overall": 0.5475473121346082}, {"timestamp": 1748358534447, "memoryEfficiency": 0, "thermalStability": 0.9967928405023283, "cpuUsage": 0.4271728515625, "responseTime": 482.59878182175447, "overall": 0.5105378428574934}, {"timestamp": 1748358594465, "memoryEfficiency": 0, "thermalStability": 0.9976250428292487, "cpuUsage": 0.458544921875, "responseTime": 417.9412992877503, "overall": 0.5426492627338615}, {"timestamp": 1748358655166, "memoryEfficiency": 0, "thermalStability": 0.9818473966999186, "cpuUsage": 0.451904296875, "responseTime": 582.4847011761824, "overall": 0.5221276003947597}, {"timestamp": 1748358719355, "memoryEfficiency": 0, "thermalStability": 0.9872114286240604, "cpuUsage": 0.42705078125, "responseTime": 408.6304355825662, "overall": 0.5336647606869632}, {"timestamp": 1748358806470, "memoryEfficiency": 0, "thermalStability": 0.9912019638551606, "cpuUsage": 0.41591796875, "responseTime": 582.7639854451719, "overall": 0.5762598915647915}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}