{"timestamp": "2025-05-27T01:48:15.391Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2145089309740962, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -1.8681086897703205, "energy": 1, "focus": 0.9607353583433934, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748310485328, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07576277837403, "neuronActivity": 0.518755870694129}, "emotionalHistory": [{"timestamp": 1748310350322, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310365322, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01512922032005, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310380322, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01512922032005, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310395323, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0302584406401, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310410324, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0302584406401, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310425325, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04538766096015, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310440327, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04538766096015, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310455327, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06057521966709, "neuronActivity": 0.518755870694129}, {"timestamp": 1748310470328, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06057521966709, "neuronActivity": 0.518755870694129}, {"timestamp": 1748310485328, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07576277837403, "neuronActivity": 0.518755870694129}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:47:05 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:47:20 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:47:35 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:47:50 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:48:05 PM", "mood": "curious"}], "circadianRhythm": 0.7271678488986155, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.9044797527511768, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.903004405822544, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.9354541240123236, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.9051936621342249, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310336413, "expiresAt": 1748310936413}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.931865248139808, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310336414, "expiresAt": 1748310936414}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.9739127950157661, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310372415, "expiresAt": 1748310972415}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.9044797527511768, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.903004405822544, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.9354541240123236, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.9051936621342249, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310336413, "expiresAt": 1748310936413}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.931865248139808, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310336414, "expiresAt": 1748310936414}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.9739127950157661, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310372415, "expiresAt": 1748310972415}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.3017208333333334, "efficiency": 0.83810325, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748310455259, "totalBoostPower": 13.810325, "averageEnergy": 1, "averageStability": 0.9256516646459739, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748310395271, "memoryEfficiency": 0, "thermalStability": 0.9842931462658776, "cpuUsage": 0.390966796875, "responseTime": 502.44735025723844, "overall": 0.5197154620968735}, {"timestamp": 1748310455278, "memoryEfficiency": 0, "thermalStability": 0.9984178108059698, "cpuUsage": 0.3766357421875, "responseTime": 468.2688271933047, "overall": 0.5853305662959094}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}