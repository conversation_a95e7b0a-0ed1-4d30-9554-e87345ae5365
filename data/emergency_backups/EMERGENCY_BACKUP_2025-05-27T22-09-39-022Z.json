{"timestamp": "2025-05-27T22:09:39.023Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2009383623378043, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.98, "confidence": -3.5602125279491794, "energy": 1, "focus": 0.9557441433780921, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748383779021, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14391888515752, "neuronActivity": 0.5451746932281701}, "emotionalHistory": [{"timestamp": 1748383493991, "mood": "curious", "dominantEmotion": "curiosity", "qi": 148, "neuronActivity": 0.5331878175806568}, {"timestamp": 1748383509006, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0153318781758, "neuronActivity": 0.5331878175806568}, {"timestamp": 1748383524006, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0153318781758, "neuronActivity": 0.5331878175806568}, {"timestamp": 1748383539007, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0306637563516, "neuronActivity": 0.5331878175806568}, {"timestamp": 1748383554008, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0306637563516, "neuronActivity": 0.5331878175806568}, {"timestamp": 1748383569009, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0459956345274, "neuronActivity": 0.5331878175806568}, {"timestamp": 1748383584010, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0459956345274, "neuronActivity": 0.5331878175806568}, {"timestamp": 1748383599011, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0613275127032, "neuronActivity": 0.5394809917204015}, {"timestamp": 1748383614014, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0613275127032, "neuronActivity": 0.5394809917204015}, {"timestamp": 1748383629015, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0767223226204, "neuronActivity": 0.5394809917204015}, {"timestamp": 1748383644016, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0767223226204, "neuronActivity": 0.5394809917204015}, {"timestamp": 1748383659017, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.09214298349053, "neuronActivity": 0.5420660870133228}, {"timestamp": 1748383674016, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.09214298349053, "neuronActivity": 0.5420660870133228}, {"timestamp": 1748383689017, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10756364436065, "neuronActivity": 0.5420660870133228}, {"timestamp": 1748383704018, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10756364436065, "neuronActivity": 0.5420660870133228}, {"timestamp": 1748383719018, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12301539129294, "neuronActivity": 0.5451746932281701}, {"timestamp": 1748383734019, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.12301539129294, "neuronActivity": 0.5451746932281701}, {"timestamp": 1748383749019, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13846713822522, "neuronActivity": 0.5451746932281701}, {"timestamp": 1748383764020, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13846713822522, "neuronActivity": 0.5451746932281701}, {"timestamp": 1748383779021, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.14391888515752, "neuronActivity": 0.5451746932281701}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "6:08:39 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "6:08:54 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "6:09:09 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "6:09:24 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "6:09:39 PM", "mood": "curious"}], "circadianRhythm": 0.26844933453262865, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9222007800396307, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8538667675249301, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8748160731145656, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9742847905521921, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748383480421, "expiresAt": 1748384080421}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.917178823963008, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748383480421, "expiresAt": 1748384080421}, "memory_compression_engine_1748383480422": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.902871660437116, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748383480422, "expiresAt": 1748384380422}, "memory_cache_optimizer_1748383480422": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.854056618769134, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748383480422, "expiresAt": 1748384380422}, "memory_garbage_collector_1748383480422": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.951522146654257, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748383480422, "expiresAt": 1748384380422}, "neural_stimulator_auto_10": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8896472397544939, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748383483277, "expiresAt": 1748384083277}, "emergency_memory_optimizer_19": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9841457476621928, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748383485498, "expiresAt": 1748383785498}, "emergency_memory_optimizer_20": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9554076399414905, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748383485498, "expiresAt": 1748383785498}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9222007800396307, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8538667675249301, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8748160731145656, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9742847905521921, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748383480421, "expiresAt": 1748384080421}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.917178823963008, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748383480421, "expiresAt": 1748384080421}, "memory_compression_engine_1748383480422": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.902871660437116, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748383480422, "expiresAt": 1748384380422}, "memory_cache_optimizer_1748383480422": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.854056618769134, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748383480422, "expiresAt": 1748384380422}, "memory_garbage_collector_1748383480422": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.951522146654257, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748383480422, "expiresAt": 1748384380422}, "neural_stimulator_auto_10": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.8896472397544939, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748383483277, "expiresAt": 1748384083277}, "emergency_memory_optimizer_19": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9841457476621928, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748383485498, "expiresAt": 1748383785498}, "emergency_memory_optimizer_20": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9554076399414905, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748383485498, "expiresAt": 1748383785498}}, "totalAccelerators": 11, "activeAccelerators": 11, "averageBoostFactor": 2.5502317633522726, "efficiency": 0.8530139058011363, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748383778897, "totalBoostPower": 28.052549396875, "averageEnergy": 1, "averageStability": 0.9163634807648193, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748383538916, "memoryEfficiency": 0, "thermalStability": 0.868992461098565, "cpuUsage": 0.4677490234375, "responseTime": 432.62310433317936, "overall": 0.5423221731559633}, {"timestamp": 1748383598997, "memoryEfficiency": 0, "thermalStability": 0.9880999934756094, "cpuUsage": 0.471923828125, "responseTime": 641.3760642387574, "overall": 0.5104884214767792}, {"timestamp": 1748383658998, "memoryEfficiency": 0, "thermalStability": 0.9957037659568919, "cpuUsage": 0.47724609375, "responseTime": 289.630607161136, "overall": 0.5377354164483672}, {"timestamp": 1748383718997, "memoryEfficiency": 0, "thermalStability": 0.8760433778580692, "cpuUsage": 0.55, "responseTime": 281.5449631445776, "overall": 0.5310489097926607}, {"timestamp": 1748383778997, "memoryEfficiency": 0, "thermalStability": 0.9360364799698194, "cpuUsage": 0.5708251953125, "responseTime": 605.8273812317675, "overall": 0.4914991149141932}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}