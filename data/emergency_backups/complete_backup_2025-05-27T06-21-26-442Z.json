{"timestamp": "2025-05-27T06:21:26.442Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2972119561623132, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.3669474276089995, "energy": 1, "focus": 0.8910991473845387, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748326870093, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13533061416166, "neuronActivity": 0.5191626556717162}, "emotionalHistory": [{"timestamp": 1748326609282, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326624286, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01494419041131, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326639287, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01494419041131, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326654290, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02988838082263, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326669292, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02988838082263, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326684298, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04483257123394, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326699299, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04483257123394, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326714978, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05977676164525, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326730007, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05977676164525, "neuronActivity": 0.5028697453571591}, {"timestamp": 1748326745008, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07480545909883, "neuronActivity": 0.5028697453571591}, {"timestamp": 1748326760028, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07480545909883, "neuronActivity": 0.5028697453571591}, {"timestamp": 1748326775032, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08987641007353, "neuronActivity": 0.5070950974698352}, {"timestamp": 1748326790109, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08987641007353, "neuronActivity": 0.5070950974698352}, {"timestamp": 1748326805187, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10494736104823, "neuronActivity": 0.5070950974698352}, {"timestamp": 1748326820638, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10494736104823, "neuronActivity": 0.5070950974698352}, {"timestamp": 1748326836268, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12013898760495, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748326852983, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12013898760495, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748326870093, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13533061416166, "neuronActivity": 0.5191626556717162}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:20:05 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:20:20 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:20:36 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:20:52 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:21:10 AM", "mood": "curious"}], "circadianRhythm": 0.9978687493252952, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9532953810653609, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.8934011228529545, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9169640381525055, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.809656412374911, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748326595320, "expiresAt": 1748327195320}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.8824569977130128, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748326595320, "expiresAt": 1748327195320}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.8300619170179401, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748326601653, "expiresAt": 1748327201653}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9532953810653609, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.8934011228529545, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9169640381525055, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.809656412374911, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748326595320, "expiresAt": 1748327195320}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.8824569977130128, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748326595320, "expiresAt": 1748327195320}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.8300619170179401, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748326601653, "expiresAt": 1748327201653}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.2235530520833335, "efficiency": 0.833413183125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748326834677, "totalBoostPower": 13.3413183125, "averageEnergy": 1, "averageStability": 0.8809726448627808, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748326654216, "memoryEfficiency": 0, "thermalStability": 0.9859787806868553, "cpuUsage": 0.3656982421875, "responseTime": 485.4832024631685, "overall": 0.571397015142332}, {"timestamp": 1748326714975, "memoryEfficiency": 0, "thermalStability": 0.9994588088658121, "cpuUsage": 0.389599609375, "responseTime": 321.20437274495873, "overall": 0.5719489910793034}, {"timestamp": 1748326775026, "memoryEfficiency": 0, "thermalStability": 0.9962588298651908, "cpuUsage": 0.3901611328125, "responseTime": 551.7227744929714, "overall": 0.570661287600739}, {"timestamp": 1748326835422, "memoryEfficiency": 0, "thermalStability": 0.9995544495268001, "cpuUsage": 0.3949951171875, "responseTime": 642.3265513693192, "overall": 0.5455919139854402}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}