{"timestamp": "2025-05-27T17:08:58.912Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1949604253663286, "accessFactor": 1.05, "decayFactor": 0.9751, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.356708797668612, "curiosity": 1, "confidence": -0.4782255979067367, "energy": 1, "focus": 0.9464117652953493, "creativity": 1, "stress": 0.3010986727249515, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748365722958, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03003283993158, "neuronActivity": 0.5016419965796192}, "emotionalHistory": [{"timestamp": 1748365664206, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5016419965796192}, {"timestamp": 1748365679192, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01501641996579, "neuronActivity": 0.5016419965796192}, {"timestamp": 1748365695969, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03003283993158, "neuronActivity": 0.5016419965796192}, {"timestamp": 1748365722958, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03003283993158, "neuronActivity": 0.5016419965796192}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:07:44 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:07:59 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:08:15 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:08:42 PM", "mood": "curious"}], "circadianRhythm": 0.01246599682218702, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.8875584619682166, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.9138404095669134, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9956638556510996, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.9077217488622612, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748365638185, "expiresAt": 1748366238185}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.8910074783031955, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748365638186, "expiresAt": 1748366238186}, "cpu_booster_auto_3": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.45, "stability": 0.8850861094441754, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748365664071, "expiresAt": 1748366264070}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.8646873789094615, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748365664715, "expiresAt": 1748366264715}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.8875584619682166, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.9138404095669134, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9956638556510996, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.9077217488622612, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748365638185, "expiresAt": 1748366238185}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.8910074783031955, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748365638186, "expiresAt": 1748366238186}, "cpu_booster_auto_3": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.45, "stability": 0.8850861094441754, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748365664071, "expiresAt": 1748366264070}, "thermal_cooler_auto_4": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.8646873789094615, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748365664715, "expiresAt": 1748366264715}}, "totalAccelerators": 7, "activeAccelerators": 7, "averageBoostFactor": 2.3590714285714287, "efficiency": 0.8415442857142856, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748365695830, "totalBoostPower": 16.5135, "averageEnergy": 1, "averageStability": 0.9065093489579034, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748365695833, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.9466064453125, "responseTime": 505.0880921496659, "overall": 0}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}