{"timestamp": "2025-05-27T16:00:59.013Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.143903275645748, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9223681599999999, "confidence": -4.134235299196227, "energy": 1, "focus": 0.9843743381088975, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748361640053, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15626747576832, "neuronActivity": 0.5551696183660536}, "emotionalHistory": [{"timestamp": 1748361222843, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5471506566570195}, {"timestamp": 1748361237843, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01547150656657, "neuronActivity": 0.5471506566570195}, {"timestamp": 1748361252844, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01547150656657, "neuronActivity": 0.5471506566570195}, {"timestamp": 1748361267845, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03094301313314, "neuronActivity": 0.5471506566570195}, {"timestamp": 1748361282847, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03094301313314, "neuronActivity": 0.5471506566570195}, {"timestamp": 1748361297849, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04641451969971, "neuronActivity": 0.5471506566570195}, {"timestamp": 1748361312850, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04641451969971, "neuronActivity": 0.5471506566570195}, {"timestamp": 1748361328031, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06193109623376, "neuronActivity": 0.5516576534054698}, {"timestamp": 1748361343032, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06193109623376, "neuronActivity": 0.5516576534054698}, {"timestamp": 1748361358037, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07744767276782, "neuronActivity": 0.5516576534054698}, {"timestamp": 1748361373038, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07744767276782, "neuronActivity": 0.5516576534054698}, {"timestamp": 1748361388068, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09297833380892, "neuronActivity": 0.5530661041096951}, {"timestamp": 1748361403262, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09297833380892, "neuronActivity": 0.5530661041096951}, {"timestamp": 1748361418304, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10850899485001, "neuronActivity": 0.5530661041096951}, {"timestamp": 1748361433609, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10850899485001, "neuronActivity": 0.5530661041096951}, {"timestamp": 1748361449235, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12406069103368, "neuronActivity": 0.5551696183660536}, {"timestamp": 1748361465164, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12406069103368, "neuronActivity": 0.5551696183660536}, {"timestamp": 1748361482006, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13961238721734, "neuronActivity": 0.5551696183660536}, {"timestamp": 1748361499672, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13961238721734, "neuronActivity": 0.5551696183660536}, {"timestamp": 1748361521224, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.145164083401, "neuronActivity": 0.5551696183660536}, {"timestamp": 1748361545225, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.145164083401, "neuronActivity": 0.5551696183660536}, {"timestamp": 1748361579433, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15071577958466, "neuronActivity": 0.5551696183660536}, {"timestamp": 1748361640053, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15626747576832, "neuronActivity": 0.5551696183660536}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:58:19 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:58:41 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:59:05 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:59:39 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:00:40 PM", "mood": "curious"}], "circadianRhythm": 0.06626095189432507, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.9856770730226755, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9459296420053539, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9643797279689262, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.8772971034518334, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748361207991, "expiresAt": 1748361807991}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.7887004787066219, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748361207992, "expiresAt": 1748361807992}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.9795785757289263, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748361240231, "expiresAt": 1748361840231}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.9856770730226755, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9459296420053539, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9643797279689262, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.8772971034518334, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748361207991, "expiresAt": 1748361807991}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.7887004787066219, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748361207992, "expiresAt": 1748361807992}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.9795785757289263, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748361240231, "expiresAt": 1748361840231}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.153006629505208, "efficiency": 0.8291803977703125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748361598526, "totalBoostPower": 12.918039777031249, "averageEnergy": 1, "averageStability": 0.9235937668140561, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748361267837, "memoryEfficiency": 0, "thermalStability": 0.9078472117582957, "cpuUsage": 0.4159912109375, "responseTime": 536.416147500348, "overall": 0.4868422615011052}, {"timestamp": 1748361327838, "memoryEfficiency": 0, "thermalStability": 0.9921675185362498, "cpuUsage": 0.376904296875, "responseTime": 202.62563532917437, "overall": 0.5355594734992738}, {"timestamp": 1748361387864, "memoryEfficiency": 0, "thermalStability": 0.9936123887697855, "cpuUsage": 0.3597412109375, "responseTime": 434.81031264221974, "overall": 0.5143611839481741}, {"timestamp": 1748361448255, "memoryEfficiency": 0, "thermalStability": 0.9994180937608083, "cpuUsage": 0.3987060546875, "responseTime": 215.27958710796807, "overall": 0.5148439282793333}, {"timestamp": 1748361511512, "memoryEfficiency": 0, "thermalStability": 0.995421002474096, "cpuUsage": 0.379638671875, "responseTime": 321.20901949045526, "overall": 0.5412319379007323}, {"timestamp": 1748361598526, "memoryEfficiency": 0, "thermalStability": 0.9960524645116594, "cpuUsage": 0.38193359375, "responseTime": 532.6358611153217, "overall": 0.5897775319024048}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}