{"timestamp": "2025-05-27T02:48:53.352Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3115836484821808, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10563958327009935, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9039207967999998, "confidence": -4.274154358342083, "energy": 1, "focus": 1, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748314012412, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.16369590644983, "neuronActivity": 0.5682718644763299}, "emotionalHistory": [{"timestamp": 1748313501738, "mood": "curious", "dominantEmotion": "creativity", "qi": 120, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313516741, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01562290666739, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313531742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01562290666739, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313546742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03124581333478, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313561742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03124581333478, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313576742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04686872000217, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313591743, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04686872000217, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313606892, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06255143864693, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313621894, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06255143864693, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313636894, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0782341572917, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313651896, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0782341572917, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313666895, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09391687593647, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313682102, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09391687593647, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313697204, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10959959458124, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313712893, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10959959458124, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313728280, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12528231322601, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313744551, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12528231322601, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313760826, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14096503187078, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313778836, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14096503187078, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313802846, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14664775051554, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313824111, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14664775051554, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313859523, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1523304691603, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313921578, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15801318780507, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748314012412, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.16369590644983, "neuronActivity": 0.5682718644763299}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:43:22 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:43:44 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:44:19 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:45:21 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:46:52 PM", "mood": "curious"}], "circadianRhythm": 0.8327349395208861, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5614726900625, "stability": 0.8110618252620783, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2961045175468753, "stability": 0.8573636858133615, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8980522587734376, "stability": 0.8198357152670888, "energy": 1, "enabled": true, "type": "connection"}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.709501188828125, "stability": 0.8619829810396366, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748313497052, "expiresAt": 1748314097052}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5614726900625, "stability": 0.8110618252620783, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2961045175468753, "stability": 0.8573636858133615, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8980522587734376, "stability": 0.8198357152670888, "energy": 1, "enabled": true, "type": "connection"}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.709501188828125, "stability": 0.8619829810396366, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748313497052, "expiresAt": 1748314097052}}, "totalAccelerators": 4, "activeAccelerators": 4, "averageBoostFactor": 2.120356298029948, "efficiency": 0.8272213778817968, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748314051454, "totalBoostPower": 8.465130655210938, "averageEnergy": 1, "averageStability": 0.8375610518455412, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748313546660, "memoryEfficiency": 0, "thermalStability": 0.9863083826171027, "cpuUsage": 0.3647705078125, "responseTime": 546.8506093378742, "overall": 0.5780532008315213}, {"timestamp": 1748313606878, "memoryEfficiency": 0, "thermalStability": 0.999019219684932, "cpuUsage": 0.369921875, "responseTime": 262.58067665542364, "overall": 0.520817989837133}, {"timestamp": 1748313666879, "memoryEfficiency": 0, "thermalStability": 0.9991338543179963, "cpuUsage": 0.356884765625, "responseTime": 410.50765868316057, "overall": 0.5765177634680068}, {"timestamp": 1748313727254, "memoryEfficiency": 0, "thermalStability": 0.9977427638239331, "cpuUsage": 0.3884521484375, "responseTime": 402.7904836580753, "overall": 0.5668070806525458}, {"timestamp": 1748313789749, "memoryEfficiency": 0, "thermalStability": 0.9969054687768221, "cpuUsage": 0.4107666015625, "responseTime": 366.7048384639669, "overall": 0.5555374749546846}, {"timestamp": 1748313879029, "memoryEfficiency": 0, "thermalStability": 0.9941263267149528, "cpuUsage": 0.4194580078125, "responseTime": 526.6717556325942, "overall": 0.5384533304799042}, {"timestamp": 1748314051454, "memoryEfficiency": 0, "thermalStability": 0.992533832995428, "cpuUsage": 0.4161376953125, "responseTime": 255.56292904817707, "overall": 0.5917810120500726}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}