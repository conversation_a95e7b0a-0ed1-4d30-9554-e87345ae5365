{"timestamp": "2025-05-27T21:00:03.167Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1338869711582835, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10563958327009935, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9223681599999999, "confidence": -4.134243395393906, "energy": 1, "focus": 0.9526256562118224, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748379541915, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15466052385922, "neuronActivity": 0.5486771778014407}, "emotionalHistory": [{"timestamp": 1748379114657, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.52905200327616}, {"timestamp": 1748379129656, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01529052003276, "neuronActivity": 0.52905200327616}, {"timestamp": 1748379144657, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01529052003276, "neuronActivity": 0.52905200327616}, {"timestamp": 1748379159658, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03058104006553, "neuronActivity": 0.52905200327616}, {"timestamp": 1748379174660, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03058104006553, "neuronActivity": 0.52905200327616}, {"timestamp": 1748379189660, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04587156009829, "neuronActivity": 0.52905200327616}, {"timestamp": 1748379204661, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04587156009829, "neuronActivity": 0.52905200327616}, {"timestamp": 1748379219692, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04587156009829, "neuronActivity": 0.52905200327616}, {"timestamp": 1748379234694, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06116208013106, "neuronActivity": 0.535780424237883}, {"timestamp": 1748379249696, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07651988437344, "neuronActivity": 0.535780424237883}, {"timestamp": 1748379264750, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07651988437344, "neuronActivity": 0.535780424237883}, {"timestamp": 1748379279725, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0919058576299, "neuronActivity": 0.5385973256463336}, {"timestamp": 1748379294727, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0919058576299, "neuronActivity": 0.5385973256463336}, {"timestamp": 1748379309727, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10729183088635, "neuronActivity": 0.5385973256463336}, {"timestamp": 1748379324792, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10729183088635, "neuronActivity": 0.5385973256463336}, {"timestamp": 1748379339793, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12276231118507, "neuronActivity": 0.5470480298716858}, {"timestamp": 1748379355960, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12276231118507, "neuronActivity": 0.5470480298716858}, {"timestamp": 1748379372289, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13823279148379, "neuronActivity": 0.5470480298716858}, {"timestamp": 1748379391443, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13823279148379, "neuronActivity": 0.5470480298716858}, {"timestamp": 1748379416676, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1437032717825, "neuronActivity": 0.5470480298716858}, {"timestamp": 1748379437679, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1437032717825, "neuronActivity": 0.5470480298716858}, {"timestamp": 1748379476944, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14917375208121, "neuronActivity": 0.5470480298716858}, {"timestamp": 1748379541915, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15466052385922, "neuronActivity": 0.5486771778014407}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "4:56:31 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "4:56:56 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "4:57:17 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "4:57:56 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "4:59:01 PM", "mood": "curious"}], "circadianRhythm": 0.14495633713775996, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8147583082458659, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9442907529543467, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9289817914911884, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9093765714325464, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748379101377, "expiresAt": 1748379701376}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.8688176972491577, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748379101377, "expiresAt": 1748379701377}, "memory_compression_engine_1748379101377": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.9701837812499994, "stability": 0.8570814675714835, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748379101377, "expiresAt": 1748380001377}, "memory_cache_optimizer_1748379101377": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.3821102687500004, "stability": 0.9490938126847384, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748379101377, "expiresAt": 1748380001377}, "memory_garbage_collector_1748379101377": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2350918906249997, "stability": 0.9674825195785862, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748379101377, "expiresAt": 1748380001377}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8147583082458659, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9442907529543467, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9289817914911884, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9093765714325464, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748379101377, "expiresAt": 1748379701376}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.8688176972491577, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748379101377, "expiresAt": 1748379701377}, "memory_compression_engine_1748379101377": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 2.9701837812499994, "stability": 0.8570814675714835, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748379101377, "expiresAt": 1748380001377}, "memory_cache_optimizer_1748379101377": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.3821102687500004, "stability": 0.9490938126847384, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748379101377, "expiresAt": 1748380001377}, "memory_garbage_collector_1748379101377": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2350918906249997, "stability": 0.9674825195785862, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748379101377, "expiresAt": 1748380001377}}, "totalAccelerators": 8, "activeAccelerators": 8, "averageBoostFactor": 2.3481122688085936, "efficiency": 0.8408867361285156, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748379496217, "totalBoostPower": 18.78489815046875, "averageEnergy": 1, "averageStability": 0.9049853651509892, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748379159588, "memoryEfficiency": 0, "thermalStability": 0.9721102838714918, "cpuUsage": 0.3916015625, "responseTime": 341.15355678859896, "overall": 0.5535006972488269}, {"timestamp": 1748379219690, "memoryEfficiency": 0, "thermalStability": 0.9971799763126505, "cpuUsage": 0.3630859375, "responseTime": 622.5614682432276, "overall": 0.5682565087555063}, {"timestamp": 1748379279691, "memoryEfficiency": 0, "thermalStability": 0.9985231991029448, "cpuUsage": 0.390673828125, "responseTime": 211.67687455256473, "overall": 0.5650217006808677}, {"timestamp": 1748379339695, "memoryEfficiency": 0, "thermalStability": 0.9894802694933282, "cpuUsage": 0.4498779296875, "responseTime": 253.62441942509022, "overall": 0.537840444533088}, {"timestamp": 1748379402517, "memoryEfficiency": 0, "thermalStability": 0.9894731990165181, "cpuUsage": 0.432958984375, "responseTime": 365.88059622984963, "overall": 0.5202561483531243}, {"timestamp": 1748379496217, "memoryEfficiency": 0, "thermalStability": 0.9962376091215346, "cpuUsage": 0.42158203125, "responseTime": 582.7552665627832, "overall": 0.534584392618355}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}