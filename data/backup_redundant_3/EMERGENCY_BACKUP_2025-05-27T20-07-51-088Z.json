{"timestamp": "2025-05-27T20:07:51.088Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.95, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.7, "curiosity": 0.8, "confidence": 0.6, "energy": 0.75, "focus": 0.65, "creativity": 0.7, "stress": 0.2, "fatigue": 0.3, "mood": "curious", "moodIntensity": 0.7, "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": null, "emotionalHistory": [], "currentThoughts": [], "circadianRhythm": 0.5, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9332161084064791, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.9204205672101079, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9955657289838575, "energy": 1, "enabled": true, "type": "connection"}, "memory_compression_engine_1748376471088": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748376471089, "expiresAt": 1748377371088}, "memory_cache_optimizer_1748376471089": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748376471089, "expiresAt": 1748377371089}, "memory_garbage_collector_1748376471089": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748376471089, "expiresAt": 1748377371089}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9332161084064791, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.9204205672101079, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9955657289838575, "energy": 1, "enabled": true, "type": "connection"}, "memory_compression_engine_1748376471088": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748376471089, "expiresAt": 1748377371088}, "memory_cache_optimizer_1748376471089": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748376471089, "expiresAt": 1748377371089}, "memory_garbage_collector_1748376471089": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748376471089, "expiresAt": 1748377371089}}, "totalAccelerators": 3, "activeAccelerators": 3, "averageBoostFactor": 2.5766666666666667, "efficiency": 0.8545999999999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748376469699, "totalBoostPower": 7.73, "averageEnergy": 1, "averageStability": 0.9497341348668149, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}