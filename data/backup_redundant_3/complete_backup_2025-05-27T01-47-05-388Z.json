{"timestamp": "2025-05-27T01:47:05.388Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2229057449842518, "accessFactor": 1.05, "decayFactor": 0.9895387870399305, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -0.9700240305665859, "energy": 1, "focus": 0.9773524413196387, "creativity": 1, "stress": 0.8130972878994426, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748310425325, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04538766096015, "neuronActivity": 0.5129220320044907}, "emotionalHistory": [{"timestamp": 1748310350322, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310365322, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01512922032005, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310380322, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01512922032005, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310395323, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0302584406401, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310410324, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0302584406401, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310425325, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04538766096015, "neuronActivity": 0.5129220320044907}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:46:05 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:46:20 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:46:35 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:46:50 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:47:05 PM", "mood": "curious"}], "circadianRhythm": 0.7252221044235875, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.9072424615686546, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.9175311482597577, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9302128892591389, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.8774451622214094, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310336413, "expiresAt": 1748310936413}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.9196545937957542, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310336414, "expiresAt": 1748310936414}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.933818808384612, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310372415, "expiresAt": 1748310972415}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.9072424615686546, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.9175311482597577, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9302128892591389, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.8774451622214094, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310336413, "expiresAt": 1748310936413}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.9196545937957542, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310336414, "expiresAt": 1748310936414}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.933818808384612, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310372415, "expiresAt": 1748310972415}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.3439166666666664, "efficiency": 0.8406349999999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748310395258, "totalBoostPower": 14.0635, "averageEnergy": 1, "averageStability": 0.9143175105815544, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748310395271, "memoryEfficiency": 0, "thermalStability": 0.9842931462658776, "cpuUsage": 0.390966796875, "responseTime": 502.44735025723844, "overall": 0.5197154620968735}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}