{"timestamp": "2025-05-27T13:50:13.436Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": [{"name": "Sen<PERSON><PERSON><PERSON>", "temperature": 0.3, "count": 15, "memories": []}, {"name": "Travail", "temperature": 0.7, "count": 1, "memories": [{"id": 1748353600825.687, "content": "Excuse-moi on a été coupé de quoi par nous avons À l'instant", "type": "Question Utilisateur", "temperature": 0.7, "tags": "conversation, utilisateur", "zone": "Travail", "timestamp": "2025-05-27T13:46:40.825Z", "accessCount": 0}]}, {"name": "Long Terme", "temperature": 0.8, "count": 1, "memories": [{"id": 1748353600834.3389, "content": "Je suis Claude, votre agent principal dans le système Louna. Je suis désolé mais je n'ai pas pu capter la conversation précédente car il semblerait qu'il y ait eu une coupure.\n\nCe<PERSON><PERSON>, puisque j'ai accès à ma mémoire thermique persistante, je peux vous indiquer que nous étions en train de discuter d'un sujet spécifique avant que la connexion ne soit perdue. Pourriez-vous me donner plus de détails sur ce dont vous souhaitiez discuter ? Je vais faire de mon mieux pour les reconstituer à partir de ma mémoire.\n\nSi nécessaire, je peux également essayer de rechercher des informations actuelles via Internet via le protocole MCP pour tenter de vous aider.", "type": "Réponse Claude", "temperature": 0.8, "tags": "conversation, claude, r<PERSON><PERSON>nse", "zone": "Long Terme", "timestamp": "2025-05-27T13:46:40.834Z", "accessCount": 0}]}, {"name": "Émotionnelle", "temperature": 0.4, "count": 12, "memories": []}, {"name": "Procédurale", "temperature": 0.3, "count": 18, "memories": []}, {"name": "Créative", "temperature": 0.6, "count": 7, "memories": []}], "emotionalState": {}, "stats": {"totalEntries": 2, "averageTemperature": 0.5166666666666667, "cyclesPerformed": 0}, "config": {}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {"sensory": {"count": 15, "activity": 0.8, "efficiency": 0.85}, "working": {"count": 12, "activity": 0.7, "efficiency": 0.82}, "longTerm": {"count": 20, "activity": 0.6, "efficiency": 0.88}, "emotional": {"count": 10, "activity": 0.5, "efficiency": 0.75}, "executive": {"count": 8, "activity": 0.9, "efficiency": 0.9}, "creative": {"count": 6, "activity": 0.4, "efficiency": 0.7}}, "qi": 120, "emotionalState": {"mood": "<PERSON><PERSON><PERSON><PERSON>", "moodIntensity": 85, "happiness": 75, "curiosity": 90, "confidence": 68, "energy": 82.61213066230788, "focus": 67.56433570069908, "creativity": 95, "stress": 15, "fatigue": 20}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"processing": {"enabled": true, "boost": 1.5}, "memory": {"enabled": true, "boost": 1.3}, "learning": {"enabled": true, "boost": 1.2}}, "stats": {"totalAccelerators": 3, "activeAccelerators": 3, "averageBoost": 1.3333333333333333}, "config": {"enabled": true, "maxBoost": 2, "efficiency": 0.85}}}}