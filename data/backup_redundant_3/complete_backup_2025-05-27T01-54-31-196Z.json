{"timestamp": "2025-05-27T01:54:31.196Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.12433750664874, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.025763392308854, "energy": 1, "focus": 0.929790378346311, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748310870960, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.121115317807, "neuronActivity": 0.5214749918196996}, "emotionalHistory": [{"timestamp": 1748310644732, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310659737, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01508678871139, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310674741, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01508678871139, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310689747, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03017357742277, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310704749, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03017357742277, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310719749, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04526036613416, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310734750, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04526036613416, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310749750, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0604079706144, "neuronActivity": 0.5147604480251458}, {"timestamp": 1748310764781, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0604079706144, "neuronActivity": 0.5147604480251458}, {"timestamp": 1748310779782, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07555557509465, "neuronActivity": 0.5147604480251458}, {"timestamp": 1748310794783, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07555557509465, "neuronActivity": 0.5147604480251458}, {"timestamp": 1748310809828, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09072807149172, "neuronActivity": 0.5172496397070235}, {"timestamp": 1748310824831, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09072807149172, "neuronActivity": 0.5172496397070235}, {"timestamp": 1748310839924, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1059005678888, "neuronActivity": 0.5172496397070235}, {"timestamp": 1748310855385, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1059005678888, "neuronActivity": 0.5172496397070235}, {"timestamp": 1748310870960, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.121115317807, "neuronActivity": 0.5214749918196996}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:53:29 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:53:44 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:53:59 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:54:15 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:54:30 PM", "mood": "curious"}], "circadianRhythm": 0.7395680918612243, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.8832496545857301, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.8302953821451522, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9626884821630305, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.9124779298218454, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310630786, "expiresAt": 1748311230786}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.8941929868692986, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310630786, "expiresAt": 1748311230786}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.8424459073939609, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310661238, "expiresAt": 1748311261238}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.8832496545857301, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.8302953821451522, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9626884821630305, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.9124779298218454, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310630786, "expiresAt": 1748311230786}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.8941929868692986, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310630786, "expiresAt": 1748311230786}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.8424459073939609, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310661238, "expiresAt": 1748311261238}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.2235530520833335, "efficiency": 0.833413183125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748310869874, "totalBoostPower": 13.3413183125, "averageEnergy": 1, "averageStability": 0.8875583904965029, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748310689697, "memoryEfficiency": 0, "thermalStability": 0.9841671082708571, "cpuUsage": 0.3866455078125, "responseTime": 238.05911952366193, "overall": 0.5716442288751827}, {"timestamp": 1748310749699, "memoryEfficiency": 0, "thermalStability": 0.9978711352166202, "cpuUsage": 0.3678466796875, "responseTime": 476.36394624108607, "overall": 0.5725478059791155}, {"timestamp": 1748310809702, "memoryEfficiency": 0, "thermalStability": 0.9984113683303197, "cpuUsage": 0.3593994140625, "responseTime": 695.1915067370909, "overall": 0.5826752848482191}, {"timestamp": 1748310869874, "memoryEfficiency": 0, "thermalStability": 0.9975847550564342, "cpuUsage": 0.3860107421875, "responseTime": 645.6663978391132, "overall": 0.5568003750118165}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}