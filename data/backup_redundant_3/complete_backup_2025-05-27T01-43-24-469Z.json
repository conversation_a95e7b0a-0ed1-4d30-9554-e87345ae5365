{"timestamp": "2025-05-27T01:43:24.469Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2795221093293323, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -1.4327460866353543, "energy": 1, "focus": 0.9917792834875742, "creativity": 1, "stress": 0.8982810337380578, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748310194398, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05894629806376, "neuronActivity": 0.47999547976274926}, "emotionalHistory": [{"timestamp": 1748310089396, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310104396, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01471544775538, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310119396, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01471544775538, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310134396, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02943089551076, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310149398, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02943089551076, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310164398, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04414634326614, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310179399, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04414634326614, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310194398, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05894629806376, "neuronActivity": 0.47999547976274926}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:42:14 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:42:29 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:42:44 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:42:59 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:43:14 PM", "mood": "curious"}], "circadianRhythm": 0.7176940485381771, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.8901235623698067, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.8651522946919, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.9766393601147235, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.8591523326299864, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310075477, "expiresAt": 1748310675477}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.8697254286336732, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310075477, "expiresAt": 1748310675477}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.8697509335980775, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310079012, "expiresAt": 1748310679012}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.8901235623698067, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.8651522946919, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.9766393601147235, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.8591523326299864, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310075477, "expiresAt": 1748310675477}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.8697254286336732, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310075477, "expiresAt": 1748310675477}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.8697509335980775, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310079012, "expiresAt": 1748310679012}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.3017208333333334, "efficiency": 0.83810325, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748310194239, "totalBoostPower": 13.810325, "averageEnergy": 1, "averageStability": 0.8884239853396946, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748310134274, "memoryEfficiency": 0, "thermalStability": 0.984767265452279, "cpuUsage": 0.431689453125, "responseTime": 371.4483680287887, "overall": 0.5061349890516287}, {"timestamp": 1748310194279, "memoryEfficiency": 0, "thermalStability": 0.9758377286959414, "cpuUsage": 0.4224609375, "responseTime": 230.6903938364016, "overall": 0.5644783239933652}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}