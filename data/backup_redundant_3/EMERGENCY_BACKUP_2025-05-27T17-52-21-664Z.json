{"timestamp": "2025-05-27T17:52:21.664Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1164198409954547, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9223681599999999, "confidence": -4.1342505598446, "energy": 1, "focus": 0.8811773168699656, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748368302413, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15112421457432, "neuronActivity": 0.5270271448547543}, "emotionalHistory": [{"timestamp": 1748367867363, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.4952980120975915}, {"timestamp": 1748367882363, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01495298012098, "neuronActivity": 0.4952980120975915}, {"timestamp": 1748367897363, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01495298012098, "neuronActivity": 0.4952980120975915}, {"timestamp": 1748367912365, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02990596024196, "neuronActivity": 0.4952980120975915}, {"timestamp": 1748367927366, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02990596024196, "neuronActivity": 0.4952980120975915}, {"timestamp": 1748367942366, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04485894036294, "neuronActivity": 0.4952980120975915}, {"timestamp": 1748367957367, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04485894036294, "neuronActivity": 0.4952980120975915}, {"timestamp": 1748367972367, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05989642752617, "neuronActivity": 0.5037487163229437}, {"timestamp": 1748367987369, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05989642752617, "neuronActivity": 0.5037487163229437}, {"timestamp": 1748368002368, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0749339146894, "neuronActivity": 0.5037487163229437}, {"timestamp": 1748368017371, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0749339146894, "neuronActivity": 0.5037487163229437}, {"timestamp": 1748368032441, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09001365537375, "neuronActivity": 0.5079740684356198}, {"timestamp": 1748368047740, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09001365537375, "neuronActivity": 0.5079740684356198}, {"timestamp": 1748368062854, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10509339605811, "neuronActivity": 0.5079740684356198}, {"timestamp": 1748368078348, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10509339605811, "neuronActivity": 0.5079740684356198}, {"timestamp": 1748368094067, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12028353282503, "neuronActivity": 0.5190136766916557}, {"timestamp": 1748368110113, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12028353282503, "neuronActivity": 0.5190136766916557}, {"timestamp": 1748368126927, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13547366959195, "neuronActivity": 0.5190136766916557}, {"timestamp": 1748368146422, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13547366959195, "neuronActivity": 0.5190136766916557}, {"timestamp": 1748368171506, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14066380635886, "neuronActivity": 0.5190136766916557}, {"timestamp": 1748368196948, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14066380635886, "neuronActivity": 0.5190136766916557}, {"timestamp": 1748368237405, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14585394312577, "neuronActivity": 0.5190136766916557}, {"timestamp": 1748368302413, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15112421457432, "neuronActivity": 0.5270271448547543}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:49:06 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:49:31 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:49:56 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:50:37 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:51:42 PM", "mood": "curious"}], "circadianRhythm": 0.0003273127494306527, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.9932733895589336, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 1, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.8760972459161549, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9269713257044531, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748367853559, "expiresAt": 1748368453559}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.9546530707976046, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748367853560, "expiresAt": 1748368453560}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.9207463391176707, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748367861979, "expiresAt": 1748368461979}, "ltx_videoGeneration": {"name": "LTX Video Accelerator", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 0.8414428363851856, "energy": 1, "enabled": true, "type": "video_generation", "createdAt": 1748367885774, "expiresAt": null}, "ltx_audioProcessing": {"name": "LTX Audio Accelerator", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 0.8430235745891335, "energy": 1, "enabled": true, "type": "audio_processing", "createdAt": 1748367885774, "expiresAt": null}, "ltx_imageGeneration": {"name": "LTX Image Accelerator", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 1, "energy": 1, "enabled": true, "type": "image_generation", "createdAt": 1748367885774, "expiresAt": null}, "ltx_model3D": {"name": "LTX 3D Accelerator", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 1, "energy": 1, "enabled": true, "type": "3d_generation", "createdAt": 1748367885774, "expiresAt": null}, "kyber_optimization": {"name": "Kyber Ultra Optimizer", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 0.9574069772083926, "energy": 1, "enabled": true, "type": "system_optimization", "createdAt": 1748367885774, "expiresAt": null}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.9932733895589336, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 1, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.8760972459161549, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9269713257044531, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748367853559, "expiresAt": 1748368453559}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.9546530707976046, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748367853560, "expiresAt": 1748368453560}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.9207463391176707, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748367861979, "expiresAt": 1748368461979}, "ltx_videoGeneration": {"name": "LTX Video Accelerator", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 0.8414428363851856, "energy": 1, "enabled": true, "type": "video_generation", "createdAt": 1748367885774, "expiresAt": null}, "ltx_audioProcessing": {"name": "LTX Audio Accelerator", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 0.8430235745891335, "energy": 1, "enabled": true, "type": "audio_processing", "createdAt": 1748367885774, "expiresAt": null}, "ltx_imageGeneration": {"name": "LTX Image Accelerator", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 1, "energy": 1, "enabled": true, "type": "image_generation", "createdAt": 1748367885774, "expiresAt": null}, "ltx_model3D": {"name": "LTX 3D Accelerator", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 1, "energy": 1, "enabled": true, "type": "3d_generation", "createdAt": 1748367885774, "expiresAt": null}, "kyber_optimization": {"name": "Kyber Ultra Optimizer", "description": "Accélérateur automatique", "boostFactor": 1.5, "stability": 0.9574069772083926, "energy": 1, "enabled": true, "type": "system_optimization", "createdAt": 1748367885774, "expiresAt": null}}, "totalAccelerators": 11, "activeAccelerators": 11, "averageBoostFactor": 1.856185434275568, "efficiency": 0.811371126056534, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748368254603, "totalBoostPower": 20.418039777031247, "averageEnergy": 1, "averageStability": 0.9376013417525025, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748367912311, "memoryEfficiency": 0, "thermalStability": 0.9268438370691405, "cpuUsage": 0.4360595703125, "responseTime": 201.23997148014865, "overall": 0.5317002173558383}, {"timestamp": 1748367972315, "memoryEfficiency": 0, "thermalStability": 0.9856907948851585, "cpuUsage": 0.4052001953125, "responseTime": 446.39640569953394, "overall": 0.5310432195568974}, {"timestamp": 1748368032321, "memoryEfficiency": 0, "thermalStability": 0.9954431002752648, "cpuUsage": 0.3687255859375, "responseTime": 644.7961331326834, "overall": 0.563140081007047}, {"timestamp": 1748368093213, "memoryEfficiency": 0, "thermalStability": 0.998705892968509, "cpuUsage": 0.393017578125, "responseTime": 660.999259946087, "overall": 0.539962472481261}, {"timestamp": 1748368156008, "memoryEfficiency": 0, "thermalStability": 0.9981742702010605, "cpuUsage": 0.40390625, "responseTime": 637.9825481236799, "overall": 0.5286457040803392}, {"timestamp": 1748368254603, "memoryEfficiency": 0, "thermalStability": 0.9959889331211647, "cpuUsage": 0.413427734375, "responseTime": 611.9181923681219, "overall": 0.5324035564498844}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}