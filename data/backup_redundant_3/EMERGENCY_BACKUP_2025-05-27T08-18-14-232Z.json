{"timestamp": "2025-05-27T08:18:14.232Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3028532548124059, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9223681599999999, "confidence": -4.134251678667426, "energy": 1, "focus": 0.8440555538313997, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748333828983, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15046948405596, "neuronActivity": 0.5128645664236853}, "emotionalHistory": [{"timestamp": 1748333428328, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.49462699274853444}, {"timestamp": 1748333443327, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01494626992749, "neuronActivity": 0.49462699274853444}, {"timestamp": 1748333458328, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01494626992749, "neuronActivity": 0.49462699274853444}, {"timestamp": 1748333473331, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02989253985498, "neuronActivity": 0.49462699274853444}, {"timestamp": 1748333488332, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02989253985498, "neuronActivity": 0.49462699274853444}, {"timestamp": 1748333503337, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04483880978248, "neuronActivity": 0.49462699274853444}, {"timestamp": 1748333518337, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04483880978248, "neuronActivity": 0.49462699274853444}, {"timestamp": 1748333533338, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05984175501803, "neuronActivity": 0.5002945235549069}, {"timestamp": 1748333548339, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05984175501803, "neuronActivity": 0.5002945235549069}, {"timestamp": 1748333563370, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07484470025358, "neuronActivity": 0.5002945235549069}, {"timestamp": 1748333578371, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07484470025358, "neuronActivity": 0.5002945235549069}, {"timestamp": 1748333593372, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08987581450322, "neuronActivity": 0.5031114249633576}, {"timestamp": 1748333608578, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08987581450322, "neuronActivity": 0.5031114249633576}, {"timestamp": 1748333623579, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10490692875285, "neuronActivity": 0.5031114249633576}, {"timestamp": 1748333638838, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10490692875285, "neuronActivity": 0.5031114249633576}, {"timestamp": 1748333655143, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12001540616257, "neuronActivity": 0.5108477409713996}, {"timestamp": 1748333670997, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12001540616257, "neuronActivity": 0.5108477409713996}, {"timestamp": 1748333686786, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13512388357228, "neuronActivity": 0.5108477409713996}, {"timestamp": 1748333703898, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13512388357228, "neuronActivity": 0.5108477409713996}, {"timestamp": 1748333722163, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.140232360982, "neuronActivity": 0.5108477409713996}, {"timestamp": 1748333744242, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.140232360982, "neuronActivity": 0.5108477409713996}, {"timestamp": 1748333778950, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14534083839172, "neuronActivity": 0.5108477409713996}, {"timestamp": 1748333828983, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15046948405596, "neuronActivity": 0.5128645664236853}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "4:15:03 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "4:15:22 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "4:15:44 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "4:16:18 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "4:17:08 AM", "mood": "curious"}], "circadianRhythm": 0.9131109567984677, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8694747192516961, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9740563077799678, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.8191468974769663, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9378494305734375, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748333414632, "expiresAt": 1748334014632}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.9109269609693273, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748333414632, "expiresAt": 1748334014632}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.852298228648422, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748333445275, "expiresAt": 1748334045275}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8694747192516961, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9740563077799678, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.8191468974769663, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9378494305734375, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748333414632, "expiresAt": 1748334014632}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.9109269609693273, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748333414632, "expiresAt": 1748334014632}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.852298228648422, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748333445275, "expiresAt": 1748334045275}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.153006629505208, "efficiency": 0.8291803977703125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748333803844, "totalBoostPower": 12.918039777031249, "averageEnergy": 1, "averageStability": 0.8939587574499694, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748333473233, "memoryEfficiency": 0, "thermalStability": 0.9778394697440995, "cpuUsage": 0.4033447265625, "responseTime": 593.5805958610031, "overall": 0.5886399035669201}, {"timestamp": 1748333533310, "memoryEfficiency": 0, "thermalStability": 0.9976095582048098, "cpuUsage": 0.370556640625, "responseTime": 583.7008685142007, "overall": 0.5286372639594079}, {"timestamp": 1748333593312, "memoryEfficiency": 0, "thermalStability": 0.9991322731392251, "cpuUsage": 0.387451171875, "responseTime": 352.9090246905148, "overall": 0.5812673465014389}, {"timestamp": 1748333653636, "memoryEfficiency": 0, "thermalStability": 0.9991501463784112, "cpuUsage": 0.3958984375, "responseTime": 441.8986362086921, "overall": 0.5275968394058834}, {"timestamp": 1748333717614, "memoryEfficiency": 0, "thermalStability": 0.9982731857233578, "cpuUsage": 0.38837890625, "responseTime": 446.3007306878848, "overall": 0.5355560138649311}, {"timestamp": 1748333803844, "memoryEfficiency": 0, "thermalStability": 0.9990796919084257, "cpuUsage": 0.4161865234375, "responseTime": 439.70324408750133, "overall": 0.583201989964794}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}