{"timestamp": "2025-05-27T07:32:15.273Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2521863777046276, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10459105791450644, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.529900802322342, "energy": 1, "focus": 0.8572712392966576, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748331121868, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13415699272936, "neuronActivity": 0.4963103597452576}, "emotionalHistory": [{"timestamp": 1748330840840, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.4858518081089027}, {"timestamp": 1748330855841, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01485851808108, "neuronActivity": 0.4858518081089027}, {"timestamp": 1748330870842, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01485851808108, "neuronActivity": 0.4858518081089027}, {"timestamp": 1748330885842, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02971703616217, "neuronActivity": 0.4858518081089027}, {"timestamp": 1748330900843, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02971703616217, "neuronActivity": 0.4858518081089027}, {"timestamp": 1748330915843, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04457555424325, "neuronActivity": 0.4858518081089027}, {"timestamp": 1748330930844, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04457555424325, "neuronActivity": 0.4858518081089027}, {"timestamp": 1748330945858, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05948231981253, "neuronActivity": 0.49067655692835616}, {"timestamp": 1748330960861, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05948231981253, "neuronActivity": 0.49067655692835616}, {"timestamp": 1748330975860, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07438908538181, "neuronActivity": 0.49067655692835616}, {"timestamp": 1748330990897, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07438908538181, "neuronActivity": 0.49067655692835616}, {"timestamp": 1748331005950, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08930993545813, "neuronActivity": 0.4920850076325816}, {"timestamp": 1748331021035, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08930993545813, "neuronActivity": 0.4920850076325816}, {"timestamp": 1748331036147, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10423078553445, "neuronActivity": 0.4920850076325816}, {"timestamp": 1748331051511, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10423078553445, "neuronActivity": 0.4920850076325816}, {"timestamp": 1748331067181, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1191938891319, "neuronActivity": 0.4963103597452576}, {"timestamp": 1748331084684, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1191938891319, "neuronActivity": 0.4963103597452576}, {"timestamp": 1748331101342, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13415699272936, "neuronActivity": 0.4963103597452576}, {"timestamp": 1748331121868, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13415699272936, "neuronActivity": 0.4963103597452576}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "3:30:51 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "3:31:07 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "3:31:24 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "3:31:41 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "3:32:01 AM", "mood": "curious"}], "circadianRhythm": 0.9602258830848753, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9855140115867873, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.865551918129816, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.888401770901098, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748330827487, "expiresAt": 1748331427487}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9096550644046788, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748330827487, "expiresAt": 1748331427487}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9277000007121355, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748330834199, "expiresAt": 1748331434199}, "cpu_booster_auto_4": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.357375, "stability": 0.9097869743958342, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748330982656, "expiresAt": 1748331582655}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9855140115867873, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.865551918129816, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.888401770901098, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748330827487, "expiresAt": 1748331427487}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9096550644046788, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748330827487, "expiresAt": 1748331427487}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.9277000007121355, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748330834199, "expiresAt": 1748331434199}, "cpu_booster_auto_4": {"name": "CPU Booster", "description": "Accélère le traitement CPU", "boostFactor": 2.357375, "stability": 0.9097869743958342, "energy": 1, "enabled": true, "type": "cpu_booster", "createdAt": 1748330982656, "expiresAt": 1748331582655}}, "totalAccelerators": 7, "activeAccelerators": 7, "averageBoostFactor": 2.2116610566964283, "efficiency": 0.8326996634017857, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748331130611, "totalBoostPower": 15.481627396874998, "averageEnergy": 1, "averageStability": 0.9266585343043356, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748330885764, "memoryEfficiency": 0, "thermalStability": 0.8590391483571794, "cpuUsage": 0.488916015625, "responseTime": 426.9129334821886, "overall": 0.480650427211606}, {"timestamp": 1748330945829, "memoryEfficiency": 0, "thermalStability": 0.9693894727362526, "cpuUsage": 0.5723876953125, "responseTime": 390.9963361783483, "overall": 0.547092683392425}, {"timestamp": 1748331005846, "memoryEfficiency": 0, "thermalStability": 0.7741340686877569, "cpuUsage": 0.8185791015625, "responseTime": 669.7897742778343, "overall": 0.4852178413072258}, {"timestamp": 1748331066248, "memoryEfficiency": 0, "thermalStability": 0.6998408951693111, "cpuUsage": 1.1524658203125, "responseTime": 287.97125874216124, "overall": 0.43008539999668044}, {"timestamp": 1748331130611, "memoryEfficiency": 0, "thermalStability": 0.7713738956799111, "cpuUsage": 1.0042724609375, "responseTime": 451.40735062227225, "overall": 0.472634254509504}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}