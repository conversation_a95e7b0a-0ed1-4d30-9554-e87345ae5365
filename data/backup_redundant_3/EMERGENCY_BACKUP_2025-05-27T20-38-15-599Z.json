{"timestamp": "2025-05-27T20:38:15.599Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.95, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.5500394019704249, "curiosity": 1, "confidence": 0.31799999999999995, "energy": 0.894179472137557, "focus": 0.7363473121346521, "creativity": 0.8744197666463887, "stress": 0.2397789473684211, "fatigue": 0.20292571075831384, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748378285438, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5293892485386085}, "emotionalHistory": [{"timestamp": 1748378285438, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5293892485386085}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "4:38:05 PM", "mood": "curious"}], "circadianRhythm": 0.11431348787987317, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9371241058363114, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.8436881051983388, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9418859122534727, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748378271723, "expiresAt": 1748378871723}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748378271724, "expiresAt": 1748378871724}, "memory_compression_engine_1748378271724": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748378271724, "expiresAt": 1748379171724}, "memory_cache_optimizer_1748378271724": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748378271724, "expiresAt": 1748379171724}, "memory_garbage_collector_1748378271724": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748378271724, "expiresAt": 1748379171724}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9371241058363114, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.8436881051983388, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9418859122534727, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748378271723, "expiresAt": 1748378871723}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748378271724, "expiresAt": 1748378871724}, "memory_compression_engine_1748378271724": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748378271724, "expiresAt": 1748379171724}, "memory_cache_optimizer_1748378271724": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748378271724, "expiresAt": 1748379171724}, "memory_garbage_collector_1748378271724": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748378271724, "expiresAt": 1748379171724}}, "totalAccelerators": 8, "activeAccelerators": 8, "averageBoostFactor": 2.5766666666666667, "efficiency": 0.8545999999999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748378270376, "totalBoostPower": 21.23, "averageEnergy": 625.375, "averageStability": 0.9028372654110155, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}