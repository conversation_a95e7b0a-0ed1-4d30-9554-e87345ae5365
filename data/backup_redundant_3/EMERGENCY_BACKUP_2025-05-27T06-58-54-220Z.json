{"timestamp": "2025-05-27T06:58:54.220Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1915672158209951, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10563958327009935, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.8858423808639998, "confidence": -4.409911357197172, "energy": 1, "focus": 0.8010558736981366, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748328946907, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1536007107624, "neuronActivity": 0.4962800555987611}, "emotionalHistory": [{"timestamp": 1748328360953, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328375954, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01477760516273, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328390955, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01477760516273, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328405955, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02955521032547, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328420940, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02955521032547, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328435934, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0443328154882, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328450933, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0443328154882, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328465940, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05919492769318, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328480941, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05919492769318, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328495941, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07405703989816, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328510976, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07405703989816, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328525977, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08894814991687, "neuronActivity": 0.4891110018709943}, {"timestamp": 1748328540978, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08894814991687, "neuronActivity": 0.4891110018709943}, {"timestamp": 1748328556088, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10383925993558, "neuronActivity": 0.4891110018709943}, {"timestamp": 1748328571418, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10383925993558, "neuronActivity": 0.4891110018709943}, {"timestamp": 1748328586721, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11878670798247, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328602567, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11878670798247, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328618913, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13374950853846, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328637521, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13374950853846, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328656860, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13871230909444, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328676960, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13871230909444, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328708442, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14367510965043, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328752617, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14863791020642, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328811647, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1536007107624, "neuronActivity": 0.4962800555987611}, {"timestamp": 1748328946907, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1536007107624, "neuronActivity": 0.4962800555987611}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:51:16 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:51:48 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:52:32 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:53:31 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:55:46 AM", "mood": "curious"}], "circadianRhythm": 0.9852628161514474, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5614726900625, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2961045175468753, "stability": 0.8849981983202817, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8980522587734376, "stability": 0.9858153004768692, "energy": 1, "enabled": true, "type": "connection"}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5614726900625, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2961045175468753, "stability": 0.8849981983202817, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8980522587734376, "stability": 0.9858153004768692, "energy": 1, "enabled": true, "type": "connection"}}, "totalAccelerators": 3, "activeAccelerators": 3, "averageBoostFactor": 2.120356298029948, "efficiency": 0.8272213778817968, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748328911823, "totalBoostPower": 6.755629466382812, "averageEnergy": 1, "averageStability": 0.9569378329323835, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748328405908, "memoryEfficiency": 0, "thermalStability": 0.9780542161729601, "cpuUsage": 0.368798828125, "responseTime": 622.6888867218463, "overall": 0.5625536985315469}, {"timestamp": 1748328465894, "memoryEfficiency": 0, "thermalStability": 0.9964157682326105, "cpuUsage": 0.3524658203125, "responseTime": 459.1281323943553, "overall": 0.5647945918838175}, {"timestamp": 1748328525896, "memoryEfficiency": 0, "thermalStability": 0.998937514755461, "cpuUsage": 0.352734375, "responseTime": 447.8605608383694, "overall": 0.522407867378654}, {"timestamp": 1748328586138, "memoryEfficiency": 0, "thermalStability": 0.9986240196973085, "cpuUsage": 0.3766357421875, "responseTime": 649.3255474655091, "overall": 0.5908523802431438}, {"timestamp": 1748328648329, "memoryEfficiency": 0, "thermalStability": 0.9981331386913855, "cpuUsage": 0.398486328125, "responseTime": 452.9547496807169, "overall": 0.5097494579642372}, {"timestamp": 1748328731208, "memoryEfficiency": 0, "thermalStability": 0.9924405405918757, "cpuUsage": 0.3831298828125, "responseTime": 594.0073571393643, "overall": 0.5800300100693057}, {"timestamp": 1748328911823, "memoryEfficiency": 0, "thermalStability": 0.9934814529286491, "cpuUsage": 0.4260498046875, "responseTime": 297.7331730258472, "overall": 0.5514403273629437}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}