{"timestamp": "2025-05-27T16:40:25.170Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.016389331389373, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10459105791450644, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.5298909772368576, "energy": 1, "focus": 0.9476819960490445, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748364009558, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13803287682336, "neuronActivity": 0.5376514086633721}, "emotionalHistory": [{"timestamp": 1748363729477, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363744478, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01527798966126, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363759478, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01527798966126, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363774478, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03055597932251, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363789479, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03055597932251, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363804480, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04583396898377, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363819482, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04583396898377, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363834482, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06118839664683, "neuronActivity": 0.5354427663054926}, {"timestamp": 1748363849487, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06118839664683, "neuronActivity": 0.5354427663054926}, {"timestamp": 1748363864487, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07654282430988, "neuronActivity": 0.5354427663054926}, {"timestamp": 1748363879513, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07654282430988, "neuronActivity": 0.5354427663054926}, {"timestamp": 1748363894513, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09191133647998, "neuronActivity": 0.536851217009718}, {"timestamp": 1748363909699, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09191133647998, "neuronActivity": 0.536851217009718}, {"timestamp": 1748363924979, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10727984865008, "neuronActivity": 0.536851217009718}, {"timestamp": 1748363940563, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10727984865008, "neuronActivity": 0.536851217009718}, {"timestamp": 1748363956096, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12265636273672, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748363973239, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12265636273672, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748363989980, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13803287682336, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748364009558, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13803287682336, "neuronActivity": 0.5376514086633721}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:39:00 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "12:39:16 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:39:33 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:39:49 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "12:40:09 PM", "mood": "curious"}], "circadianRhythm": 0.03003493788399436, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8543334530482264, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7701515352207159, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9118760628680626, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 1, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748363715258, "expiresAt": 1748364315258}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9046336043632868, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748363715258, "expiresAt": 1748364315258}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.892336477004741, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748363724676, "expiresAt": 1748364324676}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8543334530482264, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7701515352207159, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9118760628680626, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 1, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748363715258, "expiresAt": 1748364315258}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9046336043632868, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748363715258, "expiresAt": 1748364315258}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.892336477004741, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748363724676, "expiresAt": 1748364324676}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.1873753994791665, "efficiency": 0.83124252396875, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748364019272, "totalBoostPower": 13.124252396874999, "averageEnergy": 1, "averageStability": 0.888888522084172, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748363774448, "memoryEfficiency": 0, "thermalStability": 0.8073397926158375, "cpuUsage": 0.5108642578125, "responseTime": 581.7920139423991, "overall": 0.508675084345393}, {"timestamp": 1748363834452, "memoryEfficiency": 0, "thermalStability": 0.950465110813578, "cpuUsage": 0.4595458984375, "responseTime": 295.7714364415271, "overall": 0.5173028909756264}, {"timestamp": 1748363894469, "memoryEfficiency": 0, "thermalStability": 0.9870677244746022, "cpuUsage": 0.5223876953125, "responseTime": 670.2612940549245, "overall": 0.5330917338949828}, {"timestamp": 1748363954932, "memoryEfficiency": 0, "thermalStability": 0.9856216669082641, "cpuUsage": 0.508642578125, "responseTime": 382.33882904910683, "overall": 0.5162556029378876}, {"timestamp": 1748364019272, "memoryEfficiency": 0, "thermalStability": 0.9959601735489236, "cpuUsage": 0.531591796875, "responseTime": 648.7434531022824, "overall": 0.5953866444937437}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}