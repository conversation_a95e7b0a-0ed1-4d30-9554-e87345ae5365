{"timestamp": "2025-05-27T01:49:36.532Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.223177168835468, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.025762687507643, "energy": 1, "focus": 0.9180166422405541, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748310575900, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12146998761746, "neuronActivity": 0.52757538013768}, "emotionalHistory": [{"timestamp": 1748310350322, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310365322, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01512922032005, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310380322, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01512922032005, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310395323, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0302584406401, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310410324, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0302584406401, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310425325, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04538766096015, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310440327, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04538766096015, "neuronActivity": 0.5129220320044907}, {"timestamp": 1748310455327, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06057521966709, "neuronActivity": 0.518755870694129}, {"timestamp": 1748310470328, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06057521966709, "neuronActivity": 0.518755870694129}, {"timestamp": 1748310485328, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07576277837403, "neuronActivity": 0.518755870694129}, {"timestamp": 1748310500349, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07576277837403, "neuronActivity": 0.518755870694129}, {"timestamp": 1748310515350, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09097850609506, "neuronActivity": 0.5215727721025796}, {"timestamp": 1748310530404, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09097850609506, "neuronActivity": 0.5215727721025796}, {"timestamp": 1748310545489, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10619423381608, "neuronActivity": 0.5215727721025796}, {"timestamp": 1748310560636, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10619423381608, "neuronActivity": 0.5215727721025796}, {"timestamp": 1748310575900, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12146998761746, "neuronActivity": 0.52757538013768}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:48:35 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:48:50 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:49:05 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:49:20 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "9:49:35 PM", "mood": "curious"}], "circadianRhythm": 0.7300966641980038, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.8693036366404555, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.8931354760728689, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.8876673242015027, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.9348225579636307, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310336413, "expiresAt": 1748310936413}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.95695943717205, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310336414, "expiresAt": 1748310936414}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 1, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310372415, "expiresAt": 1748310972415}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.8693036366404555, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.8931354760728689, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.8876673242015027, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.9348225579636307, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310336413, "expiresAt": 1748310936413}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.95695943717205, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310336414, "expiresAt": 1748310936414}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 1, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310372415, "expiresAt": 1748310972415}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.2235530520833335, "efficiency": 0.833413183125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748310575654, "totalBoostPower": 13.3413183125, "averageEnergy": 1, "averageStability": 0.9236480720084179, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748310395271, "memoryEfficiency": 0, "thermalStability": 0.9842931462658776, "cpuUsage": 0.390966796875, "responseTime": 502.44735025723844, "overall": 0.5197154620968735}, {"timestamp": 1748310455278, "memoryEfficiency": 0, "thermalStability": 0.9984178108059698, "cpuUsage": 0.3766357421875, "responseTime": 468.2688271933047, "overall": 0.5853305662959094}, {"timestamp": 1748310515279, "memoryEfficiency": 0, "thermalStability": 0.9985754945625861, "cpuUsage": 0.404345703125, "responseTime": 635.1670322780908, "overall": 0.5198827379538772}, {"timestamp": 1748310575654, "memoryEfficiency": 0, "thermalStability": 0.9753098502755165, "cpuUsage": 0.4412353515625, "responseTime": 570.2673438869784, "overall": 0.5531488590519793}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}