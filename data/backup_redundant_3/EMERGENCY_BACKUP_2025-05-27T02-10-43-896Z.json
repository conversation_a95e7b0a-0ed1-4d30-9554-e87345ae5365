{"timestamp": "2025-05-27T02:10:43.896Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2327279642190108, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10563958327009935, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.8681255332467198, "confidence": -4.54155459668293, "energy": 1, "focus": 0.8740273418900603, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748311575015, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.16244864678632, "neuronActivity": 0.5229693074912495}, "emotionalHistory": [{"timestamp": 1748310644732, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310659737, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01508678871139, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310674741, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01508678871139, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310689747, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03017357742277, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310704749, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03017357742277, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310719749, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04526036613416, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310734750, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04526036613416, "neuronActivity": 0.5086788711387251}, {"timestamp": 1748310749750, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0604079706144, "neuronActivity": 0.5147604480251458}, {"timestamp": 1748310764781, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0604079706144, "neuronActivity": 0.5147604480251458}, {"timestamp": 1748310779782, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07555557509465, "neuronActivity": 0.5147604480251458}, {"timestamp": 1748310794783, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07555557509465, "neuronActivity": 0.5147604480251458}, {"timestamp": 1748310809828, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09072807149172, "neuronActivity": 0.5172496397070235}, {"timestamp": 1748310824831, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09072807149172, "neuronActivity": 0.5172496397070235}, {"timestamp": 1748310839924, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1059005678888, "neuronActivity": 0.5172496397070235}, {"timestamp": 1748310855385, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1059005678888, "neuronActivity": 0.5172496397070235}, {"timestamp": 1748310870960, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.121115317807, "neuronActivity": 0.5214749918196996}, {"timestamp": 1748310887119, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.121115317807, "neuronActivity": 0.5214749918196996}, {"timestamp": 1748310903132, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1363300677252, "neuronActivity": 0.5214749918196996}, {"timestamp": 1748310921627, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1363300677252, "neuronActivity": 0.5214749918196996}, {"timestamp": 1748310943113, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14154481764339, "neuronActivity": 0.5214749918196996}, {"timestamp": 1748310965403, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14154481764339, "neuronActivity": 0.5214749918196996}, {"timestamp": 1748310997580, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14675956756159, "neuronActivity": 0.5214749918196996}, {"timestamp": 1748311054652, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1519892606365, "neuronActivity": 0.5229693074912495}, {"timestamp": 1748311119392, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1572189537114, "neuronActivity": 0.5229693074912495}, {"timestamp": 1748311270120, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1572189537114, "neuronActivity": 0.5229693074912495}, {"timestamp": 1748311575015, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.16244864678632, "neuronActivity": 0.5229693074912495}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:56:37 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:57:34 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:58:39 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:01:10 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:06:15 PM", "mood": "curious"}], "circadianRhythm": 0.7617146346345698, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5083990555593747, "stability": 0.9123845124262154, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2562992916695315, "stability": 0.8489543408277189, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8781496458347657, "stability": 0.9940933183031097, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_4": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.9369060163166141, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748311485722, "expiresAt": 1748312085722}, "qi_enhancer_auto_5": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.9325823407361266, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748311485722, "expiresAt": 1748312085722}, "thermal_cooler_auto_6": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748311843895, "expiresAt": 1748312443895}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5083990555593747, "stability": 0.9123845124262154, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2562992916695315, "stability": 0.8489543408277189, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8781496458347657, "stability": 0.9940933183031097, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_4": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.9369060163166141, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748311485722, "expiresAt": 1748312085722}, "qi_enhancer_auto_5": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.9325823407361266, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748311485722, "expiresAt": 1748312085722}, "thermal_cooler_auto_6": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748311843895, "expiresAt": 1748312443895}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.270569598612734, "efficiency": 0.836234175916764, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748311573816, "totalBoostPower": 13.152847993063672, "averageEnergy": 167.5, "averageStability": 0.9208200881016309, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748310689697, "memoryEfficiency": 0, "thermalStability": 0.9841671082708571, "cpuUsage": 0.3866455078125, "responseTime": 238.05911952366193, "overall": 0.5716442288751827}, {"timestamp": 1748310749699, "memoryEfficiency": 0, "thermalStability": 0.9978711352166202, "cpuUsage": 0.3678466796875, "responseTime": 476.36394624108607, "overall": 0.5725478059791155}, {"timestamp": 1748310809702, "memoryEfficiency": 0, "thermalStability": 0.9984113683303197, "cpuUsage": 0.3593994140625, "responseTime": 695.1915067370909, "overall": 0.5826752848482191}, {"timestamp": 1748310869874, "memoryEfficiency": 0, "thermalStability": 0.9975847550564342, "cpuUsage": 0.3860107421875, "responseTime": 645.6663978391132, "overall": 0.5568003750118165}, {"timestamp": 1748310932398, "memoryEfficiency": 0, "thermalStability": 0.9987646326836612, "cpuUsage": 0.3904296875, "responseTime": 237.02790155961978, "overall": 0.5697025521198174}, {"timestamp": 1748311021549, "memoryEfficiency": 0, "thermalStability": 0.9951337329215474, "cpuUsage": 0.402490234375, "responseTime": 493.0177436350079, "overall": 0.5354264006041556}, {"timestamp": 1748311192521, "memoryEfficiency": 0, "thermalStability": 0.9969853445887565, "cpuUsage": 0.4030029296875, "responseTime": 611.0027036479873, "overall": 0.543650189606332}, {"timestamp": 1748311573817, "memoryEfficiency": 0, "thermalStability": 0.9969836297962401, "cpuUsage": 0.4224365234375, "responseTime": 454.6870921747444, "overall": 0.513957329184903}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}