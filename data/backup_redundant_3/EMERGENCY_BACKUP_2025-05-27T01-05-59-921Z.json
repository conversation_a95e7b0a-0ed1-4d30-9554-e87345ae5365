{"timestamp": "2025-05-27T01:05:59.921Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2904430476816016, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9411919999999999, "confidence": -3.9899817760405214, "energy": 1, "focus": 1, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748307944049, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15093243662628, "neuronActivity": 0.56938973573545}, "emotionalHistory": [{"timestamp": 1748307570403, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.542818500112076}, {"timestamp": 1748307585403, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01542818500113, "neuronActivity": 0.542818500112076}, {"timestamp": 1748307600403, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01542818500113, "neuronActivity": 0.542818500112076}, {"timestamp": 1748307615404, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03085637000225, "neuronActivity": 0.542818500112076}, {"timestamp": 1748307630405, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03085637000225, "neuronActivity": 0.542818500112076}, {"timestamp": 1748307645404, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04628455500338, "neuronActivity": 0.542818500112076}, {"timestamp": 1748307660405, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04628455500338, "neuronActivity": 0.542818500112076}, {"timestamp": 1748307675405, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06179724704675, "neuronActivity": 0.551269204337428}, {"timestamp": 1748307690418, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06179724704675, "neuronActivity": 0.551269204337428}, {"timestamp": 1748307705418, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07730993909013, "neuronActivity": 0.551269204337428}, {"timestamp": 1748307720435, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07730993909013, "neuronActivity": 0.551269204337428}, {"timestamp": 1748307735437, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09285735065131, "neuronActivity": 0.5547411561189222}, {"timestamp": 1748307750464, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09285735065131, "neuronActivity": 0.5547411561189222}, {"timestamp": 1748307765660, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1084047622125, "neuronActivity": 0.5547411561189222}, {"timestamp": 1748307781289, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1084047622125, "neuronActivity": 0.5547411561189222}, {"timestamp": 1748307796931, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12403668081595, "neuronActivity": 0.5631918603442743}, {"timestamp": 1748307814392, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12403668081595, "neuronActivity": 0.5631918603442743}, {"timestamp": 1748307832434, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1396685994194, "neuronActivity": 0.5631918603442743}, {"timestamp": 1748307852148, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1396685994194, "neuronActivity": 0.5631918603442743}, {"timestamp": 1748307875503, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14530051802284, "neuronActivity": 0.5631918603442743}, {"timestamp": 1748307903436, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14530051802284, "neuronActivity": 0.5631918603442743}, {"timestamp": 1748307944049, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15093243662628, "neuronActivity": 0.56938973573545}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:03:52 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:04:12 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:04:35 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:05:03 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:05:44 PM", "mood": "curious"}], "circadianRhythm": 0.6414514665452438, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.9291483658035592, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 1, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9989193486781601, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.8283884713866987, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748307556531, "expiresAt": 1748308156531}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.872684316163974, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748307556531, "expiresAt": 1748308156531}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.8339361421956598, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748307563658, "expiresAt": 1748308163658}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.9291483658035592, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 1, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9989193486781601, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.8283884713866987, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748307556531, "expiresAt": 1748308156531}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.872684316163974, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748307556531, "expiresAt": 1748308156531}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.8339361421956598, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748307563658, "expiresAt": 1748308163658}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.153006629505208, "efficiency": 0.8291803977703125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748307951851, "totalBoostPower": 12.918039777031249, "averageEnergy": 1, "averageStability": 0.9105127740380086, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748307615327, "memoryEfficiency": 0, "thermalStability": 0.8137510483463606, "cpuUsage": 0.5150634765625, "responseTime": 322.248035061289, "overall": 0.5111814328704427}, {"timestamp": 1748307675364, "memoryEfficiency": 0, "thermalStability": 0.9812692554874552, "cpuUsage": 0.4592529296875, "responseTime": 505.99514824408504, "overall": 0.5084873876308147}, {"timestamp": 1748307735364, "memoryEfficiency": 0, "thermalStability": 0.9898635869638788, "cpuUsage": 0.490673828125, "responseTime": 419.8497174222145, "overall": 0.5928317024738239}, {"timestamp": 1748307796044, "memoryEfficiency": 0, "thermalStability": 0.9918486352182097, "cpuUsage": 0.451513671875, "responseTime": 528.5691077567056, "overall": 0.5077044018482535}, {"timestamp": 1748307860541, "memoryEfficiency": 0, "thermalStability": 0.9916178129199479, "cpuUsage": 0.522509765625, "responseTime": 345.050765860974, "overall": 0.5828960068529998}, {"timestamp": 1748307951851, "memoryEfficiency": 0, "thermalStability": 0.9928125588430299, "cpuUsage": 0.54912109375, "responseTime": 690.2513797743645, "overall": 0.589867515609913}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}