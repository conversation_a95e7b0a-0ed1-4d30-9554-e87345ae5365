{"timestamp": "2025-05-27T15:32:49.360Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1693921817776696, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9223681599999999, "confidence": -4.134249389922273, "energy": 1, "focus": 0.8747428919715491, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748359934993, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15131572705947, "neuronActivity": 0.5199321415644399}, "emotionalHistory": [{"timestamp": 1748359520333, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5010806603151506}, {"timestamp": 1748359535332, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01501080660316, "neuronActivity": 0.5010806603151506}, {"timestamp": 1748359550333, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01501080660316, "neuronActivity": 0.5010806603151506}, {"timestamp": 1748359565334, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03002161320632, "neuronActivity": 0.5010806603151506}, {"timestamp": 1748359580335, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03002161320632, "neuronActivity": 0.5010806603151506}, {"timestamp": 1748359595335, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04503241980947, "neuronActivity": 0.5010806603151506}, {"timestamp": 1748359610335, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04503241980947, "neuronActivity": 0.5010806603151506}, {"timestamp": 1748359625335, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06011260296434, "neuronActivity": 0.5080183154871477}, {"timestamp": 1748359640336, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06011260296434, "neuronActivity": 0.5080183154871477}, {"timestamp": 1748359655353, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0751927861192, "neuronActivity": 0.5080183154871477}, {"timestamp": 1748359670391, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0751927861192, "neuronActivity": 0.5080183154871477}, {"timestamp": 1748359685392, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09030113828815, "neuronActivity": 0.5108352168955984}, {"timestamp": 1748359700415, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09030113828815, "neuronActivity": 0.5108352168955984}, {"timestamp": 1748359715454, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1054094904571, "neuronActivity": 0.5108352168955984}, {"timestamp": 1748359730729, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1054094904571, "neuronActivity": 0.5108352168955984}, {"timestamp": 1748359745898, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12058621925378, "neuronActivity": 0.5176728796677322}, {"timestamp": 1748359762538, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12058621925378, "neuronActivity": 0.5176728796677322}, {"timestamp": 1748359779364, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13576294805046, "neuronActivity": 0.5176728796677322}, {"timestamp": 1748359797421, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13576294805046, "neuronActivity": 0.5176728796677322}, {"timestamp": 1748359818165, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14093967684714, "neuronActivity": 0.5176728796677322}, {"timestamp": 1748359841302, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14093967684714, "neuronActivity": 0.5176728796677322}, {"timestamp": 1748359877270, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14611640564382, "neuronActivity": 0.5176728796677322}, {"timestamp": 1748359934993, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15131572705947, "neuronActivity": 0.5199321415644399}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "11:29:57 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:30:18 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:30:41 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:31:17 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:32:14 AM", "mood": "curious"}], "circadianRhythm": 0.10035439639807303, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8219685738448309, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.832709860100654, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.964497225292454, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9008371023112995, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748359506627, "expiresAt": 1748360106627}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.877584956430634, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748359506628, "expiresAt": 1748360106627}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.8469226286578968, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748359554823, "expiresAt": 1748360154822}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8219685738448309, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.832709860100654, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.964497225292454, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.9008371023112995, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748359506627, "expiresAt": 1748360106627}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.877584956430634, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748359506628, "expiresAt": 1748360106627}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.8469226286578968, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748359554823, "expiresAt": 1748360154822}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.153006629505208, "efficiency": 0.8291803977703125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748359896216, "totalBoostPower": 12.918039777031249, "averageEnergy": 1, "averageStability": 0.8740867244396281, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748359565257, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.4775634765625, "responseTime": 518.207549282183, "overall": 0}, {"timestamp": 1748359625301, "memoryEfficiency": 0, "thermalStability": 0.9531158127800263, "cpuUsage": 0.439892578125, "responseTime": 203.2857099363628, "overall": 0.5241814729767281}, {"timestamp": 1748359685331, "memoryEfficiency": 0, "thermalStability": 0.990867016878393, "cpuUsage": 0.3907470703125, "responseTime": 363.6749667822521, "overall": 0.5783914290061727}, {"timestamp": 1748359745683, "memoryEfficiency": 0, "thermalStability": 0.9979166032539474, "cpuUsage": 0.3978271484375, "responseTime": 457.5715663930024, "overall": 0.5847929668688527}, {"timestamp": 1748359807899, "memoryEfficiency": 0, "thermalStability": 0.9992779922154215, "cpuUsage": 0.4075439453125, "responseTime": 375.28234390869636, "overall": 0.5940548002567152}, {"timestamp": 1748359896216, "memoryEfficiency": 0, "thermalStability": 0.9985093238039149, "cpuUsage": 0.398388671875, "responseTime": 691.9222818201695, "overall": 0.5233215338430712}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}