{"timestamp": "2025-05-27T01:42:14.462Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.273249850438638, "accessFactor": 1.05, "decayFactor": 0.9894365346569974, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.301728022840822, "curiosity": 1, "confidence": -0.478230320806612, "energy": 1, "focus": 0.9006290918522044, "creativity": 1, "stress": 0.4110570737806147, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748310134396, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02943089551076, "neuronActivity": 0.4715447755373971}, "emotionalHistory": [{"timestamp": 1748310089396, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310104396, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01471544775538, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310119396, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01471544775538, "neuronActivity": 0.4715447755373971}, {"timestamp": 1748310134396, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02943089551076, "neuronActivity": 0.4715447755373971}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "9:41:29 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:41:44 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:41:59 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "9:42:14 PM", "mood": "curious"}], "circadianRhythm": 0.7157278916304046, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.9167669732137445, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.9082852377340307, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9703869300995116, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.894206829844061, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310075477, "expiresAt": 1748310675477}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.8747045016384803, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310075477, "expiresAt": 1748310675477}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.8567083131423691, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310079012, "expiresAt": 1748310679012}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.9167669732137445, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.9082852377340307, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9703869300995116, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.894206829844061, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748310075477, "expiresAt": 1748310675477}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.8747045016384803, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748310075477, "expiresAt": 1748310675477}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.8567083131423691, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748310079012, "expiresAt": 1748310679012}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.3439166666666664, "efficiency": 0.8406349999999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748310134239, "totalBoostPower": 14.0635, "averageEnergy": 1, "averageStability": 0.9035097976120329, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748310134274, "memoryEfficiency": 0, "thermalStability": 0.984767265452279, "cpuUsage": 0.431689453125, "responseTime": 371.4483680287887, "overall": 0.5061349890516287}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}