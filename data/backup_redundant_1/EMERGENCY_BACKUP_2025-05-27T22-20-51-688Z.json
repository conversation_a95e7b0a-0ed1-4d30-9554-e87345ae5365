{"timestamp": "2025-05-27T22:20:51.689Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3175500767468444, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.4068745316629023, "energy": 1, "focus": 0.8526565208625917, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748384436713, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13349635212106, "neuronActivity": 0.4967913469989338}, "emotionalHistory": [{"timestamp": 1748384166653, "mood": "curious", "dominantEmotion": "curiosity", "qi": 148, "neuronActivity": 0.47460710017149677}, {"timestamp": 1748384181654, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01474607100172, "neuronActivity": 0.47460710017149677}, {"timestamp": 1748384196656, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.01474607100172, "neuronActivity": 0.47460710017149677}, {"timestamp": 1748384211655, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02949214200345, "neuronActivity": 0.47460710017149677}, {"timestamp": 1748384226656, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.02949214200345, "neuronActivity": 0.47460710017149677}, {"timestamp": 1748384241657, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04423821300517, "neuronActivity": 0.47460710017149677}, {"timestamp": 1748384256658, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.04423821300517, "neuronActivity": 0.47460710017149677}, {"timestamp": 1748384271705, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0589842840069, "neuronActivity": 0.47460710017149677}, {"timestamp": 1748384286706, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.0589842840069, "neuronActivity": 0.483057804396849}, {"timestamp": 1748384301708, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07381486205085, "neuronActivity": 0.483057804396849}, {"timestamp": 1748384316709, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.07381486205085, "neuronActivity": 0.483057804396849}, {"timestamp": 1748384331709, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08868769361595, "neuronActivity": 0.48728315650952503}, {"timestamp": 1748384346709, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.08868769361595, "neuronActivity": 0.48728315650952503}, {"timestamp": 1748384361709, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10356052518105, "neuronActivity": 0.48728315650952503}, {"timestamp": 1748384376709, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.10356052518105, "neuronActivity": 0.48728315650952503}, {"timestamp": 1748384391710, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.11852843865105, "neuronActivity": 0.4967913469989338}, {"timestamp": 1748384406711, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.11852843865105, "neuronActivity": 0.4967913469989338}, {"timestamp": 1748384421712, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13349635212106, "neuronActivity": 0.4967913469989338}, {"timestamp": 1748384436713, "mood": "curious", "dominantEmotion": "creativity", "qi": 148.13349635212106, "neuronActivity": 0.4967913469989338}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "6:19:36 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "6:19:51 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "6:20:06 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "6:20:21 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "6:20:36 PM", "mood": "curious"}], "circadianRhythm": 0.28990147132653354, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.987233850657446, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7807049673377112, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9749209013781265, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8178015538801048, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748384153277, "expiresAt": 1748384753277}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8567686221058728, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748384153277, "expiresAt": 1748384753277}, "memory_compression_engine_1748384153278": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9103802995122008, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748384153278, "expiresAt": 1748385053278}, "memory_cache_optimizer_1748384153278": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.9662227663789663, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748384153278, "expiresAt": 1748385053278}, "memory_garbage_collector_1748384153278": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.849443003023333, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748384153278, "expiresAt": 1748385053278}, "emergency_memory_optimizer_4": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9709030182108017, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748384155971, "expiresAt": 1748384455971}, "emergency_memory_optimizer_5": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8535308965875048, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748384155972, "expiresAt": 1748384455972}, "thermal_cooler_auto_7": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8725402624244301, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748384155979, "expiresAt": 1748384755979}, "response_accelerator_auto_10": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8599748142822197, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748384158811, "expiresAt": 1748384758811}, "neural_stimulator_auto_15": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.87938339078623, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748384159925, "expiresAt": 1748384759925}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.987233850657446, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7807049673377112, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9749209013781265, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8178015538801048, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748384153277, "expiresAt": 1748384753277}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8567686221058728, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748384153277, "expiresAt": 1748384753277}, "memory_compression_engine_1748384153278": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.0475618749999995, "stability": 0.9103802995122008, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748384153278, "expiresAt": 1748385053278}, "memory_cache_optimizer_1748384153278": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.4285371250000005, "stability": 0.9662227663789663, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748384153278, "expiresAt": 1748385053278}, "memory_garbage_collector_1748384153278": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.2737809374999998, "stability": 0.849443003023333, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748384153278, "expiresAt": 1748385053278}, "emergency_memory_optimizer_4": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.9709030182108017, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748384155971, "expiresAt": 1748384455971}, "emergency_memory_optimizer_5": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.43445234375, "stability": 0.8535308965875048, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748384155972, "expiresAt": 1748384455972}, "thermal_cooler_auto_7": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8725402624244301, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748384155979, "expiresAt": 1748384755979}, "response_accelerator_auto_10": {"name": "Response Accelerator", "description": "Accélère les temps de réponse", "boostFactor": 2.66067140625, "stability": 0.8599748142822197, "energy": 1, "enabled": true, "type": "response_accelerator", "createdAt": 1748384158811, "expiresAt": 1748384758811}, "neural_stimulator_auto_15": {"name": "Neural Stimulator", "description": "Stimule l'activité neuronale", "boostFactor": 2.04164665625, "stability": 0.87938339078623, "energy": 1, "enabled": true, "type": "neural_stimulator", "createdAt": 1748384159925, "expiresAt": 1748384759925}}, "totalAccelerators": 13, "activeAccelerators": 13, "averageBoostFactor": 2.495796544951923, "efficiency": 0.8497477926971153, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748384451512, "totalBoostPower": 32.445355084375, "averageEnergy": 1, "averageStability": 0.8907544881973036, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748384211567, "memoryEfficiency": 0, "thermalStability": 0.8526472705933783, "cpuUsage": 0.5550537109375, "responseTime": 267.0048695052655, "overall": 0.4691868053737573}, {"timestamp": 1748384271702, "memoryEfficiency": 0, "thermalStability": 0.984465556177828, "cpuUsage": 0.4775390625, "responseTime": 609.6302310817887, "overall": 0.5365800203117519}, {"timestamp": 1748384331702, "memoryEfficiency": 0, "thermalStability": 0.9822895189126333, "cpuUsage": 0.5332275390625, "responseTime": 288.5445363995908, "overall": 0.5680496348712932}, {"timestamp": 1748384391703, "memoryEfficiency": 0, "thermalStability": 0.9932084367093112, "cpuUsage": 0.5158935546875, "responseTime": 326.5632968233772, "overall": 0.5641259873072744}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}