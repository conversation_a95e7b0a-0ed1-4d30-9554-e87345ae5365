{"timestamp": "2025-05-27T06:48:05.999Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.178004511839879, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -1.653745673873039, "energy": 1, "focus": 0.9974680375557337, "creativity": 1, "stress": 0.9880715414417138, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748328480941, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05919492769318, "neuronActivity": 0.48621122049864346}, "emotionalHistory": [{"timestamp": 1748328360953, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328375954, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01477760516273, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328390955, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01477760516273, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328405955, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02955521032547, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328420940, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02955521032547, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328435934, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0443328154882, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328450933, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0443328154882, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328465940, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05919492769318, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328480941, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05919492769318, "neuronActivity": 0.48621122049864346}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:47:00 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:47:15 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:47:30 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:47:45 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:48:00 AM", "mood": "curious"}], "circadianRhythm": 0.989066685372425, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.9901706937654686, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.8952752629021932, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.9739800031862333, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.9087123394487028, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748328347111, "expiresAt": 1748328947111}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.8805350917560406, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748328347111, "expiresAt": 1748328947111}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.899401571229123, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748328377260, "expiresAt": 1748328977260}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.9901706937654686, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.8952752629021932, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.9739800031862333, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.9087123394487028, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748328347111, "expiresAt": 1748328947111}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.8805350917560406, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748328347111, "expiresAt": 1748328947111}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.899401571229123, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748328377260, "expiresAt": 1748328977260}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.3017208333333334, "efficiency": 0.83810325, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748328465871, "totalBoostPower": 13.810325, "averageEnergy": 1, "averageStability": 0.9246791603812935, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748328405908, "memoryEfficiency": 0, "thermalStability": 0.9780542161729601, "cpuUsage": 0.368798828125, "responseTime": 622.6888867218463, "overall": 0.5625536985315469}, {"timestamp": 1748328465894, "memoryEfficiency": 0, "thermalStability": 0.9964157682326105, "cpuUsage": 0.3524658203125, "responseTime": 459.1281323943553, "overall": 0.5647945918838175}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}