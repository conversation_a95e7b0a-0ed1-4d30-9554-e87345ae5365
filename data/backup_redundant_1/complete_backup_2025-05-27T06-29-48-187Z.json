{"timestamp": "2025-05-27T06:29:48.187Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2820562229596586, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.8858423808639998, "confidence": -4.4099045964507475, "energy": 1, "focus": 0.8568771208345504, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748327254219, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15615678995121, "neuronActivity": 0.5221461338050719}, "emotionalHistory": [{"timestamp": 1748326609282, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326624286, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01494419041131, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326639287, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01494419041131, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326654290, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02988838082263, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326669292, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02988838082263, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326684298, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04483257123394, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326699299, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04483257123394, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326714978, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05977676164525, "neuronActivity": 0.494419041131807}, {"timestamp": 1748326730007, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05977676164525, "neuronActivity": 0.5028697453571591}, {"timestamp": 1748326745008, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07480545909883, "neuronActivity": 0.5028697453571591}, {"timestamp": 1748326760028, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07480545909883, "neuronActivity": 0.5028697453571591}, {"timestamp": 1748326775032, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08987641007353, "neuronActivity": 0.5070950974698352}, {"timestamp": 1748326790109, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08987641007353, "neuronActivity": 0.5070950974698352}, {"timestamp": 1748326805187, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10494736104823, "neuronActivity": 0.5070950974698352}, {"timestamp": 1748326820638, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10494736104823, "neuronActivity": 0.5070950974698352}, {"timestamp": 1748326836268, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12013898760495, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748326852983, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12013898760495, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748326870093, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13533061416166, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748326888875, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13533061416166, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748326910667, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14052224071838, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748326932594, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14052224071838, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748326966098, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1457138672751, "neuronActivity": 0.5191626556717162}, {"timestamp": 1748327019816, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15093532861316, "neuronActivity": 0.5221461338050719}, {"timestamp": 1748327092190, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15615678995121, "neuronActivity": 0.5221461338050719}, {"timestamp": 1748327254219, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15615678995121, "neuronActivity": 0.5221461338050719}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:22:12 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:22:46 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:23:39 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:24:52 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:27:34 AM", "mood": "curious"}], "circadianRhythm": 0.9963864467413872, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5614726900625, "stability": 0.951246330677852, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2961045175468753, "stability": 0.8880766523686702, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8980522587734376, "stability": 0.9055386946897283, "energy": 1, "enabled": true, "type": "connection"}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5614726900625, "stability": 0.951246330677852, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2961045175468753, "stability": 0.8880766523686702, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8980522587734376, "stability": 0.9055386946897283, "energy": 1, "enabled": true, "type": "connection"}}, "totalAccelerators": 3, "activeAccelerators": 3, "averageBoostFactor": 2.2518764887942706, "efficiency": 0.8351125893276562, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748327211194, "totalBoostPower": 6.755629466382812, "averageEnergy": 1, "averageStability": 0.9149538925787501, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748326654216, "memoryEfficiency": 0, "thermalStability": 0.9859787806868553, "cpuUsage": 0.3656982421875, "responseTime": 485.4832024631685, "overall": 0.571397015142332}, {"timestamp": 1748326714975, "memoryEfficiency": 0, "thermalStability": 0.9994588088658121, "cpuUsage": 0.389599609375, "responseTime": 321.20437274495873, "overall": 0.5719489910793034}, {"timestamp": 1748326775026, "memoryEfficiency": 0, "thermalStability": 0.9962588298651908, "cpuUsage": 0.3901611328125, "responseTime": 551.7227744929714, "overall": 0.570661287600739}, {"timestamp": 1748326835422, "memoryEfficiency": 0, "thermalStability": 0.9995544495268001, "cpuUsage": 0.3949951171875, "responseTime": 642.3265513693192, "overall": 0.5455919139854402}, {"timestamp": 1748326897332, "memoryEfficiency": 0, "thermalStability": 0.9994431262214979, "cpuUsage": 0.4258544921875, "responseTime": 337.8056148845592, "overall": 0.5815929063105638}, {"timestamp": 1748326985520, "memoryEfficiency": 0, "thermalStability": 0.9968058661040332, "cpuUsage": 0.3888427734375, "responseTime": 673.8687418546928, "overall": 0.5932140050584558}, {"timestamp": 1748327211194, "memoryEfficiency": 0, "thermalStability": 0.9957593837132056, "cpuUsage": 0.389599609375, "responseTime": 603.6787185584385, "overall": 0.5690577644707666}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}