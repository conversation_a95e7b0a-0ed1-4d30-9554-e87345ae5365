{"timestamp": "2025-05-27T18:38:17.133Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2451259211600094, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9411919999999999, "confidence": -3.989978992058689, "energy": 1, "focus": 1, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748371044124, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15189084404662, "neuronActivity": 0.5705440756540722}, "emotionalHistory": [{"timestamp": 1748370676678, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.553971106618302}, {"timestamp": 1748370691678, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01553971106618, "neuronActivity": 0.553971106618302}, {"timestamp": 1748370706678, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01553971106618, "neuronActivity": 0.553971106618302}, {"timestamp": 1748370721678, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03107942213236, "neuronActivity": 0.553971106618302}, {"timestamp": 1748370736679, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03107942213236, "neuronActivity": 0.553971106618302}, {"timestamp": 1748370751679, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04661913319855, "neuronActivity": 0.553971106618302}, {"timestamp": 1748370766678, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04661913319855, "neuronActivity": 0.553971106618302}, {"timestamp": 1748370781715, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06215884426473, "neuronActivity": 0.553971106618302}, {"timestamp": 1748370796716, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06215884426473, "neuronActivity": 0.5624218108436541}, {"timestamp": 1748370811717, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07778306237317, "neuronActivity": 0.5624218108436541}, {"timestamp": 1748370826719, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07778306237317, "neuronActivity": 0.5624218108436541}, {"timestamp": 1748370841738, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09344015620385, "neuronActivity": 0.565709383068674}, {"timestamp": 1748370857184, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09344015620385, "neuronActivity": 0.565709383068674}, {"timestamp": 1748370872344, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10909725003454, "neuronActivity": 0.565709383068674}, {"timestamp": 1748370887905, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10909725003454, "neuronActivity": 0.565709383068674}, {"timestamp": 1748370903491, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.124774521777, "neuronActivity": 0.5705440756540722}, {"timestamp": 1748370920368, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.124774521777, "neuronActivity": 0.5705440756540722}, {"timestamp": 1748370937633, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14047996253353, "neuronActivity": 0.5705440756540722}, {"timestamp": 1748370957465, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14047996253353, "neuronActivity": 0.5705440756540722}, {"timestamp": 1748370981621, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14618540329008, "neuronActivity": 0.5705440756540722}, {"timestamp": 1748371005220, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14618540329008, "neuronActivity": 0.5705440756540722}, {"timestamp": 1748371044124, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15189084404662, "neuronActivity": 0.5705440756540722}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:35:37 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:35:57 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:36:21 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:36:45 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:37:24 PM", "mood": "curious"}], "circadianRhythm": 0.006643575176805472, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.9252781284879105, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.8631098353687006, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9015756324526811, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 1, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748370662884, "expiresAt": 1748371262884}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.8929938123575267, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748370662885, "expiresAt": 1748371262885}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.8217847460784354, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748370681202, "expiresAt": 1748371281202}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.9252781284879105, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.8631098353687006, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 0.9015756324526811, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 1, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748370662884, "expiresAt": 1748371262884}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.8929938123575267, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748370662885, "expiresAt": 1748371262885}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.8217847460784354, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748370681202, "expiresAt": 1748371281202}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.153006629505208, "efficiency": 0.8291803977703125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748371059309, "totalBoostPower": 12.918039777031249, "averageEnergy": 1, "averageStability": 0.9007903591242091, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748370721602, "memoryEfficiency": 0, "thermalStability": 0.9364595272474818, "cpuUsage": 0.526220703125, "responseTime": 212.24700477936602, "overall": 0.5506526365532316}, {"timestamp": 1748370781713, "memoryEfficiency": 0, "thermalStability": 0.9814963717013597, "cpuUsage": 0.5103271484375, "responseTime": 697.6264184784832, "overall": 0.5623870468255786}, {"timestamp": 1748370841733, "memoryEfficiency": 0, "thermalStability": 0.9941596262984805, "cpuUsage": 0.4524169921875, "responseTime": 298.78516843029786, "overall": 0.563183276271314}, {"timestamp": 1748370902301, "memoryEfficiency": 0, "thermalStability": 0.9911776163925727, "cpuUsage": 0.46513671875, "responseTime": 267.4320391431572, "overall": 0.5833448223794493}, {"timestamp": 1748370965381, "memoryEfficiency": 0, "thermalStability": 0.9983122864117225, "cpuUsage": 0.464013671875, "responseTime": 562.6930384100308, "overall": 0.5803830363173155}, {"timestamp": 1748371059309, "memoryEfficiency": 0, "thermalStability": 0.9943314689728949, "cpuUsage": 0.435791015625, "responseTime": 421.5980590135329, "overall": 0.5823147575512004}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}