{"timestamp": "2025-05-27T14:45:41.935Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.3307602522633732, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9603999999999999, "confidence": -3.8412936295132827, "energy": 1, "focus": 0.6935668560284866, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748357121026, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13587529319882, "neuronActivity": 0.46807934395285034}, "emotionalHistory": [{"timestamp": 1748356785114, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.45013684397627907}, {"timestamp": 1748356800114, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01450136843977, "neuronActivity": 0.45013684397627907}, {"timestamp": 1748356815114, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01450136843977, "neuronActivity": 0.45013684397627907}, {"timestamp": 1748356830115, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02900273687953, "neuronActivity": 0.45013684397627907}, {"timestamp": 1748356845114, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02900273687953, "neuronActivity": 0.45013684397627907}, {"timestamp": 1748356860115, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0435041053193, "neuronActivity": 0.45013684397627907}, {"timestamp": 1748356875115, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0435041053193, "neuronActivity": 0.45013684397627907}, {"timestamp": 1748356890172, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0435041053193, "neuronActivity": 0.45013684397627907}, {"timestamp": 1748356905173, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05807222270249, "neuronActivity": 0.4568117383190476}, {"timestamp": 1748356920183, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07264034008568, "neuronActivity": 0.4568117383190476}, {"timestamp": 1748356935185, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07264034008568, "neuronActivity": 0.4568117383190476}, {"timestamp": 1748356950205, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08723662648296, "neuronActivity": 0.45962863972749823}, {"timestamp": 1748356965401, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08723662648296, "neuronActivity": 0.45962863972749823}, {"timestamp": 1748356980433, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10183291288024, "neuronActivity": 0.45962863972749823}, {"timestamp": 1748356995994, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10183291288024, "neuronActivity": 0.45962863972749823}, {"timestamp": 1748357011425, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11651370631976, "neuronActivity": 0.46807934395285034}, {"timestamp": 1748357028155, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11651370631976, "neuronActivity": 0.46807934395285034}, {"timestamp": 1748357044503, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13119449975929, "neuronActivity": 0.46807934395285034}, {"timestamp": 1748357064014, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13119449975929, "neuronActivity": 0.46807934395285034}, {"timestamp": 1748357086849, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13587529319882, "neuronActivity": 0.46807934395285034}, {"timestamp": 1748357121026, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13587529319882, "neuronActivity": 0.46807934395285034}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:43:48 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:44:04 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:44:24 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:44:46 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:45:21 AM", "mood": "curious"}], "circadianRhythm": 0.16975267627925517, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8452830292493809, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.973958389260171, "energy": 1, "enabled": true, "type": "connection"}, "qi_enhancer_auto_1": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9448954750539723, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748356771427, "expiresAt": 1748357371427}, "memory_optimizer_auto_2": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8152360377492861, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748356775246, "expiresAt": 1748357375246}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 1, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748356800197, "expiresAt": 1748357400197}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 1, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.8452830292493809, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.973958389260171, "energy": 1, "enabled": true, "type": "connection"}, "qi_enhancer_auto_1": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9448954750539723, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748356771427, "expiresAt": 1748357371427}, "memory_optimizer_auto_2": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8152360377492861, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748356775246, "expiresAt": 1748357375246}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 1, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748356800197, "expiresAt": 1748357400197}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.1873753994791665, "efficiency": 0.83124252396875, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748357079142, "totalBoostPower": 13.124252396874999, "averageEnergy": 1, "averageStability": 0.929895488552135, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748356830061, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.420849609375, "responseTime": 695.1028164279016, "overall": 0}, {"timestamp": 1748356890171, "memoryEfficiency": 0, "thermalStability": 0.9131383374333382, "cpuUsage": 0.397265625, "responseTime": 441.7566668843533, "overall": 0.5739147668613169}, {"timestamp": 1748356950201, "memoryEfficiency": 0, "thermalStability": 0.9503414322729824, "cpuUsage": 0.446630859375, "responseTime": 239.57248605238829, "overall": 0.5046588743138696}, {"timestamp": 1748357010815, "memoryEfficiency": 0, "thermalStability": 0.95347371071945, "cpuUsage": 0.4332275390625, "responseTime": 361.30071861758, "overall": 0.5460617966546002}, {"timestamp": 1748357079142, "memoryEfficiency": 0, "thermalStability": 0.9600877065533473, "cpuUsage": 0.4909423828125, "responseTime": 326.19040870965523, "overall": 0.5425128784603862}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}