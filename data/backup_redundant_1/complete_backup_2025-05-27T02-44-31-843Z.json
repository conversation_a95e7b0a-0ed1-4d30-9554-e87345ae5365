{"timestamp": "2025-05-27T02:44:31.843Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2626135347958118, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9411919999999999, "confidence": -3.9899770952828884, "energy": 1, "focus": 1, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748313859523, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1523304691603, "neuronActivity": 0.5682718644763299}, "emotionalHistory": [{"timestamp": 1748313501738, "mood": "curious", "dominantEmotion": "creativity", "qi": 120, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313516741, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01562290666739, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313531742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01562290666739, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313546742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03124581333478, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313561742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03124581333478, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313576742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04686872000217, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313591743, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04686872000217, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313606892, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06255143864693, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313621894, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06255143864693, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313636894, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0782341572917, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313651896, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0782341572917, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313666895, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09391687593647, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313682102, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09391687593647, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313697204, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10959959458124, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313712893, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10959959458124, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313728280, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12528231322601, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313744551, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12528231322601, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313760826, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14096503187078, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313778836, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14096503187078, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313802846, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14664775051554, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313824111, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14664775051554, "neuronActivity": 0.5682718644763299}, {"timestamp": 1748313859523, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1523304691603, "neuronActivity": 0.5682718644763299}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:42:40 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:42:58 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:43:22 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "10:43:44 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:44:19 PM", "mood": "curious"}], "circadianRhythm": 0.8285649067566734, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8637873099716585, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9108390020891166, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8473553683769111, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8629831706729287, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748313487961, "expiresAt": 1748314087961}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9371489698543191, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748313487961, "expiresAt": 1748314087961}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8659638751266232, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748313497052, "expiresAt": 1748314097052}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8637873099716585, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9108390020891166, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8473553683769111, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8629831706729287, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748313487961, "expiresAt": 1748314087961}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9371489698543191, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748313487961, "expiresAt": 1748314087961}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8659638751266232, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748313497052, "expiresAt": 1748314097052}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.1873753994791665, "efficiency": 0.83124252396875, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748313789749, "totalBoostPower": 13.124252396874999, "averageEnergy": 1, "averageStability": 0.8813462826819262, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748313546660, "memoryEfficiency": 0, "thermalStability": 0.9863083826171027, "cpuUsage": 0.3647705078125, "responseTime": 546.8506093378742, "overall": 0.5780532008315213}, {"timestamp": 1748313606878, "memoryEfficiency": 0, "thermalStability": 0.999019219684932, "cpuUsage": 0.369921875, "responseTime": 262.58067665542364, "overall": 0.520817989837133}, {"timestamp": 1748313666879, "memoryEfficiency": 0, "thermalStability": 0.9991338543179963, "cpuUsage": 0.356884765625, "responseTime": 410.50765868316057, "overall": 0.5765177634680068}, {"timestamp": 1748313727254, "memoryEfficiency": 0, "thermalStability": 0.9977427638239331, "cpuUsage": 0.3884521484375, "responseTime": 402.7904836580753, "overall": 0.5668070806525458}, {"timestamp": 1748313789749, "memoryEfficiency": 0, "thermalStability": 0.9969054687768221, "cpuUsage": 0.4107666015625, "responseTime": 366.7048384639669, "overall": 0.5555374749546846}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}