{"timestamp": "2025-05-27T18:28:31.028Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.4748672087648471, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10407070439254373, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9411919999999999, "confidence": -3.989990957628777, "energy": 1, "focus": 0.9223773335978299, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748370488523, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14760648190429, "neuronActivity": 0.5343741963453833}, "emotionalHistory": [{"timestamp": 1748370131530, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5115568405603639}, {"timestamp": 1748370146530, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0151155684056, "neuronActivity": 0.5115568405603639}, {"timestamp": 1748370161530, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0151155684056, "neuronActivity": 0.5115568405603639}, {"timestamp": 1748370176538, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03023113681121, "neuronActivity": 0.5115568405603639}, {"timestamp": 1748370191538, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03023113681121, "neuronActivity": 0.5115568405603639}, {"timestamp": 1748370206541, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04534670521682, "neuronActivity": 0.5115568405603639}, {"timestamp": 1748370221548, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04534670521682, "neuronActivity": 0.5115568405603639}, {"timestamp": 1748370236559, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06054678066467, "neuronActivity": 0.5200075447857159}, {"timestamp": 1748370251561, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06054678066467, "neuronActivity": 0.5200075447857159}, {"timestamp": 1748370266561, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07574685611252, "neuronActivity": 0.5200075447857159}, {"timestamp": 1748370281613, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07574685611252, "neuronActivity": 0.5200075447857159}, {"timestamp": 1748370296651, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0909891850815, "neuronActivity": 0.5242328968983919}, {"timestamp": 1748370311895, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0909891850815, "neuronActivity": 0.5242328968983919}, {"timestamp": 1748370326931, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10623151405048, "neuronActivity": 0.5242328968983919}, {"timestamp": 1748370342385, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10623151405048, "neuronActivity": 0.5242328968983919}, {"timestamp": 1748370357635, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12157525601393, "neuronActivity": 0.5343741963453833}, {"timestamp": 1748370374172, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12157525601393, "neuronActivity": 0.5343741963453833}, {"timestamp": 1748370390082, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13691899797738, "neuronActivity": 0.5343741963453833}, {"timestamp": 1748370410025, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13691899797738, "neuronActivity": 0.5343741963453833}, {"timestamp": 1748370429373, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14226273994083, "neuronActivity": 0.5343741963453833}, {"timestamp": 1748370453285, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14226273994083, "neuronActivity": 0.5343741963453833}, {"timestamp": 1748370488523, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14760648190429, "neuronActivity": 0.5343741963453833}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:26:30 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:26:50 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:27:09 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:27:33 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:28:08 PM", "mood": "curious"}], "circadianRhythm": 0.0037647874241186208, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8527733995262545, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9670206694663653, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8905867336423123, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8406998010946168, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748370117801, "expiresAt": 1748370717801}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9756555396371039, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748370117801, "expiresAt": 1748370717801}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8639028564752648, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748370122149, "expiresAt": 1748370722149}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.8527733995262545, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9670206694663653, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.8905867336423123, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8406998010946168, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748370117801, "expiresAt": 1748370717801}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9756555396371039, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748370117801, "expiresAt": 1748370717801}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8639028564752648, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748370122149, "expiresAt": 1748370722149}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.1873753994791665, "efficiency": 0.83124252396875, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748370421137, "totalBoostPower": 13.124252396874999, "averageEnergy": 1, "averageStability": 0.8984398333069863, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748370176456, "memoryEfficiency": 0, "thermalStability": 0.9710573770933681, "cpuUsage": 0.392431640625, "responseTime": 370.97124371299833, "overall": 0.5329601087002966}, {"timestamp": 1748370236544, "memoryEfficiency": 0, "thermalStability": 0.997748435412844, "cpuUsage": 0.3817138671875, "responseTime": 576.3287161009612, "overall": 0.5259817298175385}, {"timestamp": 1748370296585, "memoryEfficiency": 0, "thermalStability": 0.9960516567445463, "cpuUsage": 0.42939453125, "responseTime": 449.52451175267487, "overall": 0.5170230910621784}, {"timestamp": 1748370357044, "memoryEfficiency": 0, "thermalStability": 0.9913211615549193, "cpuUsage": 0.4874755859375, "responseTime": 302.2325778113005, "overall": 0.5582495649915908}, {"timestamp": 1748370422504, "memoryEfficiency": 0, "thermalStability": 0.9842214361247089, "cpuUsage": 0.4884765625, "responseTime": 519.2380833662596, "overall": 0.5386048912998707}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}