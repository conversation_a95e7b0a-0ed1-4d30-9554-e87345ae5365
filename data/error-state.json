{"stats": {"total": 1, "byType": {"uncaughtException": 1}, "byComponent": {}, "resolved": 0, "unresolved": 1}, "recentErrors": [{"type": "uncaughtException", "message": "Unexpected eval or arguments in strict mode", "stack": "/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/modules/real-qi-evaluation-system.js:481\n        const recentAvg = recent.reduce((sum, eval) => sum + eval.qi, 0) / recent.length;\n                                              ^^^^\n\nSyntaxError: Unexpected eval or arguments in strict mode\n    at wrapSafe (node:internal/modules/cjs/loader:1666:18)\n    at Module._compile (node:internal/modules/cjs/loader:1708:20)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/server.js:97:32)", "timestamp": "2025-05-27T22:09:56.459Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T22:09:56.465Z"}