{"stats": {"total": 291, "byType": {"uncaughtException": 291}, "byComponent": {}, "resolved": 0, "unresolved": 291}, "recentErrors": [{"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.835Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.836Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.838Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.841Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.842Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.843Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.844Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.847Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.848Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.849Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.851Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.852Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.853Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.855Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.856Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.857Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.867Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.868Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.897Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.899Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.901Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.902Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.904Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.933Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.935Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.937Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.940Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.942Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.944Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.948Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.961Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.962Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.963Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.964Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.965Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.967Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.968Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.975Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.977Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.980Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.981Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.983Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.985Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.993Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.995Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.996Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:29.997Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:29.998Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:51:30.001Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:51:30.002Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T21:51:30.015Z"}