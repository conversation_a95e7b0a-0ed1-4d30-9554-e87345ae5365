{"stats": {"total": 1, "byType": {"uncaughtException": 1}, "byComponent": {}, "resolved": 0, "unresolved": 1}, "recentErrors": [{"type": "uncaughtException", "message": "Cannot read properties of undefined (reading 'listen')", "stack": "TypeError: Cannot read properties of undefined (reading 'listen')\n    at Object.<anonymous> (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/server.js:7022:8)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:151:5)\n    at node:internal/main/run_main_module:33:47", "timestamp": "2025-05-27T21:10:02.635Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T21:10:05.100Z"}