{"stats": {"total": 61, "byType": {"uncaughtException": 61}, "byComponent": {}, "resolved": 0, "unresolved": 61}, "recentErrors": [{"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.895Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.896Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.899Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.903Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.903Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.904Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.909Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.910Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.911Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.912Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.913Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.914Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.917Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at /Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/speed-optimizer.js:183:25", "timestamp": "2025-05-27T17:23:45.919Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.919Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.920Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.923Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.924Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.926Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.928Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.929Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.931Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.932Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.933Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.934Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.936Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.936Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.939Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.941Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:23:45.942Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:23:45.943Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:33.547Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:33.548Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:33.549Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:33.550Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:33.551Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:33.552Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:33.860Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:35.095Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:35.098Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:35.099Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:35.100Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:35.101Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:35.408Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:35.410Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:35.412Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:35.415Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:35.416Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T17:24:35.418Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T17:24:35.431Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T17:24:35.432Z"}