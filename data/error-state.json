{"stats": {"total": 99, "byType": {"uncaughtException": 99}, "byComponent": {}, "resolved": 0, "unresolved": 99}, "recentErrors": [{"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:43.984Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:43.985Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:43.986Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:43.989Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:43.990Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:43.994Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:43.996Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:43.997Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:44.132Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:44.210Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:44.420Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:44.421Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:44.840Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:44.910Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:44.911Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:44.912Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.398Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.399Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.400Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.950Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.951Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.952Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.954Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.956Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.958Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.959Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.961Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.962Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.964Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.965Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.966Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.968Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.970Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.972Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.973Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.974Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.975Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.976Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.978Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.982Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.982Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.984Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.985Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.986Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.988Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.989Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.990Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.992Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.log (node:internal/console/constructor:380:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T15:16:45.993Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:962:11)\n    at Socket._write (node:net:974:8)\n    at writeOrBuffer (node:internal/streams/writable:392:12)\n    at _write (node:internal/streams/writable:333:10)\n    at Writable.write (node:internal/streams/writable:337:10)\n    at console.value (node:internal/console/constructor:305:16)\n    at console.warn (node:internal/console/constructor:385:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T15:16:45.995Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T15:16:45.995Z"}