{"stats": {"total": 1, "byType": {"uncaughtException": 1}, "byComponent": {}, "resolved": 0, "unresolved": 1}, "recentErrors": [{"type": "uncaughtException", "message": "listen EADDRINUSE: address already in use :::3000", "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1817:16)\n    at listenInCluster (node:net:1865:12)\n    at Server.listen (node:net:1953:7)\n    at Object.<anonymous> (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/server.js:7021:8)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)\n    at node:internal/main/run_main_module:28:49", "timestamp": "2025-05-27T19:12:49.278Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T19:12:49.297Z"}