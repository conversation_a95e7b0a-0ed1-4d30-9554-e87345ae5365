{"stats": {"total": 334, "byType": {"uncaughtException": 334}, "byComponent": {}, "resolved": 0, "unresolved": 334}, "recentErrors": [{"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.308Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.314Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.317Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.318Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.320Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.330Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.332Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.333Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.335Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.336Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.338Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.340Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.354Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.427Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.432Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.498Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.505Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.523Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.544Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.547Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.549Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.550Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.551Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.554Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.555Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.566Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.568Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.570Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.573Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.575Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.576Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.578Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.585Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.587Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.589Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.590Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.591Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.593Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.594Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.600Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.604Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.605Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.612Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.615Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.617Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.629Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.630Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.632Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:36:35.636Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EIO", "stack": "Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:36:35.637Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T21:36:35.638Z"}