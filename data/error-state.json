{"stats": {"total": 1, "byType": {"uncaughtException": 1}, "byComponent": {}, "resolved": 0, "unresolved": 1}, "recentErrors": [{"type": "uncaughtException", "message": "listen EADDRINUSE: address already in use :::3000", "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Object.<anonymous> (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/server.js:7021:8)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)", "timestamp": "2025-05-27T20:07:51.048Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T20:07:51.079Z"}