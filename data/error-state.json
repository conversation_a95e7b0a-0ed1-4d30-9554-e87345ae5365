{"stats": {"total": 21, "byType": {"uncaughtException": 21}, "byComponent": {}, "resolved": 0, "unresolved": 21}, "recentErrors": [{"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at AdvancedOptimizationSystem.triggerOptimizations (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/advanced-optimization-system.js:260:17)", "timestamp": "2025-05-27T21:03:19.773Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:20.085Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:20.387Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:20.400Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:20.657Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:21.675Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:21.676Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:21.693Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:21.694Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:21.696Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:21.697Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:21.701Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:21.702Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:21.722Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:21.722Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:21.724Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:21.725Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:21.729Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:21.730Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:137:19)", "timestamp": "2025-05-27T21:03:21.735Z", "context": {}, "critical": true}, {"type": "uncaughtException", "message": "write EPIPE", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at Logger.log (/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933/utils/logger.js:143:19)", "timestamp": "2025-05-27T21:03:21.736Z", "context": {}, "critical": true}], "timestamp": "2025-05-27T21:03:21.737Z"}