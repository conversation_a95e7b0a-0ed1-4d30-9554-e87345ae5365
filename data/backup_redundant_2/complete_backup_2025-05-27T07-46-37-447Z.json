{"timestamp": "2025-05-27T07:46:37.447Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2648505314101839, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.1989575922285685, "energy": 1, "focus": 0.8955772596232648, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748331990195, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11972536479557, "neuronActivity": 0.5093048708364499}, "emotionalHistory": [{"timestamp": 1748331746956, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331761957, "mood": "curious", "dominantEmotion": "energy", "qi": 120.0148871115537, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331776958, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0148871115537, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331791959, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02977422310741, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331806959, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02977422310741, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331821959, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04466133466111, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331836972, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04466133466111, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331851972, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05963295325708, "neuronActivity": 0.497161859596081}, {"timestamp": 1748331866974, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05963295325708, "neuronActivity": 0.497161859596081}, {"timestamp": 1748331881992, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07460457185304, "neuronActivity": 0.497161859596081}, {"timestamp": 1748331896992, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07460457185304, "neuronActivity": 0.497161859596081}, {"timestamp": 1748331912040, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08961844397012, "neuronActivity": 0.501387211708757}, {"timestamp": 1748331927092, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08961844397012, "neuronActivity": 0.501387211708757}, {"timestamp": 1748331942201, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1046323160872, "neuronActivity": 0.501387211708757}, {"timestamp": 1748331957695, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1046323160872, "neuronActivity": 0.501387211708757}, {"timestamp": 1748331973613, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11972536479557, "neuronActivity": 0.5093048708364499}, {"timestamp": 1748331990195, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11972536479557, "neuronActivity": 0.5093048708364499}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "3:45:27 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "3:45:42 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "3:45:57 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "3:46:13 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "3:46:30 AM", "mood": "curious"}], "circadianRhythm": 0.9469762123829426, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9651595187170539, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.932915296796292, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9907486711005878, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.8969016843213037, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748331733043, "expiresAt": 1748332333043}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.9593273091563038, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748331733043, "expiresAt": 1748332333043}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.872662514591726, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748331737090, "expiresAt": 1748332337090}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9651595187170539, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.932915296796292, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9907486711005878, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.8969016843213037, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748331733043, "expiresAt": 1748332333043}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.9593273091563038, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748331733043, "expiresAt": 1748332333043}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.872662514591726, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748331737090, "expiresAt": 1748332337090}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.2235530520833335, "efficiency": 0.833413183125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748331972447, "totalBoostPower": 13.3413183125, "averageEnergy": 1, "averageStability": 0.9362858324472113, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748331791883, "memoryEfficiency": 0, "thermalStability": 0.9497669412030114, "cpuUsage": 0.4538330078125, "responseTime": 234.4386709008192, "overall": 0.555218749455438}, {"timestamp": 1748331851883, "memoryEfficiency": 0, "thermalStability": 0.9957419801089499, "cpuUsage": 0.4263916015625, "responseTime": 453.74792961246203, "overall": 0.5192259367712407}, {"timestamp": 1748331911883, "memoryEfficiency": 0, "thermalStability": 0.9983620937085814, "cpuUsage": 0.420263671875, "responseTime": 522.7779268383129, "overall": 0.5729995019232923}, {"timestamp": 1748331972447, "memoryEfficiency": 0, "thermalStability": 0.9943752858373854, "cpuUsage": 0.4517333984375, "responseTime": 594.3497371703597, "overall": 0.5743056504749489}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}