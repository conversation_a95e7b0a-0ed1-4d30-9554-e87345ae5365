{"timestamp": "2025-05-27T17:20:09.890Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1026626394221037, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10511401320407897, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9411919999999999, "confidence": -3.989993820706844, "energy": 1, "focus": 0.894082439140908, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748366377501, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14654244205917, "neuronActivity": 0.5244582787338722}, "emotionalHistory": [{"timestamp": 1748366027833, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5020225099793995}, {"timestamp": 1748366042834, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01502022509979, "neuronActivity": 0.5020225099793995}, {"timestamp": 1748366057834, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01502022509979, "neuronActivity": 0.5020225099793995}, {"timestamp": 1748366072835, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03004045019958, "neuronActivity": 0.5020225099793995}, {"timestamp": 1748366087836, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03004045019958, "neuronActivity": 0.5020225099793995}, {"timestamp": 1748366102837, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04506067529937, "neuronActivity": 0.5020225099793995}, {"timestamp": 1748366117837, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04506067529937, "neuronActivity": 0.5020225099793995}, {"timestamp": 1748366132822, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06016540744142, "neuronActivity": 0.5104732142047516}, {"timestamp": 1748366147841, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06016540744142, "neuronActivity": 0.5104732142047516}, {"timestamp": 1748366162840, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07527013958347, "neuronActivity": 0.5104732142047516}, {"timestamp": 1748366177856, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07527013958347, "neuronActivity": 0.5104732142047516}, {"timestamp": 1748366192856, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09041712524665, "neuronActivity": 0.5146985663174275}, {"timestamp": 1748366208099, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09041712524665, "neuronActivity": 0.5146985663174275}, {"timestamp": 1748366223223, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10556411090982, "neuronActivity": 0.5146985663174275}, {"timestamp": 1748366238739, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10556411090982, "neuronActivity": 0.5146985663174275}, {"timestamp": 1748366254179, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12080869369716, "neuronActivity": 0.5244582787338722}, {"timestamp": 1748366270863, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12080869369716, "neuronActivity": 0.5244582787338722}, {"timestamp": 1748366286603, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13605327648449, "neuronActivity": 0.5244582787338722}, {"timestamp": 1748366304947, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13605327648449, "neuronActivity": 0.5244582787338722}, {"timestamp": 1748366323408, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14129785927183, "neuronActivity": 0.5244582787338722}, {"timestamp": 1748366345961, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14129785927183, "neuronActivity": 0.5244582787338722}, {"timestamp": 1748366377501, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14654244205917, "neuronActivity": 0.5244582787338722}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:18:06 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:18:24 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:18:43 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:19:05 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:19:37 PM", "mood": "curious"}], "circadianRhythm": 0.007738848170328216, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8428730822765651, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9448222261067075, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 1, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.8404615056059145, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748366014159, "expiresAt": 1748366614159}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.9237994421771157, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748366014159, "expiresAt": 1748366614159}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.927749650437063, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748366049594, "expiresAt": 1748366649594}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8428730822765651, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9448222261067075, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 1, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.8404615056059145, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748366014159, "expiresAt": 1748366614159}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.9237994421771157, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748366014159, "expiresAt": 1748366614159}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.927749650437063, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748366049594, "expiresAt": 1748366649594}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.153006629505208, "efficiency": 0.8291803977703125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748366402230, "totalBoostPower": 12.918039777031249, "averageEnergy": 1, "averageStability": 0.9132843177672276, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748366072782, "memoryEfficiency": 0, "thermalStability": 0.7260125835736593, "cpuUsage": 0.5215087890625, "responseTime": 500.3057705843868, "overall": 0.5040516149386233}, {"timestamp": 1748366132770, "memoryEfficiency": 0, "thermalStability": 0.9610144110603465, "cpuUsage": 0.4627685546875, "responseTime": 679.8836530507283, "overall": 0.5592518786769274}, {"timestamp": 1748366192806, "memoryEfficiency": 0, "thermalStability": 0.9940053479539024, "cpuUsage": 0.475390625, "responseTime": 215.9133804065611, "overall": 0.5862988357230365}, {"timestamp": 1748366253220, "memoryEfficiency": 0, "thermalStability": 0.9957413433740536, "cpuUsage": 0.4708740234375, "responseTime": 603.2194886556911, "overall": 0.5906065943191379}, {"timestamp": 1748366315511, "memoryEfficiency": 0, "thermalStability": 0.9956404556830724, "cpuUsage": 0.43701171875, "responseTime": 335.07076645529804, "overall": 0.5146985993271294}, {"timestamp": 1748366402230, "memoryEfficiency": 0, "thermalStability": 0.9928313356720739, "cpuUsage": 0.4101318359375, "responseTime": 277.0028496144788, "overall": 0.5312721569389436}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}