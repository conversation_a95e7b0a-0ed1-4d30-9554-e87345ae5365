{"timestamp": "2025-05-27T06:49:57.894Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.179967059588678, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.025768373559517, "energy": 1, "focus": 0.8895420333987387, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748328586721, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11878670798247, "neuronActivity": 0.4962800555987611}, "emotionalHistory": [{"timestamp": 1748328360953, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328375954, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01477760516273, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328390955, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01477760516273, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328405955, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02955521032547, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328420940, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02955521032547, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328435934, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0443328154882, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328450933, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0443328154882, "neuronActivity": 0.4777605162732915}, {"timestamp": 1748328465940, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05919492769318, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328480941, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05919492769318, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328495941, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07405703989816, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328510976, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07405703989816, "neuronActivity": 0.48621122049864346}, {"timestamp": 1748328525977, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08894814991687, "neuronActivity": 0.4891110018709943}, {"timestamp": 1748328540978, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08894814991687, "neuronActivity": 0.4891110018709943}, {"timestamp": 1748328556088, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10383925993558, "neuronActivity": 0.4891110018709943}, {"timestamp": 1748328571418, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10383925993558, "neuronActivity": 0.4891110018709943}, {"timestamp": 1748328586721, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11878670798247, "neuronActivity": 0.4962800555987611}], "currentThoughts": [{"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:48:45 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "2:49:00 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:49:16 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "2:49:31 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "2:49:46 AM", "mood": "curious"}], "circadianRhythm": 0.988252281221779, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9604043529073646, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.9321672940315572, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9645340907273695, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.8889039310115678, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748328347111, "expiresAt": 1748328947111}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.9087734185852968, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748328347111, "expiresAt": 1748328947111}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.9602057484195876, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748328377260, "expiresAt": 1748328977260}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.7380495, "stability": 0.9604043529073646, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4285371250000005, "stability": 0.9321672940315572, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9642685625000003, "stability": 0.9645340907273695, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.907253125, "stability": 0.8889039310115678, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748328347111, "expiresAt": 1748328947111}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.558858125, "stability": 0.9087734185852968, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748328347111, "expiresAt": 1748328947111}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.744351875, "stability": 0.9602057484195876, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748328377260, "expiresAt": 1748328977260}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.2235530520833335, "efficiency": 0.833413183125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748328586138, "totalBoostPower": 13.3413183125, "averageEnergy": 1, "averageStability": 0.9358314726137906, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748328405908, "memoryEfficiency": 0, "thermalStability": 0.9780542161729601, "cpuUsage": 0.368798828125, "responseTime": 622.6888867218463, "overall": 0.5625536985315469}, {"timestamp": 1748328465894, "memoryEfficiency": 0, "thermalStability": 0.9964157682326105, "cpuUsage": 0.3524658203125, "responseTime": 459.1281323943553, "overall": 0.5647945918838175}, {"timestamp": 1748328525896, "memoryEfficiency": 0, "thermalStability": 0.998937514755461, "cpuUsage": 0.352734375, "responseTime": 447.8605608383694, "overall": 0.522407867378654}, {"timestamp": 1748328586138, "memoryEfficiency": 0, "thermalStability": 0.9986240196973085, "cpuUsage": 0.3766357421875, "responseTime": 649.3255474655091, "overall": 0.5908523802431438}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}