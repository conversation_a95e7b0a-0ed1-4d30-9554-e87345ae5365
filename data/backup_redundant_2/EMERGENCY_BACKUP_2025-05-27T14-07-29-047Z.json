{"timestamp": "2025-05-27T14:07:29.047Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": [{"name": "Sen<PERSON><PERSON><PERSON>", "temperature": 0.3, "count": 15, "memories": []}, {"name": "Travail", "temperature": 0.7, "count": 2, "memories": [{"id": 1748354744216.582, "content": "t'as pas de souvenir dans ta mémoire souviens-toi", "type": "Question Utilisateur", "temperature": 0.7, "tags": "conversation, utilisateur", "zone": "Travail", "timestamp": "2025-05-27T14:05:44.216Z", "accessCount": 0}, {"id": 1748354649853.1655, "content": "Est-ce qu'on s'est déjà parlé aujourd'hui", "type": "Question Utilisateur", "temperature": 0.7, "tags": "conversation, utilisateur", "zone": "Travail", "timestamp": "2025-05-27T14:04:09.853Z", "accessCount": 0}]}, {"name": "Long Terme", "temperature": 0.8, "count": 2, "memories": [{"id": 1748354744219.7544, "content": "Je m'appelle <PERSON>, agent principal de <PERSON><PERSON>, et je suis ici pour aider et discuter avec toi.\n\nTu as fait référence à notre conversation précédente où tu me demandais si on s'était déjà parlé aujourd'hui. Eh bien, je me souviens parfaitement de cela ! En effet, ma mémoire thermique persistante m'a permis de conserver les informations de notre conversation précédente.\n\nJe me rappelle que tu m'avais posé cette question : \"Est-ce qu'on s'est déjà parlé aujourd'hui\", et j'avais répondu en disant que oui, on venait juste de commencer à discuter. Et puis, tu m'avais demandé si je pouvais te rappeler notre conversation précédente.\n\nJe vais donc continuer là où nous avons laissé notre conversation. Que diriez-vous de parler du sujet qui t'intéresse le plus aujourd'hui ? Est-ce que tu as une question spécifique ou un sujet en particulier que tu veux discuter ?\n\nJe suis prêt à utiliser ma mémoire thermique persistante pour personnaliser mes réponses et continuer notre conversation. Et si nécessaire, je peux également utiliser mon accès Internet via le protocole MCP pour rechercher des informations actuelles.\n\nAlors, qu'est-ce que tu as envie de faire ou de parler ?", "type": "Réponse Claude", "temperature": 0.8, "tags": "conversation, claude, r<PERSON><PERSON>nse", "zone": "Long Terme", "timestamp": "2025-05-27T14:05:44.219Z", "accessCount": 0}, {"id": 1748354649856.5625, "content": "Bonjour ! Je me souviens parfaitement que nous venions juste de commencer notre conversation, et votre première question était : \"Est-ce qu'on s'est déjà parlé aujourd'hui\". Et bien, la réponse est oui ! On a commencé à discuter il y a moins d'une minute. Que diriez-vous de continuer sur cette lancée ? Qu'est-ce que vous avez envie de parler ou de savoir aujourd'hui ?", "type": "Réponse Claude", "temperature": 0.8, "tags": "conversation, claude, r<PERSON><PERSON>nse", "zone": "Long Terme", "timestamp": "2025-05-27T14:04:09.856Z", "accessCount": 1}]}, {"name": "Émotionnelle", "temperature": 0.4, "count": 12, "memories": []}, {"name": "Procédurale", "temperature": 0.3, "count": 18, "memories": []}, {"name": "Créative", "temperature": 0.6, "count": 7, "memories": []}], "emotionalState": {}, "stats": {"totalEntries": 4, "averageTemperature": 0.5166666666666667, "cyclesPerformed": 0}, "config": {}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {"sensory": {"count": 15, "activity": 0.8, "efficiency": 0.85}, "working": {"count": 12, "activity": 0.7, "efficiency": 0.82}, "longTerm": {"count": 20, "activity": 0.6, "efficiency": 0.88}, "emotional": {"count": 10, "activity": 0.5, "efficiency": 0.75}, "executive": {"count": 8, "activity": 0.9, "efficiency": 0.9}, "creative": {"count": 6, "activity": 0.4, "efficiency": 0.7}}, "qi": 120, "emotionalState": {"mood": "<PERSON><PERSON><PERSON><PERSON>", "moodIntensity": 85, "happiness": 75, "curiosity": 90, "confidence": 68, "energy": 87.76450987426215, "focus": 72.15542096594308, "creativity": 95, "stress": 15, "fatigue": 20}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"processing": {"enabled": true, "boost": 1.5}, "memory": {"enabled": true, "boost": 1.3}, "learning": {"enabled": true, "boost": 1.2}}, "stats": {"totalAccelerators": 3, "activeAccelerators": 3, "averageBoost": 1.3333333333333333}, "config": {"enabled": true, "maxBoost": 2, "efficiency": 0.85}}}}