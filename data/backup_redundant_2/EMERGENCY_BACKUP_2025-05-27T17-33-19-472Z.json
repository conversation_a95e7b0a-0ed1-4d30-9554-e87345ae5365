{"timestamp": "2025-05-27T17:33:19.472Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1417746828073736, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9223681599999999, "confidence": -4.134226765934278, "energy": 1, "focus": 1, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748367126527, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15945978789817, "neuronActivity": 0.5821798341932947}, "emotionalHistory": [{"timestamp": 1748366712802, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5717874634099186}, {"timestamp": 1748366727802, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0157178746341, "neuronActivity": 0.5717874634099186}, {"timestamp": 1748366742802, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0157178746341, "neuronActivity": 0.5717874634099186}, {"timestamp": 1748366757803, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03143574926821, "neuronActivity": 0.5717874634099186}, {"timestamp": 1748366772803, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03143574926821, "neuronActivity": 0.5717874634099186}, {"timestamp": 1748366787803, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04715362390232, "neuronActivity": 0.5717874634099186}, {"timestamp": 1748366802804, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04715362390232, "neuronActivity": 0.5717874634099186}, {"timestamp": 1748366817869, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06294121864744, "neuronActivity": 0.5787594745113556}, {"timestamp": 1748366832874, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06294121864744, "neuronActivity": 0.5787594745113556}, {"timestamp": 1748366847880, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07872881339256, "neuronActivity": 0.5787594745113556}, {"timestamp": 1748366862882, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07872881339256, "neuronActivity": 0.5787594745113556}, {"timestamp": 1748366877939, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09453980479054, "neuronActivity": 0.5810991397981271}, {"timestamp": 1748366893078, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09453980479054, "neuronActivity": 0.5810991397981271}, {"timestamp": 1748366908268, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11035079618853, "neuronActivity": 0.5810991397981271}, {"timestamp": 1748366923575, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11035079618853, "neuronActivity": 0.5810991397981271}, {"timestamp": 1748366939494, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12617259453046, "neuronActivity": 0.5821798341932947}, {"timestamp": 1748366956304, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12617259453046, "neuronActivity": 0.5821798341932947}, {"timestamp": 1748366972414, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14199439287239, "neuronActivity": 0.5821798341932947}, {"timestamp": 1748366989895, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14199439287239, "neuronActivity": 0.5821798341932947}, {"timestamp": 1748367014071, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14781619121432, "neuronActivity": 0.5821798341932947}, {"timestamp": 1748367036254, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14781619121432, "neuronActivity": 0.5821798341932947}, {"timestamp": 1748367073439, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15363798955624, "neuronActivity": 0.5821798341932947}, {"timestamp": 1748367126527, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15945978789817, "neuronActivity": 0.5821798341932947}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:29:49 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:30:14 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "1:30:36 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "1:31:13 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "1:32:06 PM", "mood": "curious"}], "circadianRhythm": 0.0036980570269841695, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8865124640505242, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9455815319432963, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 1, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.8613275959101002, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748366697960, "expiresAt": 1748367297960}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.9176367190805698, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748366697961, "expiresAt": 1748367297961}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.9044186285443712, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748366728254, "expiresAt": 1748367328254}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6173396737499997, "stability": 0.8865124640505242, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3380047553125003, "stability": 0.9455815319432963, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9190023776562501, "stability": 1, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8675459453124998, "stability": 0.8613275959101002, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748366697960, "expiresAt": 1748367297960}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.4556194578125, "stability": 0.9176367190805698, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748366697961, "expiresAt": 1748367297961}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7205275671875, "stability": 0.9044186285443712, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748366728254, "expiresAt": 1748367328254}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.153006629505208, "efficiency": 0.8291803977703125, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748367089294, "totalBoostPower": 12.918039777031249, "averageEnergy": 1, "averageStability": 0.9192461565881436, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748366757797, "memoryEfficiency": 0, "thermalStability": 0.877238359550635, "cpuUsage": 0.4576904296875, "responseTime": 205.42827786593824, "overall": 0.5588716655011463}, {"timestamp": 1748366817810, "memoryEfficiency": 0, "thermalStability": 0.9940291889839702, "cpuUsage": 0.4611083984375, "responseTime": 468.8786256572717, "overall": 0.5424502256006714}, {"timestamp": 1748366877811, "memoryEfficiency": 0, "thermalStability": 0.9973494800014628, "cpuUsage": 0.462158203125, "responseTime": 696.6633496514183, "overall": 0.5105751424413003}, {"timestamp": 1748366938290, "memoryEfficiency": 0, "thermalStability": 0.9983742088079453, "cpuUsage": 0.44453125, "responseTime": 413.8264015501714, "overall": 0.5636710634937832}, {"timestamp": 1748367000712, "memoryEfficiency": 0, "thermalStability": 0.9910451390677029, "cpuUsage": 0.435009765625, "responseTime": 396.4913321438907, "overall": 0.5099496189555792}, {"timestamp": 1748367089294, "memoryEfficiency": 0, "thermalStability": 0.9948674598087867, "cpuUsage": 0.416259765625, "responseTime": 410.07345730532427, "overall": 0.5145546193130233}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}