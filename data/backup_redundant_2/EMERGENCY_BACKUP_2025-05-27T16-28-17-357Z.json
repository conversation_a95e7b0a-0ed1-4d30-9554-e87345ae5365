{"timestamp": "2025-05-27T16:28:17.357Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1373227704835618, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9603999999999999, "confidence": -3.8412878003669864, "energy": 1, "focus": 0.8123748994726686, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748363265512, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1392154818194, "neuronActivity": 0.5017949271948807}, "emotionalHistory": [{"timestamp": 1748362941615, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.4828067704308657}, {"timestamp": 1748362956614, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120.01482806770431, "neuronActivity": 0.4828067704308657}, {"timestamp": 1748362971615, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01482806770431, "neuronActivity": 0.4828067704308657}, {"timestamp": 1748362986616, "mood": "curious", "dominantEmotion": "energy", "qi": 120.02965613540863, "neuronActivity": 0.4828067704308657}, {"timestamp": 1748363001617, "mood": "curious", "dominantEmotion": "energy", "qi": 120.02965613540863, "neuronActivity": 0.4828067704308657}, {"timestamp": 1748363016617, "mood": "curious", "dominantEmotion": "energy", "qi": 120.04448420311294, "neuronActivity": 0.4828067704308657}, {"timestamp": 1748363031617, "mood": "curious", "dominantEmotion": "energy", "qi": 120.04448420311294, "neuronActivity": 0.4828067704308657}, {"timestamp": 1748363046647, "mood": "curious", "dominantEmotion": "energy", "qi": 120.04448420311294, "neuronActivity": 0.4828067704308657}, {"timestamp": 1748363061658, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05938947632856, "neuronActivity": 0.4905273215610779}, {"timestamp": 1748363076681, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05938947632856, "neuronActivity": 0.4905273215610779}, {"timestamp": 1748363091682, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07429474954418, "neuronActivity": 0.4905273215610779}, {"timestamp": 1748363106719, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08922819177387, "neuronActivity": 0.4933442229695287}, {"timestamp": 1748363121781, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.08922819177387, "neuronActivity": 0.4933442229695287}, {"timestamp": 1748363136984, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10416163400356, "neuronActivity": 0.4933442229695287}, {"timestamp": 1748363152229, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10416163400356, "neuronActivity": 0.4933442229695287}, {"timestamp": 1748363167876, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11917958327551, "neuronActivity": 0.5017949271948807}, {"timestamp": 1748363184702, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.11917958327551, "neuronActivity": 0.5017949271948807}, {"timestamp": 1748363202485, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13419753254746, "neuronActivity": 0.5017949271948807}, {"timestamp": 1748363220969, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13419753254746, "neuronActivity": 0.5017949271948807}, {"timestamp": 1748363240868, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1392154818194, "neuronActivity": 0.5017949271948807}, {"timestamp": 1748363265512, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1392154818194, "neuronActivity": 0.5017949271948807}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "12:26:24 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:26:42 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "12:27:00 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:27:20 PM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "12:27:45 PM", "mood": "curious"}], "circadianRhythm": 0.03995366528147426, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9638032832309168, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9298853534377042, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9799687015156056, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8647914182991816, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748362927897, "expiresAt": 1748363527897}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9024175759707684, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748362927897, "expiresAt": 1748363527897}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8433199738201826, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748362935146, "expiresAt": 1748363535146}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9638032832309168, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.9298853534377042, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9799687015156056, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.8647914182991816, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748362927897, "expiresAt": 1748363527897}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.9024175759707684, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748362927897, "expiresAt": 1748363527897}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8433199738201826, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748362935146, "expiresAt": 1748363535146}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.1873753994791665, "efficiency": 0.83124252396875, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748363229907, "totalBoostPower": 13.124252396874999, "averageEnergy": 1, "averageStability": 0.9140310510457267, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748362986541, "memoryEfficiency": 0, "thermalStability": 0.9421321809291839, "cpuUsage": 0.4435302734375, "responseTime": 655.5735327765656, "overall": 0.4974056578031577}, {"timestamp": 1748363046644, "memoryEfficiency": 0, "thermalStability": 0.9969610008928511, "cpuUsage": 0.40576171875, "responseTime": 325.7943436801261, "overall": 0.5463719311667784}, {"timestamp": 1748363106646, "memoryEfficiency": 0, "thermalStability": 0.996321602869365, "cpuUsage": 0.4181640625, "responseTime": 519.9025558800188, "overall": 0.5628188525043215}, {"timestamp": 1748363166963, "memoryEfficiency": 0, "thermalStability": 0.9878512831611765, "cpuUsage": 0.43916015625, "responseTime": 247.5634696249495, "overall": 0.593525009418534}, {"timestamp": 1748363229907, "memoryEfficiency": 0, "thermalStability": 0.9895205421580209, "cpuUsage": 0.4349853515625, "responseTime": 367.4292831279993, "overall": 0.5395744603160583}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}