{"timestamp": "2025-05-27T02:38:56.924Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2514213597149346, "accessFactor": 1.05, "decayFactor": 0.9847658221496475, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.4041713006296227, "curiosity": 1, "confidence": -0.22086456686215952, "energy": 1, "focus": 0.9093116221343371, "creativity": 1, "stress": 0.3777476874993149, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748313531742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01562290666739, "neuronActivity": 0.5622906667382688}, "emotionalHistory": [{"timestamp": 1748313501738, "mood": "curious", "dominantEmotion": "creativity", "qi": 120, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313516741, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01562290666739, "neuronActivity": 0.5622906667382688}, {"timestamp": 1748313531742, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01562290666739, "neuronActivity": 0.5622906667382688}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "10:38:21 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:38:36 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "10:38:51 PM", "mood": "curious"}], "circadianRhythm": 0.8194885837114931, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9349948873115824, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.8825810323975386, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9291629456630606, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748313487961, "expiresAt": 1748314087961}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748313487961, "expiresAt": 1748314087961}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748313497052, "expiresAt": 1748314097052}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9349948873115824, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.8825810323975386, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9291629456630606, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748313487961, "expiresAt": 1748314087961}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748313487961, "expiresAt": 1748314087961}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748313497052, "expiresAt": 1748314097052}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.5766666666666667, "efficiency": 0.8545999999999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748313486624, "totalBoostPower": 14.330000000000002, "averageEnergy": 500.5, "averageStability": 0.9077898108953636, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}