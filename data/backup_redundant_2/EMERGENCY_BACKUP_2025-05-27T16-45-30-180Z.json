{"timestamp": "2025-05-27T16:45:30.180Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10563958327009935, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 0.9039207967999998, "confidence": -4.274165988708899, "energy": 1, "focus": 0.9315528271672019, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748364243970, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1595389331699, "neuronActivity": 0.5376514086633721}, "emotionalHistory": [{"timestamp": 1748363729477, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363744478, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01527798966126, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363759478, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01527798966126, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363774478, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03055597932251, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363789479, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03055597932251, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363804480, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04583396898377, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363819482, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04583396898377, "neuronActivity": 0.5277989661250958}, {"timestamp": 1748363834482, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06118839664683, "neuronActivity": 0.5354427663054926}, {"timestamp": 1748363849487, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06118839664683, "neuronActivity": 0.5354427663054926}, {"timestamp": 1748363864487, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07654282430988, "neuronActivity": 0.5354427663054926}, {"timestamp": 1748363879513, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07654282430988, "neuronActivity": 0.5354427663054926}, {"timestamp": 1748363894513, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09191133647998, "neuronActivity": 0.536851217009718}, {"timestamp": 1748363909699, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.09191133647998, "neuronActivity": 0.536851217009718}, {"timestamp": 1748363924979, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10727984865008, "neuronActivity": 0.536851217009718}, {"timestamp": 1748363940563, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10727984865008, "neuronActivity": 0.536851217009718}, {"timestamp": 1748363956096, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12265636273672, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748363973239, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12265636273672, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748363989980, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13803287682336, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748364009558, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.13803287682336, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748364031627, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14340939091, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748364053906, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14340939091, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748364101521, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.14878590499663, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748364170129, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.15416241908326, "neuronActivity": 0.5376514086633721}, {"timestamp": 1748364243970, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.1595389331699, "neuronActivity": 0.5376514086633721}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "12:40:31 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:40:53 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:41:41 PM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "12:42:50 PM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "12:44:03 PM", "mood": "curious"}], "circadianRhythm": 0.027193735074725667, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5614726900625, "stability": 0.8440859635952129, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2961045175468753, "stability": 0.7202024316279192, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8980522587734376, "stability": 0.9278711829480772, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8491686480468748, "stability": 1, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748363715258, "expiresAt": 1748364315258}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.407838484921875, "stability": 0.8969200987870595, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748363715258, "expiresAt": 1748364315258}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.709501188828125, "stability": 0.9621498841881172, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748363724676, "expiresAt": 1748364324676}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.5614726900625, "stability": 0.8440859635952129, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.2961045175468753, "stability": 0.7202024316279192, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.8980522587734376, "stability": 0.9278711829480772, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8491686480468748, "stability": 1, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748363715258, "expiresAt": 1748364315258}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.407838484921875, "stability": 0.8969200987870595, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748363715258, "expiresAt": 1748364315258}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.709501188828125, "stability": 0.9621498841881172, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748363724676, "expiresAt": 1748364324676}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.120356298029948, "efficiency": 0.8272213778817968, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748364243401, "totalBoostPower": 12.722137788179687, "averageEnergy": 1, "averageStability": 0.8918715935243977, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748363774448, "memoryEfficiency": 0, "thermalStability": 0.8073397926158375, "cpuUsage": 0.5108642578125, "responseTime": 581.7920139423991, "overall": 0.508675084345393}, {"timestamp": 1748363834452, "memoryEfficiency": 0, "thermalStability": 0.950465110813578, "cpuUsage": 0.4595458984375, "responseTime": 295.7714364415271, "overall": 0.5173028909756264}, {"timestamp": 1748363894469, "memoryEfficiency": 0, "thermalStability": 0.9870677244746022, "cpuUsage": 0.5223876953125, "responseTime": 670.2612940549245, "overall": 0.5330917338949828}, {"timestamp": 1748363954932, "memoryEfficiency": 0, "thermalStability": 0.9856216669082641, "cpuUsage": 0.508642578125, "responseTime": 382.33882904910683, "overall": 0.5162556029378876}, {"timestamp": 1748364019272, "memoryEfficiency": 0, "thermalStability": 0.9959601735489236, "cpuUsage": 0.531591796875, "responseTime": 648.7434531022824, "overall": 0.5953866444937437}, {"timestamp": 1748364119767, "memoryEfficiency": 0, "thermalStability": 0.994819115805957, "cpuUsage": 0.51826171875, "responseTime": 238.9637549919888, "overall": 0.5880206116106131}, {"timestamp": 1748364243401, "memoryEfficiency": 0, "thermalStability": 0.9949687795506583, "cpuUsage": 0.5398193359375, "responseTime": 684.967414284391, "overall": 0.5097996583585015}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}