{"timestamp": "2025-05-27T07:44:32.017Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.1, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2283809849518472, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -1.6537450638218865, "energy": 1, "focus": 0.9935978852838503, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748331866974, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05963295325708, "neuronActivity": 0.497161859596081}, "emotionalHistory": [{"timestamp": 1748331746956, "mood": "curious", "dominantEmotion": "energy", "qi": 120, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331761957, "mood": "curious", "dominantEmotion": "energy", "qi": 120.0148871115537, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331776958, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0148871115537, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331791959, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02977422310741, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331806959, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.02977422310741, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331821959, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04466133466111, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331836972, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04466133466111, "neuronActivity": 0.48871115537072873}, {"timestamp": 1748331851972, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05963295325708, "neuronActivity": 0.497161859596081}, {"timestamp": 1748331866974, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.05963295325708, "neuronActivity": 0.497161859596081}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "3:43:26 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "3:43:41 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "3:43:56 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "3:44:11 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "3:44:26 AM", "mood": "curious"}], "circadianRhythm": 0.9489662033730994, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.9427385591527683, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.9040411572360786, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.9717782800871408, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.873290608977871, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748331733043, "expiresAt": 1748332333043}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.9361555487786519, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748331733043, "expiresAt": 1748332333043}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.9101256516082982, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748331737090, "expiresAt": 1748332337090}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.9427385591527683, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.9040411572360786, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.9717782800871408, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.873290608977871, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748331733043, "expiresAt": 1748332333043}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.9361555487786519, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748331733043, "expiresAt": 1748332333043}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.9101256516082982, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748331737090, "expiresAt": 1748332337090}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.3017208333333334, "efficiency": 0.83810325, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748331851883, "totalBoostPower": 13.810325, "averageEnergy": 1, "averageStability": 0.9230216343068015, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748331791883, "memoryEfficiency": 0, "thermalStability": 0.9497669412030114, "cpuUsage": 0.4538330078125, "responseTime": 234.4386709008192, "overall": 0.555218749455438}, {"timestamp": 1748331851883, "memoryEfficiency": 0, "thermalStability": 0.9957419801089499, "cpuUsage": 0.4263916015625, "responseTime": 453.74792961246203, "overall": 0.5192259367712407}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}