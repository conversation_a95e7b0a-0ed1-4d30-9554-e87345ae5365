{"timestamp": "2025-05-27T15:24:02.674Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 1, "memoryDecayRate": 0.99, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1785754204741166, "accessFactor": 1.05, "decayFactor": 0.99, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10459105791450644, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -3.529896055091965, "energy": 1, "focus": 0.9166521784792795, "creativity": 1, "stress": 1, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748359428133, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.136152485763, "neuronActivity": 0.5238750063918844}, "emotionalHistory": [{"timestamp": 1748359149786, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5060106396065727}, {"timestamp": 1748359164785, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01506010639606, "neuronActivity": 0.5060106396065727}, {"timestamp": 1748359179786, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01506010639606, "neuronActivity": 0.5060106396065727}, {"timestamp": 1748359194786, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03012021279213, "neuronActivity": 0.5060106396065727}, {"timestamp": 1748359209787, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03012021279213, "neuronActivity": 0.5060106396065727}, {"timestamp": 1748359224786, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04518031918819, "neuronActivity": 0.5060106396065727}, {"timestamp": 1748359239792, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04518031918819, "neuronActivity": 0.5060106396065727}, {"timestamp": 1748359254966, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06024042558425, "neuronActivity": 0.5060106396065727}, {"timestamp": 1748359269967, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.06024042558425, "neuronActivity": 0.5126074007580816}, {"timestamp": 1748359284967, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07536649959184, "neuronActivity": 0.5126074007580816}, {"timestamp": 1748359299968, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.07536649959184, "neuronActivity": 0.5126074007580816}, {"timestamp": 1748359314993, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0905207426135, "neuronActivity": 0.5154243021665323}, {"timestamp": 1748359330060, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0905207426135, "neuronActivity": 0.5154243021665323}, {"timestamp": 1748359345080, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10567498563518, "neuronActivity": 0.5154243021665323}, {"timestamp": 1748359360371, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.10567498563518, "neuronActivity": 0.5154243021665323}, {"timestamp": 1748359376488, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12091373569909, "neuronActivity": 0.5238750063918844}, {"timestamp": 1748359393959, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.12091373569909, "neuronActivity": 0.5238750063918844}, {"timestamp": 1748359409707, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.136152485763, "neuronActivity": 0.5238750063918844}, {"timestamp": 1748359428133, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.136152485763, "neuronActivity": 0.5238750063918844}], "currentThoughts": [{"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:22:40 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:22:56 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:23:13 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "11:23:29 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "11:23:48 AM", "mood": "curious"}], "circadianRhythm": 0.11169871335193893, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9548125063224955, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7839792041991894, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9705204142086973, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9606893548043283, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748359135661, "expiresAt": 1748359735661}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8671202480190243, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748359135661, "expiresAt": 1748359735661}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8775601170496453, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748359167804, "expiresAt": 1748359767804}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.6761470249999997, "stability": 0.9548125063224955, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.3821102687500004, "stability": 0.7839792041991894, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9410551343750002, "stability": 0.9705204142086973, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.8868904687499999, "stability": 0.9606893548043283, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748359135661, "expiresAt": 1748359735661}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.50591521875, "stability": 0.8671202480190243, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748359135661, "expiresAt": 1748359735661}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.73213428125, "stability": 0.8775601170496453, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748359167804, "expiresAt": 1748359767804}}, "totalAccelerators": 6, "activeAccelerators": 6, "averageBoostFactor": 2.1873753994791665, "efficiency": 0.83124252396875, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748359438821, "totalBoostPower": 13.124252396874999, "averageEnergy": 1, "averageStability": 0.9024469741005635, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748359194728, "memoryEfficiency": 0, "thermalStability": 0.9853027918272548, "cpuUsage": 0.38310546875, "responseTime": 363.2285052417882, "overall": 0.5057474990359684}, {"timestamp": 1748359254965, "memoryEfficiency": 0, "thermalStability": 0.9991364299009243, "cpuUsage": 0.3610107421875, "responseTime": 658.06031045933, "overall": 0.589814355560986}, {"timestamp": 1748359314991, "memoryEfficiency": 0, "thermalStability": 0.9948056418448686, "cpuUsage": 0.431103515625, "responseTime": 316.4692830081059, "overall": 0.578079655040071}, {"timestamp": 1748359375715, "memoryEfficiency": 0, "thermalStability": 0.9966231351097424, "cpuUsage": 0.446533203125, "responseTime": 598.9746799196253, "overall": 0.52377681752236}, {"timestamp": 1748359438821, "memoryEfficiency": 0, "thermalStability": 0.9989416051242087, "cpuUsage": 0.4515869140625, "responseTime": 560.3023828579976, "overall": 0.5659658254654667}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}