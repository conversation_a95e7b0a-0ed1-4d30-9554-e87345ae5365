{"temperatureHistory": [{"timestamp": 1748334124269, "temperatures": {"cpu": 0, "gpu": 0, "memory": 0, "battery": 0, "average": 0, "timestamp": 1748334124267, "normalized": 0}, "normalizedTemperature": 0}, {"timestamp": 1748334129478, "temperatures": {"cpu": 37.9150390625, "gpu": 41.70654296875, "memory": 30.33203125, "battery": 26.54052734375, "average": 34.12353515625, "timestamp": 1748334129272, "normalized": 0.13191731770833334}, "normalizedTemperature": 0.13191731770833334}, {"timestamp": 1748334131480, "temperatures": {"cpu": 37.9150390625, "gpu": 41.70654296875, "memory": 30.33203125, "battery": 26.54052734375, "average": 34.12353515625, "timestamp": 1748334129272, "normalized": 0.13191731770833334}, "normalizedTemperature": 0.13191731770833334}, {"timestamp": 1748334140528, "temperatures": {"cpu": 38.6328125, "gpu": 42.49609375, "memory": 30.90625, "battery": 27.04296875, "average": 34.76953125, "timestamp": 1748334139277, "normalized": 0.14388020833333334}, "normalizedTemperature": 0.14388020833333334}, {"timestamp": 1748334149536, "temperatures": {"cpu": 39.94384765625, "gpu": 43.938232421875, "memory": 31.955078125, "battery": 27.960693359375, "average": 35.949462890625, "timestamp": 1748334149282, "normalized": 0.16573079427083334}, "normalizedTemperature": 0.16573079427083334}, {"timestamp": 1748334151538, "temperatures": {"cpu": 39.94384765625, "gpu": 43.938232421875, "memory": 31.955078125, "battery": 27.960693359375, "average": 35.949462890625, "timestamp": 1748334149282, "normalized": 0.16573079427083334}, "normalizedTemperature": 0.16573079427083334}, {"timestamp": 1748334154290, "temperatures": {"cpu": 39.94384765625, "gpu": 43.938232421875, "memory": 31.955078125, "battery": 27.960693359375, "average": 35.949462890625, "timestamp": 1748334149282, "normalized": 0.16573079427083334}, "normalizedTemperature": 0.16573079427083334}], "memoryStats": [{"timestamp": 1748334124269, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 0, "lastCycleTime": "<PERSON><PERSON>", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 0, "gpu": 0, "memory": 0, "average": 0, "normalized": 0}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748334129478, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 0, "lastCycleTime": "5/27/2025, 4:22:07 AM", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 37.9150390625, "gpu": 41.70654296875, "memory": 30.33203125, "average": 34.12353515625, "normalized": 0.13191731770833334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.1, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748334131480, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 1, "lastCycleTime": "5/27/2025, 4:22:09 AM", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 37.9150390625, "gpu": 41.70654296875, "memory": 30.33203125, "average": 34.12353515625, "normalized": 0.13191731770833334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748334140528, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 2, "lastCycleTime": "5/27/2025, 4:22:11 AM", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 38.6328125, "gpu": 42.49609375, "memory": 30.90625, "average": 34.76953125, "normalized": 0.14388020833333334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.1, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748334149536, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 3, "lastCycleTime": "5/27/2025, 4:22:20 AM", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 39.94384765625, "gpu": 43.938232421875, "memory": 31.955078125, "average": 35.949462890625, "normalized": 0.16573079427083334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.1, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748334151538, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 4, "lastCycleTime": "5/27/2025, 4:22:29 AM", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 39.94384765625, "gpu": 43.938232421875, "memory": 31.955078125, "average": 35.949462890625, "normalized": 0.16573079427083334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}, {"timestamp": 1748334154290, "stats": {"totalMemories": 0, "zone1Count": 0, "zone2Count": 0, "zone3Count": 0, "zone4Count": 0, "zone5Count": 0, "zone6Count": 0, "averageTemperature": 0, "cyclesPerformed": 5, "lastCycleTime": "5/27/2025, 4:22:31 AM", "temperatureCursor": 0.5, "temperatureThresholds": {"instant": 0.8, "shortTerm": 0.65, "working": 0.5, "mediumTerm": 0.35, "longTerm": 0.2}, "systemTemperatures": {"cpu": 39.94384765625, "gpu": 43.938232421875, "memory": 31.955078125, "average": 35.949462890625, "normalized": 0.16573079427083334}, "useRealTemperatures": true, "autoEvolution": {"enabled": true, "adaptationEnabled": true, "performanceMetrics": {"memoryEfficiency": 0, "temperatureStability": 0, "accessOptimization": 0, "overallPerformance": 0}, "parameters": {"temperatureCursorSensitivity": 0.1, "memoryDecayRate": 0.95, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98}}}}], "parameterHistory": [], "optimizationHistory": [], "insights": {}, "lastOptimization": null, "cyclesSinceOptimization": 7, "version": 1}