{"timestamp": "2025-05-27T16:27:11.293Z", "performanceHistory": [{"timestamp": 1748362986541, "memoryEfficiency": 0, "thermalStability": 0.9421321809291839, "cpuUsage": 0.4435302734375, "responseTime": 655.5735327765656, "overall": 0.4974056578031577}, {"timestamp": 1748363046644, "memoryEfficiency": 0, "thermalStability": 0.9969610008928511, "cpuUsage": 0.40576171875, "responseTime": 325.7943436801261, "overall": 0.5463719311667784}, {"timestamp": 1748363106646, "memoryEfficiency": 0, "thermalStability": 0.996321602869365, "cpuUsage": 0.4181640625, "responseTime": 519.9025558800188, "overall": 0.5628188525043215}, {"timestamp": 1748363166963, "memoryEfficiency": 0, "thermalStability": 0.9878512831611765, "cpuUsage": 0.43916015625, "responseTime": 247.5634696249495, "overall": 0.593525009418534}, {"timestamp": 1748363229907, "memoryEfficiency": 0, "thermalStability": 0.9895205421580209, "cpuUsage": 0.4349853515625, "responseTime": 367.4292831279993, "overall": 0.5395744603160583}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}