{"timestamp": "2025-05-27T17:58:54.462Z", "performanceHistory": [{"timestamp": 1748367912311, "memoryEfficiency": 0, "thermalStability": 0.9268438370691405, "cpuUsage": 0.4360595703125, "responseTime": 201.23997148014865, "overall": 0.5317002173558383}, {"timestamp": 1748367972315, "memoryEfficiency": 0, "thermalStability": 0.9856907948851585, "cpuUsage": 0.4052001953125, "responseTime": 446.39640569953394, "overall": 0.5310432195568974}, {"timestamp": 1748368032321, "memoryEfficiency": 0, "thermalStability": 0.9954431002752648, "cpuUsage": 0.3687255859375, "responseTime": 644.7961331326834, "overall": 0.563140081007047}, {"timestamp": 1748368093213, "memoryEfficiency": 0, "thermalStability": 0.998705892968509, "cpuUsage": 0.393017578125, "responseTime": 660.999259946087, "overall": 0.539962472481261}, {"timestamp": 1748368156008, "memoryEfficiency": 0, "thermalStability": 0.9981742702010605, "cpuUsage": 0.40390625, "responseTime": 637.9825481236799, "overall": 0.5286457040803392}, {"timestamp": 1748368254603, "memoryEfficiency": 0, "thermalStability": 0.9959889331211647, "cpuUsage": 0.413427734375, "responseTime": 611.9181923681219, "overall": 0.5324035564498844}, {"timestamp": 1748368381948, "memoryEfficiency": 0, "thermalStability": 0.9950480131639374, "cpuUsage": 0.43251953125, "responseTime": 285.8277097170849, "overall": 0.5903655787549691}, {"timestamp": 1748368733165, "memoryEfficiency": 0, "thermalStability": 0.9924943041884237, "cpuUsage": 0.4241943359375, "responseTime": 548.8197851720173, "overall": 0.5451780568923263}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}