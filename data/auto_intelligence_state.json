{"timestamp": "2025-05-27T20:13:56.934Z", "performanceHistory": [{"timestamp": 1748376588352, "memoryEfficiency": 0, "thermalStability": 0.8888044317563375, "cpuUsage": 0.494921875, "responseTime": 493.59455815045123, "overall": 0.49238976191726214}, {"timestamp": 1748376648416, "memoryEfficiency": 0, "thermalStability": 0.9833253141906526, "cpuUsage": 0.4902587890625, "responseTime": 268.53202477983467, "overall": 0.5215895494488889}, {"timestamp": 1748376708448, "memoryEfficiency": 0, "thermalStability": 0.980882965400815, "cpuUsage": 0.4868408203125, "responseTime": 564.7308691739015, "overall": 0.572677566184669}, {"timestamp": 1748376769389, "memoryEfficiency": 0, "thermalStability": 0.9943927223897643, "cpuUsage": 0.4745361328125, "responseTime": 443.79163292344407, "overall": 0.5796036628428087}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}