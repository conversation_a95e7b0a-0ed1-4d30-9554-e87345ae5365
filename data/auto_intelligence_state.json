{"timestamp": "2025-05-27T22:20:51.566Z", "performanceHistory": [{"timestamp": 1748384211567, "memoryEfficiency": 0, "thermalStability": 0.8526472705933783, "cpuUsage": 0.5550537109375, "responseTime": 267.0048695052655, "overall": 0.4691868053737573}, {"timestamp": 1748384271702, "memoryEfficiency": 0, "thermalStability": 0.984465556177828, "cpuUsage": 0.4775390625, "responseTime": 609.6302310817887, "overall": 0.5365800203117519}, {"timestamp": 1748384331702, "memoryEfficiency": 0, "thermalStability": 0.9822895189126333, "cpuUsage": 0.5332275390625, "responseTime": 288.5445363995908, "overall": 0.5680496348712932}, {"timestamp": 1748384391703, "memoryEfficiency": 0, "thermalStability": 0.9932084367093112, "cpuUsage": 0.5158935546875, "responseTime": 326.5632968233772, "overall": 0.5641259873072744}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}