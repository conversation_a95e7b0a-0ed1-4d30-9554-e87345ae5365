{"timestamp": "2025-05-27T15:11:58.094Z", "performanceHistory": [{"timestamp": 1748358474383, "memoryEfficiency": 0, "thermalStability": 0.9735538130005201, "cpuUsage": 0.3990234375, "responseTime": 298.8138021360911, "overall": 0.5475473121346082}, {"timestamp": 1748358534447, "memoryEfficiency": 0, "thermalStability": 0.9967928405023283, "cpuUsage": 0.4271728515625, "responseTime": 482.59878182175447, "overall": 0.5105378428574934}, {"timestamp": 1748358594465, "memoryEfficiency": 0, "thermalStability": 0.9976250428292487, "cpuUsage": 0.458544921875, "responseTime": 417.9412992877503, "overall": 0.5426492627338615}, {"timestamp": 1748358655166, "memoryEfficiency": 0, "thermalStability": 0.9818473966999186, "cpuUsage": 0.451904296875, "responseTime": 582.4847011761824, "overall": 0.5221276003947597}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}