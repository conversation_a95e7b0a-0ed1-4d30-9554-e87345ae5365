{"timestamp": "2025-05-27T21:35:27.326Z", "performanceHistory": [{"timestamp": 1748381187330, "memoryEfficiency": 0, "thermalStability": 0.9711424383852217, "cpuUsage": 0.385107421875, "responseTime": 328.006388194306, "overall": 0.5120519175042839}, {"timestamp": 1748381247431, "memoryEfficiency": 0, "thermalStability": 0.9985481376449267, "cpuUsage": 0.438330078125, "responseTime": 364.9862109309912, "overall": 0.5757590259216612}, {"timestamp": 1748381307431, "memoryEfficiency": 0, "thermalStability": 0.962070638479458, "cpuUsage": 0.44853515625, "responseTime": 649.6058108918651, "overall": 0.557055439159982}, {"timestamp": 1748381367432, "memoryEfficiency": 0, "thermalStability": 0.9844880784965224, "cpuUsage": 0.3978759765625, "responseTime": 643.589500704964, "overall": 0.5172733459158433}, {"timestamp": 1748381427432, "memoryEfficiency": 0, "thermalStability": 0.9960241004824638, "cpuUsage": 0.3744873046875, "responseTime": 518.6300214923406, "overall": 0.5468912080144305}, {"timestamp": 1748381487432, "memoryEfficiency": 0, "thermalStability": 0.9971462119370699, "cpuUsage": 0.3802490234375, "responseTime": 363.63265523383166, "overall": 0.5566403229921639}, {"timestamp": 1748381547433, "memoryEfficiency": 0, "thermalStability": 0.9974220214618577, "cpuUsage": 0.389892578125, "responseTime": 335.36723888760605, "overall": 0.5193036920902575}, {"timestamp": 1748381607433, "memoryEfficiency": 0, "thermalStability": 0.9978728524512714, "cpuUsage": 0.3882080078125, "responseTime": 596.3951832035897, "overall": 0.581407712113672}, {"timestamp": 1748381667434, "memoryEfficiency": 0, "thermalStability": 0.9980174890822835, "cpuUsage": 0.3814453125, "responseTime": 598.995127289624, "overall": 0.5809793613256}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}