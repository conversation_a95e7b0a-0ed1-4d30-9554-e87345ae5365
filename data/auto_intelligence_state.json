{"timestamp": "2025-05-27T21:56:36.321Z", "performanceHistory": [{"timestamp": 1748382756324, "memoryEfficiency": 0, "thermalStability": 0.9051898287402259, "cpuUsage": 0.4773193359375, "responseTime": 373.622438917923, "overall": 0.5231792719370685}, {"timestamp": 1748382816416, "memoryEfficiency": 0, "thermalStability": 0.9819729069868723, "cpuUsage": 0.4534423828125, "responseTime": 304.687924830723, "overall": 0.5431147914810328}, {"timestamp": 1748382876417, "memoryEfficiency": 0, "thermalStability": 0.9986974199612936, "cpuUsage": 0.48662109375, "responseTime": 225.3695735952876, "overall": 0.5811453086809}, {"timestamp": 1748382936429, "memoryEfficiency": 0, "thermalStability": 0.9921691972762346, "cpuUsage": 0.4353271484375, "responseTime": 581.5954575939603, "overall": 0.5814228362173965}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}