{"timestamp": "2025-05-27T20:56:46.241Z", "performanceHistory": [{"timestamp": 1748379159588, "memoryEfficiency": 0, "thermalStability": 0.9721102838714918, "cpuUsage": 0.3916015625, "responseTime": 341.15355678859896, "overall": 0.5535006972488269}, {"timestamp": 1748379219690, "memoryEfficiency": 0, "thermalStability": 0.9971799763126505, "cpuUsage": 0.3630859375, "responseTime": 622.5614682432276, "overall": 0.5682565087555063}, {"timestamp": 1748379279691, "memoryEfficiency": 0, "thermalStability": 0.9985231991029448, "cpuUsage": 0.390673828125, "responseTime": 211.67687455256473, "overall": 0.5650217006808677}, {"timestamp": 1748379339695, "memoryEfficiency": 0, "thermalStability": 0.9894802694933282, "cpuUsage": 0.4498779296875, "responseTime": 253.62441942509022, "overall": 0.537840444533088}, {"timestamp": 1748379402517, "memoryEfficiency": 0, "thermalStability": 0.9894731990165181, "cpuUsage": 0.432958984375, "responseTime": 365.88059622984963, "overall": 0.5202561483531243}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}