{"timestamp": "2025-05-27T22:03:57.425Z", "performanceHistory": [{"timestamp": 1748383197397, "memoryEfficiency": 0, "thermalStability": 0.9304482201735179, "cpuUsage": 0.4200927734375, "responseTime": 311.65983536212167, "overall": 0.5296474632414128}, {"timestamp": 1748383258095, "memoryEfficiency": 0, "thermalStability": 0.9952741415964232, "cpuUsage": 0.42587890625, "responseTime": 460.2139047055403, "overall": 0.5637275136449971}, {"timestamp": 1748383318096, "memoryEfficiency": 0, "thermalStability": 0.9956332080894046, "cpuUsage": 0.4313720703125, "responseTime": 577.0216695438471, "overall": 0.5384015859361007}, {"timestamp": 1748383378096, "memoryEfficiency": 0, "thermalStability": 0.9984597283932898, "cpuUsage": 0.449365234375, "responseTime": 394.26388476892686, "overall": 0.5206676376965169}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}