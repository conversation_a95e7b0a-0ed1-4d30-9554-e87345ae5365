{"timestamp": "2025-05-27T21:41:39.863Z", "performanceHistory": [{"timestamp": 1748381859864, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.408740234375, "responseTime": 440.2689708556841, "overall": 0}, {"timestamp": 1748381919951, "memoryEfficiency": 0, "thermalStability": 0.9819684083814975, "cpuUsage": 0.3875732421875, "responseTime": 574.7756139003006, "overall": 0.5657593892870885}, {"timestamp": 1748381979952, "memoryEfficiency": 0, "thermalStability": 0.9989050198346376, "cpuUsage": 0.3720703125, "responseTime": 363.9937291289133, "overall": 0.5227962122920906}, {"timestamp": 1748382039972, "memoryEfficiency": 0, "thermalStability": 0.9897156949258513, "cpuUsage": 0.4458984375, "responseTime": 699.1247873401499, "overall": 0.5081357556138171}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}