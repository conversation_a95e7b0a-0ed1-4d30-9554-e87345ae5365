{"timestamp": "2025-05-27T22:09:38.915Z", "performanceHistory": [{"timestamp": 1748383538916, "memoryEfficiency": 0, "thermalStability": 0.868992461098565, "cpuUsage": 0.4677490234375, "responseTime": 432.62310433317936, "overall": 0.5423221731559633}, {"timestamp": 1748383598997, "memoryEfficiency": 0, "thermalStability": 0.9880999934756094, "cpuUsage": 0.471923828125, "responseTime": 641.3760642387574, "overall": 0.5104884214767792}, {"timestamp": 1748383658998, "memoryEfficiency": 0, "thermalStability": 0.9957037659568919, "cpuUsage": 0.47724609375, "responseTime": 289.630607161136, "overall": 0.5377354164483672}, {"timestamp": 1748383718997, "memoryEfficiency": 0, "thermalStability": 0.8760433778580692, "cpuUsage": 0.55, "responseTime": 281.5449631445776, "overall": 0.5310489097926607}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}