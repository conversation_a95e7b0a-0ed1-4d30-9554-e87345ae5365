{"timestamp": "2025-05-27T20:36:59.412Z", "performanceHistory": [{"timestamp": 1748377969897, "memoryEfficiency": 0, "thermalStability": 0.9768811557028029, "cpuUsage": 0.3951171875, "responseTime": 654.7013339600883, "overall": 0.5320797035348168}, {"timestamp": 1748378029901, "memoryEfficiency": 0, "thermalStability": 0.998506512078974, "cpuUsage": 0.4138427734375, "responseTime": 482.1329359005477, "overall": 0.5121984104491627}, {"timestamp": 1748378089986, "memoryEfficiency": 0, "thermalStability": 0.9853436972118086, "cpuUsage": 0.463330078125, "responseTime": 692.0953186120685, "overall": 0.574188148284671}, {"timestamp": 1748378151032, "memoryEfficiency": 0, "thermalStability": 0.9963565321846141, "cpuUsage": 0.4635498046875, "responseTime": 692.1360572593758, "overall": 0.5222771904332489}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}