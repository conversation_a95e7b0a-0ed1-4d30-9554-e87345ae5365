{"timestamp": "2025-05-27T21:20:47.948Z", "performanceHistory": [{"timestamp": 1748380307998, "memoryEfficiency": 0, "thermalStability": 0.9688550770282746, "cpuUsage": 0.3863525390625, "responseTime": 470.12744633679574, "overall": 0.5690651047033419}, {"timestamp": 1748380368042, "memoryEfficiency": 0, "thermalStability": 0.9986741666992506, "cpuUsage": 0.3832275390625, "responseTime": 520.6038214000415, "overall": 0.5289460990261297}, {"timestamp": 1748380428045, "memoryEfficiency": 0, "thermalStability": 0.9984553338752853, "cpuUsage": 0.3978759765625, "responseTime": 466.27706000226084, "overall": 0.5933941493339723}, {"timestamp": 1748380488047, "memoryEfficiency": 0, "thermalStability": 0.994454772149523, "cpuUsage": 0.3681396484375, "responseTime": 523.8481369858516, "overall": 0.5427633233990244}, {"timestamp": 1748380548047, "memoryEfficiency": 0, "thermalStability": 0.996780447040995, "cpuUsage": 0.38251953125, "responseTime": 387.1212647648691, "overall": 0.5451507174626606}, {"timestamp": 1748380608048, "memoryEfficiency": 0, "thermalStability": 0.9974857709887955, "cpuUsage": 0.37109375, "responseTime": 622.3021742959799, "overall": 0.5686937725675392}, {"timestamp": 1748380668050, "memoryEfficiency": 0, "thermalStability": 0.997417248123222, "cpuUsage": 0.3612548828125, "responseTime": 477.3998884053255, "overall": 0.5119758316274947}, {"timestamp": 1748380728050, "memoryEfficiency": 0, "thermalStability": 0.996962861220042, "cpuUsage": 0.36376953125, "responseTime": 549.226475888642, "overall": 0.5200667313911439}, {"timestamp": 1748380788052, "memoryEfficiency": 0, "thermalStability": 0.9967925610227717, "cpuUsage": 0.43037109375, "responseTime": 209.38711396149407, "overall": 0.5164947131120187}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}