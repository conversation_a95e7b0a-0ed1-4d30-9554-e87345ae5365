{"timestamp": "2025-05-27T21:51:20.962Z", "performanceHistory": [{"timestamp": 1748382440966, "memoryEfficiency": 0, "thermalStability": 0.9543182285295593, "cpuUsage": 0.4205078125, "responseTime": 522.7074783240889, "overall": 0.5447641016770324}, {"timestamp": 1748382501039, "memoryEfficiency": 0, "thermalStability": 0.9937971853547626, "cpuUsage": 0.45615234375, "responseTime": 678.113829763041, "overall": 0.5810088152861077}, {"timestamp": 1748382561039, "memoryEfficiency": 0, "thermalStability": 0.9966660160157416, "cpuUsage": 0.41943359375, "responseTime": 406.8247736689459, "overall": 0.5673785405327973}, {"timestamp": 1748382621038, "memoryEfficiency": 0, "thermalStability": 0.9986325047496293, "cpuUsage": 0.385693359375, "responseTime": 230.12716302104513, "overall": 0.5887680035069192}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}