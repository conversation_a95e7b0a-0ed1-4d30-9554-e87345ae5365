{"timestamp": "2025-05-27T19:55:31.483Z", "performanceHistory": [{"timestamp": 1748375486455, "memoryEfficiency": 0, "thermalStability": 0.1854454947842492, "cpuUsage": 0.68046875, "responseTime": 642.1057956303184, "overall": 0.2919899495917342}, {"timestamp": 1748375546457, "memoryEfficiency": 0, "thermalStability": 0.6784620396378968, "cpuUsage": 0.5619140625, "responseTime": 350.40141813325033, "overall": 0.43729269028409357}, {"timestamp": 1748375606539, "memoryEfficiency": 0, "thermalStability": 0.942667943570349, "cpuUsage": 0.4713134765625, "responseTime": 596.0379189862397, "overall": 0.507849230477246}, {"timestamp": 1748375666914, "memoryEfficiency": 0, "thermalStability": 0.9645979344844818, "cpuUsage": 0.4967529296875, "responseTime": 450.4061476339608, "overall": 0.5709875788388191}], "autoAccelerators": [["cpu_accelerator", {"name": "Accélérateur CPU Auto", "boost": 1.3, "target": "cpu", "algorithm": "parallel_processing", "efficiency": 0.9, "createdAt": "2025-05-24T13:29:36.783Z", "autoGenerated": true}], ["memory_optimizer", {"name": "Optimiseur Mémoire Auto", "boost": 1.5, "target": "memory", "algorithm": "adaptive_compression", "efficiency": 0.85, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}], ["thermal_stabilizer", {"name": "Stabilisateur Thermique Auto", "boost": 1.2, "target": "thermal", "algorithm": "temperature_balancing", "efficiency": 0.75, "createdAt": "2025-05-24T04:30:04.014Z", "autoGenerated": true}]], "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}