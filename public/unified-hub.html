<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Hub Central Intelligent</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/css/louna-unified-theme.css?v=2025">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
        }

        .hub-container {
            display: grid;
            grid-template-areas:
                "sidebar main-content right-panel"
                "sidebar main-content right-panel";
            grid-template-columns: 280px 1fr 320px;
            grid-template-rows: 1fr;
            height: 100vh;
            gap: 0;
        }

        /* Sidebar Navigation */
        .sidebar {
            grid-area: sidebar;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            overflow-y: auto;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo-section h1 {
            font-size: 28px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 5px;
        }

        .logo-section .subtitle {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-section {
            margin-bottom: 25px;
        }

        .nav-section h3 {
            font-size: 14px;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 15px;
            padding-left: 10px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 5px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .nav-item i {
            width: 20px;
            margin-right: 12px;
            font-size: 16px;
        }

        .nav-item span {
            font-size: 14px;
            font-weight: 500;
        }

        .nav-item .badge {
            margin-left: auto;
            background: #ff6b6b;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* Main Content Area */
        .main-content {
            grid-area: main-content;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.02);
            position: relative;
        }

        .content-header {
            padding: 20px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.2);
        }

        .content-header h2 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .content-header .breadcrumb {
            font-size: 14px;
            color: #888;
        }

        .content-body {
            flex: 1;
            padding: 30px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* Dynamic Content Containers */
        .content-section {
            display: none;
            height: 100%;
            animation: fadeIn 0.3s ease;
        }

        .content-section.active {
            display: flex;
            flex-direction: column;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Chat Interface - Styles de base */

        /* Right Panel */
        .right-panel {
            grid-area: right-panel;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            overflow-y: auto;
        }

        .panel-section {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-section h3 {
            font-size: 16px;
            margin-bottom: 15px;
            color: #4ecdc4;
        }

        /* Status Indicators */
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .status-value {
            font-size: 20px;
            font-weight: bold;
            color: #4ecdc4;
        }

        .status-label {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .quick-action {
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .quick-action:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .quick-action i {
            display: block;
            font-size: 16px;
            margin-bottom: 5px;
            color: #4ecdc4;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .hub-container {
                grid-template-columns: 250px 1fr 280px;
            }
        }

        @media (max-width: 768px) {
            .hub-container {
                grid-template-areas:
                    "main-content"
                    "main-content";
                grid-template-columns: 1fr;
            }

            .sidebar, .right-panel {
                display: none;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #4ecdc4;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* ===== STYLES POUR LE CHAT ===== */
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        }

        /* En-tête du chat */
        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: rgba(0, 0, 0, 0.3);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-title h3 {
            margin: 0;
            font-size: 18px;
            color: white;
        }

        .chat-title i {
            color: #4ecdc4;
            font-size: 20px;
        }

        /* Menu déroulant */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dropdown-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #4ecdc4;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background: rgba(30, 30, 46, 0.95);
            backdrop-filter: blur(10px);
            min-width: 200px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            margin-top: 5px;
        }

        .dropdown-content.show {
            display: block;
            animation: dropdownFadeIn 0.3s ease;
        }

        @keyframes dropdownFadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .dropdown-content a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 4px;
        }

        .dropdown-content a:hover {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
        }

        .dropdown-content a i {
            width: 16px;
            text-align: center;
        }

        /* Zone des messages */
        .chat-messages {
            flex: 1;
            padding: 20px;
            padding-bottom: 10px;
            overflow-y: auto;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            scroll-behavior: smooth;
            min-height: 0;
        }

        .message {
            display: flex;
            margin-bottom: 20px;
            animation: messageSlideIn 0.3s ease;
        }

        @keyframes messageSlideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 12px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        }

        .message.user .message-avatar {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
        }

        .message-content {
            max-width: 70%;
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
        }

        .message.user .message-content {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
        }

        .message-content p {
            margin: 0;
            color: white;
            line-height: 1.4;
        }

        .message-time {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 5px;
            display: block;
        }

        /* Zone d'envoi fixée en bas */
        .chat-input-container {
            position: sticky;
            bottom: 0;
            padding: 15px 20px;
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: 10;
        }

        .input-actions-bar {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            padding: 0 5px;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .action-btn:hover {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            transform: translateY(-2px);
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            font-family: inherit;
        }

        .chat-input:focus {
            border-color: #4ecdc4;
            box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .send-btn {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
        }

        .send-btn:active {
            transform: scale(0.95);
        }

        .input-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            padding: 0 5px;
            font-size: 12px;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #4ecdc4;
        }

        .typing-dots {
            display: flex;
            gap: 3px;
        }

        .typing-dots span {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #4ecdc4;
            animation: typingDots 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typingDots {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .char-counter {
            color: rgba(255, 255, 255, 0.5);
        }

        .char-counter.warning {
            color: #ff6b6b;
        }

        /* ===== STYLES POUR LA NAVIGATION ===== */
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .header-left h2 {
            margin: 0 0 5px 0;
            color: white;
            font-size: 24px;
        }

        .breadcrumb {
            color: #888;
            font-size: 14px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-apps {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(78, 205, 196, 0.2);
            border-color: #4ecdc4;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
        }

        .nav-btn i {
            font-size: 16px;
        }

        .home-btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
        }

        .home-btn:hover {
            background: linear-gradient(45deg, #ff5252, #26a69a);
            transform: translateY(-2px) scale(1.05);
        }

        /* Menu déroulant des applications */
        #apps-dropdown {
            min-width: 250px;
            max-height: 400px;
            overflow-y: auto;
        }

        #apps-dropdown a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 6px;
            margin: 2px;
            font-size: 14px;
        }

        #apps-dropdown a:hover {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            transform: translateX(5px);
        }

        #apps-dropdown a i {
            width: 20px;
            text-align: center;
            font-size: 16px;
        }

        /* Responsive pour les boutons de navigation */
        @media (max-width: 768px) {
            .content-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .header-right {
                width: 100%;
                justify-content: space-between;
            }

            .nav-btn span {
                display: none;
            }

            .nav-btn {
                padding: 10px;
            }
        }

        /* Animation pour l'ouverture du menu */
        .dropdown-content.show {
            animation: dropdownSlideIn 0.3s ease;
        }

        @keyframes dropdownSlideIn {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Badge de notification pour les applications */
        .nav-btn .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff6b6b;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .nav-btn {
            position: relative;
        }

        /* ===== STYLES POUR LE PANNEAU DE RÉFLEXIONS ===== */
        .reflection-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: rgba(30, 30, 46, 0.95);
            backdrop-filter: blur(15px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            transition: right 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .reflection-panel.open {
            right: 0;
        }

        .reflection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .reflection-title {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .reflection-title i {
            color: #4ecdc4;
            font-size: 18px;
        }

        .reflection-controls {
            display: flex;
            gap: 8px;
        }

        .reflection-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .reflection-btn:hover {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
        }

        .reflection-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .reflection-item {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 3px solid #4ecdc4;
            animation: reflectionSlideIn 0.3s ease;
        }

        @keyframes reflectionSlideIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .reflection-type {
            font-size: 12px;
            color: #4ecdc4;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        .reflection-text {
            color: white;
            line-height: 1.5;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .reflection-time {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.5);
        }

        .reflection-footer {
            padding: 15px 20px;
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .reflection-stats {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .separator {
            color: rgba(255, 255, 255, 0.3);
        }

        /* Types de réflexions avec couleurs différentes */
        .reflection-item[data-type="thinking"] {
            border-left-color: #45b7d1;
        }

        .reflection-item[data-type="thinking"] .reflection-type {
            color: #45b7d1;
        }

        .reflection-item[data-type="analyzing"] {
            border-left-color: #ffa726;
        }

        .reflection-item[data-type="analyzing"] .reflection-type {
            color: #ffa726;
        }

        .reflection-item[data-type="learning"] {
            border-left-color: #66bb6a;
        }

        .reflection-item[data-type="learning"] .reflection-type {
            color: #66bb6a;
        }

        .reflection-item[data-type="error"] {
            border-left-color: #ff6b6b;
        }

        .reflection-item[data-type="error"] .reflection-type {
            color: #ff6b6b;
        }

        /* Responsive pour le panneau de réflexions */
        @media (max-width: 768px) {
            .reflection-panel {
                width: 100%;
                right: -100%;
            }
        }

        /* Animation pour le bouton de réflexions actif */
        .action-btn.reflection-active {
            background: rgba(78, 205, 196, 0.3);
            color: #4ecdc4;
        }

        /* Indicateur de nouvelles réflexions */
        .reflection-notification {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 12px;
            height: 12px;
            background: #ff6b6b;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        /* ===== BOUTON DE RÉFLEXIONS PRINCIPAL ===== */
        .reflection-controls-panel {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .reflection-toggle-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            border: none;
            border-radius: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 600;
            position: relative;
            width: 100%;
            justify-content: center;
        }

        .reflection-toggle-btn:hover {
            background: linear-gradient(45deg, #26a69a, #2196f3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
        }

        .reflection-toggle-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
        }

        .reflection-toggle-btn i {
            font-size: 20px;
        }

        .reflection-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 12px;
            height: 12px;
            background: #ff6b6b;
            border-radius: 50%;
            display: none;
            animation: pulse 2s infinite;
        }

        .reflection-indicator.active {
            display: block;
        }

        .reflection-status-mini {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            font-size: 12px;
        }

        .mini-status {
            color: #4ecdc4;
            font-weight: 600;
        }

        .mini-status.active {
            color: #66bb6a;
        }

        .mini-status.paused {
            color: #ffa726;
        }
    </style>
</head>
<body>
    <div class="hub-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="logo-section">
                <h1>LOUNA</h1>
                <div class="subtitle">Intelligence Évolutive</div>
            </div>

            <div class="nav-section">
                <h3>Principal</h3>
                <div class="nav-item active" data-section="chat">
                    <i class="fas fa-comments"></i>
                    <span>Chat Intelligent</span>
                    <div class="badge">●</div>
                </div>
                <div class="nav-item" data-section="generation">
                    <i class="fas fa-magic"></i>
                    <span>Génération</span>
                </div>
                <div class="nav-item" data-section="code">
                    <i class="fas fa-code"></i>
                    <span>Développement</span>
                </div>
                <div class="nav-item" data-section="analysis">
                    <i class="fas fa-chart-line"></i>
                    <span>Analyse</span>
                </div>
            </div>

            <div class="nav-section">
                <h3>Système</h3>
                <div class="nav-item" data-section="memory">
                    <i class="fas fa-brain"></i>
                    <span>Mémoire Thermique</span>
                </div>
                <div class="nav-item" data-section="monitoring">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Monitoring</span>
                </div>
                <div class="nav-item" data-section="agents">
                    <i class="fas fa-robot"></i>
                    <span>Agents</span>
                </div>
                <div class="nav-item" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <div class="header-left">
                    <h2 id="content-title">Chat Intelligent</h2>
                    <div class="breadcrumb" id="content-breadcrumb">Accueil > Chat</div>
                </div>
                <div class="header-right">
                    <!-- Boutons de navigation vers les autres applications -->
                    <div class="nav-apps">
                        <div class="dropdown">
                            <button class="nav-btn" onclick="toggleAppsMenu()">
                                <i class="fas fa-th-large"></i>
                                <span>Applications</span>
                            </button>
                            <div class="dropdown-content" id="apps-dropdown">
                                <a href="#" onclick="goToApp('home')"><i class="fas fa-home"></i> Page d'accueil</a>
                                <a href="#" onclick="goToApp('chat')"><i class="fas fa-comments"></i> Chat Principal</a>
                                <a href="#" onclick="goToApp('memory')"><i class="fas fa-brain"></i> Mémoire Thermique</a>
                                <a href="#" onclick="goToApp('monitor')"><i class="fas fa-chart-line"></i> Monitoring Cerveau</a>
                                <a href="#" onclick="goToApp('brain3d')"><i class="fas fa-cube"></i> Cerveau 3D</a>
                                <a href="#" onclick="goToApp('generation')"><i class="fas fa-magic"></i> Studio Génération</a>
                                <a href="#" onclick="goToApp('kyber')"><i class="fas fa-rocket"></i> Accélérateurs Kyber</a>
                                <a href="#" onclick="goToApp('performance')"><i class="fas fa-tachometer-alt"></i> Performances</a>
                                <a href="#" onclick="goToApp('settings')"><i class="fas fa-cog"></i> Paramètres</a>
                            </div>
                        </div>
                        <button class="nav-btn home-btn" onclick="goToHome()" title="Retour à l'accueil">
                            <i class="fas fa-home"></i>
                            <span>Accueil</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="content-body">
                <!-- Chat Section -->
                <div class="content-section active" id="chat-section">
                    <div class="chat-container">
                        <!-- En-tête du chat avec menu déroulant -->
                        <div class="chat-header">
                            <div class="chat-title">
                                <i class="fas fa-comments"></i>
                                <h3>💬 Chat avec Louna</h3>
                            </div>
                            <div class="chat-menu">
                                <div class="dropdown">
                                    <button class="dropdown-btn" onclick="toggleChatMenu()">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-content" id="chat-dropdown">
                                        <a href="#" onclick="clearChat()"><i class="fas fa-trash"></i> Effacer le chat</a>
                                        <a href="#" onclick="exportChat()"><i class="fas fa-download"></i> Exporter chat</a>
                                        <a href="#" onclick="exportReflections()"><i class="fas fa-brain"></i> Exporter réflexions</a>
                                        <a href="#" onclick="importChat()"><i class="fas fa-upload"></i> Importer</a>
                                        <a href="#" onclick="searchInChat()"><i class="fas fa-search"></i> Rechercher</a>
                                        <a href="#" onclick="toggleReflectionPanel()"><i class="fas fa-eye"></i> Voir réflexions</a>
                                        <a href="#" onclick="toggleChatTheme()"><i class="fas fa-palette"></i> Thème</a>
                                        <a href="#" onclick="chatSettings()"><i class="fas fa-cog"></i> Paramètres</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Zone des messages -->
                        <div class="chat-messages" id="chat-messages">
                            <div class="welcome-message">
                                <div class="message agent">
                                    <div class="message-avatar">🤖</div>
                                    <div class="message-content">
                                        <p>Bonjour ! Je suis Louna, votre assistant IA avancé. Comment puis-je vous aider aujourd'hui ?</p>
                                        <span class="message-time">Maintenant</span>
                                    </div>
                                </div>
                                <div class="message agent">
                                    <div class="message-avatar">🧠</div>
                                    <div class="message-content">
                                        <p><strong>🎯 TESTS DISPONIBLES :</strong><br>
                                        • Tapez "test chat" pour tester le chat<br>
                                        • Cliquez sur l'icône 🧠 pour voir les réflexions<br>
                                        • Utilisez le menu "Applications" pour naviguer<br>
                                        • Cliquez sur "Test Réflexions" dans les actions rapides</p>
                                        <span class="message-time">Maintenant</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Zone d'envoi fixée en bas -->
                        <div class="chat-input-container">
                            <div class="input-actions-bar">
                                <button class="action-btn" onclick="toggleReflectionPanel()" title="Réflexions de l'Agent">
                                    <i class="fas fa-brain"></i>
                                </button>
                                <button class="action-btn" onclick="attachFile()" title="Joindre un fichier">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <button class="action-btn" onclick="startVoiceInput()" title="Message vocal">
                                    <i class="fas fa-microphone"></i>
                                </button>
                                <button class="action-btn" onclick="insertEmoji()" title="Emoji">
                                    <i class="fas fa-smile"></i>
                                </button>
                                <button class="action-btn" onclick="takeScreenshot()" title="Capture d'écran">
                                    <i class="fas fa-camera"></i>
                                </button>
                            </div>
                            <div class="input-group">
                                <textarea class="chat-input" id="chat-input" placeholder="Tapez votre message ici..." rows="1" onkeypress="handleChatKeyPress(event)" oninput="adjustTextareaHeight(this)"></textarea>
                                <button class="send-btn" id="send-btn" onclick="sendMessage()">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                            <div class="input-status">
                                <span id="typing-indicator" class="typing-indicator" style="display: none;">
                                    <span class="typing-dots">
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </span>
                                    Louna tape...
                                </span>
                                <span id="char-counter" class="char-counter">0/2000</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other sections will be loaded dynamically -->
                <div class="content-section" id="generation-section">
                    <h3>Studio de Génération</h3>
                    <p>Interface de génération multimédia en cours de chargement...</p>
                </div>

                <div class="content-section" id="code-section">
                    <h3>Environnement de Développement</h3>
                    <p>Éditeur de code en cours de chargement...</p>
                </div>

                <div class="content-section" id="analysis-section">
                    <h3>Analyse et Insights</h3>
                    <p>Outils d'analyse en cours de chargement...</p>
                </div>

                <div class="content-section" id="memory-section">
                    <h3>Mémoire Thermique</h3>
                    <p>Interface de mémoire thermique en cours de chargement...</p>
                </div>

                <div class="content-section" id="monitoring-section">
                    <h3>Monitoring Système</h3>
                    <p>Tableaux de bord de monitoring en cours de chargement...</p>
                </div>

                <div class="content-section" id="agents-section">
                    <h3>Gestion des Agents</h3>
                    <p>Interface de gestion des agents en cours de chargement...</p>
                </div>

                <div class="content-section" id="settings-section">
                    <h3>Paramètres</h3>
                    <p>Configuration système en cours de chargement...</p>
                </div>
            </div>
        </div>

        <!-- Right Panel -->
        <div class="right-panel">
            <div class="panel-section">
                <h3>État du Système</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value" id="qi-value">120</div>
                        <div class="status-label">QI</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="neurons-value">71</div>
                        <div class="status-label">Neurones</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="memory-temp">42°C</div>
                        <div class="status-label">Mémoire</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="efficiency">84%</div>
                        <div class="status-label">Efficacité</div>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3>Réflexions Agent</h3>
                <div class="reflection-controls-panel">
                    <button class="reflection-toggle-btn" onclick="toggleReflectionPanel()" id="main-reflection-btn">
                        <i class="fas fa-brain"></i>
                        <span>Voir Réflexions</span>
                        <div class="reflection-indicator" id="reflection-indicator"></div>
                    </button>
                    <div class="reflection-status-mini">
                        <span id="mini-reflection-count">0 réflexions</span>
                        <span class="mini-status" id="mini-reflection-status">Inactif</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3>Actions Rapides</h3>
                <div class="quick-actions">
                    <div class="quick-action" onclick="checkAgentStatus()">
                        <i class="fas fa-robot"></i>
                        Test Agent
                    </div>
                    <div class="quick-action" onclick="runDiagnostic()">
                        <i class="fas fa-stethoscope"></i>
                        Diagnostic
                    </div>
                    <div class="quick-action" onclick="testReflections()">
                        <i class="fas fa-brain"></i>
                        Test Réflexions
                    </div>
                    <div class="quick-action" onclick="quickAction('optimize-system')">
                        <i class="fas fa-rocket"></i>
                        Optimiser
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3>Activité Récente</h3>
                <div id="recent-activity">
                    <div style="text-align: center; color: #888; font-size: 14px;">
                        <i class="loading"></i>
                        <p style="margin-top: 10px;">Chargement...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Panneau de réflexions de l'agent -->
    <div class="reflection-panel" id="reflection-panel">
        <div class="reflection-header">
            <div class="reflection-title">
                <i class="fas fa-brain"></i>
                <span>Réflexions de l'Agent</span>
            </div>
            <div class="reflection-controls">
                <button class="reflection-btn" onclick="clearReflections()" title="Effacer les réflexions">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="reflection-btn" onclick="pauseReflections()" title="Pause/Reprendre" id="pause-reflections">
                    <i class="fas fa-pause"></i>
                </button>
                <button class="reflection-btn" onclick="toggleReflectionPanel()" title="Fermer">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="reflection-content" id="reflection-content">
            <div class="reflection-item">
                <div class="reflection-type">Initialisation</div>
                <div class="reflection-text">En attente des réflexions de l'agent...</div>
                <div class="reflection-time">Maintenant</div>
            </div>
        </div>
        <div class="reflection-footer">
            <div class="reflection-stats">
                <span id="reflection-count">1 réflexion</span>
                <span class="separator">•</span>
                <span id="reflection-status">En temps réel</span>
            </div>
        </div>
    </div>

    <script src="/js/unified-hub.js"></script>
    <script src="/js/advanced-voice-system.js"></script>
    <script src="/js/intelligent-vision-system.js"></script>
    <script src="/js/adaptive-learning-system.js"></script>
    <script src="/js/real-content-generator.js"></script>
</body>
</html>
