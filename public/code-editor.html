<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Éditeur de Code Louna - Ultra Complet</title>

    <!-- Styles Louna -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/native-app.css">
    <link rel="stylesheet" href="/css/pink-black-theme.css">
    <link rel="stylesheet" href="/css/unified-interface.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Monaco Editor -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            overflow: hidden;
            font-size: 16px;
        }

        .editor-container {
            display: flex;
            height: 100vh;
            width: 100vw;
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(15px);
            border-right: 2px solid rgba(255, 105, 180, 0.4);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 25px;
            border-bottom: 2px solid rgba(255, 105, 180, 0.4);
            background: rgba(255, 105, 180, 0.15);
        }

        .sidebar-header h2 {
            color: #ff69b4;
            font-size: 20px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        /* Navigation buttons */
        .nav-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .nav-btn {
            flex: 1;
            padding: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            color: #ffffff !important;
            cursor: pointer;
            font-weight: bold !important;
            font-size: 12px;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: #ffffff !important;
        }

        .nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        .nav-btn.home:hover {
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }

        .new-file-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            border: none;
            border-radius: 10px;
            color: #ffffff !important;
            cursor: pointer;
            font-weight: bold !important;
            font-size: 16px;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .new-file-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 105, 180, 0.5);
            color: #ffffff !important;
        }

        .file-explorer {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-size: 16px;
        }

        .file-item:hover {
            background: rgba(255, 105, 180, 0.15);
            border-color: rgba(255, 105, 180, 0.4);
            transform: translateX(5px);
        }

        .file-item.active {
            background: rgba(255, 105, 180, 0.25);
            border-color: #ff69b4;
            transform: translateX(5px);
        }

        .file-icon {
            font-size: 20px;
            width: 25px;
            text-align: center;
        }

        .file-name {
            font-weight: 600;
            font-size: 16px;
            color: #ffffff;
        }

        /* Main Editor Area */
        .main-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(15px);
            border-bottom: 2px solid rgba(255, 105, 180, 0.4);
            flex-wrap: wrap;
            gap: 10px;
        }

        .toolbar-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .toolbar-btn {
            padding: 12px 18px;
            background: rgba(255, 105, 180, 0.15);
            border: 2px solid rgba(255, 105, 180, 0.4);
            border-radius: 10px;
            color: #ffffff !important;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 700 !important;
            display: flex;
            align-items: center;
            gap: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .toolbar-btn:hover {
            background: rgba(255, 105, 180, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
            color: #ffffff !important;
        }

        .toolbar-btn.primary {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            border-color: #ff69b4;
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }

        .toolbar-btn.primary:hover {
            color: #ffffff !important;
        }

        .tabs-container {
            display: flex;
            background: rgba(0, 0, 0, 0.3);
            border-bottom: 2px solid rgba(255, 105, 180, 0.4);
            overflow-x: auto;
        }

        .tab {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.08);
            border-right: 1px solid rgba(255, 105, 180, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            font-size: 15px;
            font-weight: 600;
            color: #ffffff;
        }

        .tab.active {
            background: rgba(255, 105, 180, 0.25);
            border-bottom: 3px solid #ff69b4;
        }

        .tab:hover {
            background: rgba(255, 105, 180, 0.15);
        }

        .tab-close {
            margin-left: 10px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
            font-size: 14px;
        }

        .tab-close:hover {
            opacity: 1;
            color: #ff69b4;
        }

        .editor-wrapper {
            flex: 1;
            position: relative;
            background: #1e1e1e;
        }

        #monaco-editor {
            width: 100%;
            height: 100%;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 25px;
            background: rgba(255, 105, 180, 0.9);
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .status-group {
            display: flex;
            gap: 25px;
        }

        /* Terminal */
        .terminal {
            height: 300px;
            background: #000000;
            border-top: 2px solid rgba(255, 105, 180, 0.4);
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            color: #00ff00;
            overflow-y: auto;
            display: none;
        }

        .terminal.visible {
            display: block;
        }

        .terminal-line {
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .terminal-prompt {
            color: #ff69b4;
            font-weight: bold;
        }

        /* Modals */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 20px;
            padding: 40px;
            width: 500px;
            border: 2px solid rgba(255, 105, 180, 0.4);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7);
        }

        .modal-header {
            margin-bottom: 25px;
        }

        .modal-header h3 {
            color: #ff69b4;
            font-size: 24px;
            font-weight: bold;
        }

        .modal-input, .modal-textarea, .modal-select {
            width: 100%;
            padding: 15px;
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 105, 180, 0.4);
            border-radius: 10px;
            color: #ffffff;
            margin-bottom: 20px;
            font-size: 16px;
            font-family: inherit;
        }

        .modal-textarea {
            height: 120px;
            resize: vertical;
        }

        .modal-input:focus, .modal-textarea:focus, .modal-select:focus {
            border-color: #ff69b4;
            box-shadow: 0 0 15px rgba(255, 105, 180, 0.4);
            outline: none;
        }

        .modal-input::placeholder, .modal-textarea::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .modal-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold !important;
            font-size: 16px;
            transition: all 0.3s ease;
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .modal-btn.primary {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }

        .modal-btn.secondary {
            background: rgba(255, 255, 255, 0.15);
            color: #ffffff !important;
            border: 2px solid rgba(255, 255, 255, 0.4);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .modal-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            color: #ffffff !important;
        }

        /* Command Items */
        .command-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            margin-bottom: 8px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            color: white;
            font-size: 16px;
        }

        .command-item:hover {
            background: rgba(255, 105, 180, 0.15);
            border-color: rgba(255, 105, 180, 0.4);
            transform: translateX(5px);
        }

        .command-item i {
            width: 25px;
            text-align: center;
            color: #ff69b4;
            font-size: 18px;
        }

        /* Responsive et optimisation de l'espace */
        @media (max-width: 768px) {
            .sidebar {
                width: 250px;
            }

            .toolbar {
                padding: 15px;
            }

            .toolbar-group {
                gap: 8px;
            }

            .toolbar-btn {
                padding: 10px 14px;
                font-size: 12px;
            }

            .sidebar-header {
                padding: 20px;
            }

            .file-explorer {
                padding: 15px;
            }
        }

        @media (min-width: 1200px) {
            .sidebar {
                width: 350px;
            }

            .toolbar {
                padding: 25px;
            }

            .toolbar-btn {
                padding: 14px 20px;
                font-size: 15px;
            }
        }

        @media (min-width: 1600px) {
            .sidebar {
                width: 400px;
            }

            .toolbar {
                padding: 30px;
            }

            .sidebar-header {
                padding: 30px;
            }

            .file-explorer {
                padding: 25px;
            }
        }

        /* Optimisation pour les très grands écrans */
        @media (min-width: 2000px) {
            .sidebar {
                width: 450px;
            }

            .toolbar-btn {
                padding: 16px 24px;
                font-size: 16px;
            }
        }

        /* Corrections de lisibilité pour tous les boutons */
        button, .btn, .toolbar-btn, .modal-btn, .new-file-btn, .tab {
            color: #ffffff !important;
            font-weight: bold !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        button:hover, .btn:hover, .toolbar-btn:hover, .modal-btn:hover, .new-file-btn:hover, .tab:hover {
            color: #ffffff !important;
        }

        /* Correction spécifique pour les icônes dans les boutons */
        .toolbar-btn i, .modal-btn i, .new-file-btn i {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* Correction pour les onglets */
        .tab {
            color: #ffffff !important;
            font-weight: 600 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .tab.active {
            color: #ffffff !important;
            font-weight: bold !important;
        }

        /* Correction pour la barre de statut */
        .status-bar {
            color: #ffffff !important;
            font-weight: 600 !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
        }

        .status-bar span {
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
        }
    </style>

    <link rel="stylesheet" href="/css/contrast-fixes.css">
    <style>
        /* CORRECTION UNIVERSELLE DE LISIBILITÉ - LOUNA */

        /* Optimisation du rendu des polices */
        * {
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Variables corrigées pour lisibilité maximale */
        :root {
            --text-primary: #ffffff !important;
            --text-secondary: #ffffff !important;
            --text-muted: rgba(255, 255, 255, 0.9) !important;
            --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* CORRECTION GLOBALE - TOUS LES TEXTES */
        h1, h2, h3, h4, h5, h6, p, span, div, li, a, label, input, textarea, select {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        /* BOUTONS - LISIBILITÉ MAXIMALE */
        button, .btn, .button, .toolbar-btn, .demo-button, .cta-button {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        button:hover, .btn:hover, .button:hover {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }

        /* NAVIGATION - TOUJOURS VISIBLE */
        .nav-item, .nav-link, .navbar-nav a, .top-navbar a {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        .logo-text, .navbar-brand {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: bold !important;
        }

        /* CARTES ET CONTENEURS - CONTRASTE OPTIMAL */
        .card, .unified-card, .metric-card, .capability-card {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        /* TITRES ET SOUS-TITRES */
        .interface-title, .section-title, .card-title {
            color: #ffffff !important;
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        .interface-subtitle, .card-subtitle {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* STATISTIQUES ET MÉTRIQUES */
        .stat-number, .metric-value {
            color: #ff6b6b !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        .stat-label, .metric-label {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        /* LISTES ET DESCRIPTIONS */
        .capability-features li, .spec-list li {
            color: rgba(255, 255, 255, 0.95) !important;
            text-shadow: var(--text-shadow) !important;
        }

        .capability-description, .card-text {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* FORMULAIRES */
        input, select, textarea {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            color: #ffffff !important;
        }

        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* TABLEAUX */
        table, th, td {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        th {
            background: rgba(255, 105, 180, 0.2) !important;
            font-weight: bold !important;
        }

        /* ALERTES ET NOTIFICATIONS */
        .alert, .notification {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* FOOTER */
        .footer, .footer-content {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* CORRECTIONS SPÉCIFIQUES POUR FONDS CLAIRS */
        .tech-specs, .capabilities-section[style*="background: #f"] {
            color: #333 !important;
        }

        .tech-specs *, .capabilities-section[style*="background: #f"] * {
            color: #333 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
        }

        /* ICÔNES */
        .fas, .far, .fab {
            text-shadow: var(--text-shadow) !important;
        }

        /* CORRECTION POUR LES ÉLÉMENTS SPÉCIAUX */
        .progress-bar, .slider {
            background: linear-gradient(135deg, #ff69b4, #ff1493) !important;
        }

        /* AMÉLIORATION DU CONTRASTE POUR LES LIENS */
        a {
            color: #ff69b4 !important;
            text-shadow: var(--text-shadow) !important;
        }

        a:hover {
            color: #ff1493 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-code"></i> Éditeur Louna Ultra</h2>

                <!-- Boutons de navigation -->
                <div class="nav-buttons">
                    <button class="nav-btn home" onclick="goHome()">
                        <i class="fas fa-home"></i> Accueil
                    </button>
                    <button class="nav-btn" onclick="goToChat()">
                        <i class="fas fa-comments"></i> Chat
                    </button>
                </div>

                <button class="new-file-btn" onclick="showNewFileModal()">
                    <i class="fas fa-plus"></i> Nouveau Fichier
                </button>
            </div>

            <div class="file-explorer" id="file-explorer">
                <div class="file-item active" data-file="welcome.md" data-language="markdown">
                    <i class="fas fa-home file-icon" style="color: #ff69b4;"></i>
                    <span class="file-name">Bienvenue</span>
                </div>

                <div class="file-item" data-file="example.js" data-language="javascript">
                    <i class="fab fa-js-square file-icon" style="color: #f7df1e;"></i>
                    <span class="file-name">example.js</span>
                </div>

                <div class="file-item" data-file="style.css" data-language="css">
                    <i class="fab fa-css3-alt file-icon" style="color: #1572b6;"></i>
                    <span class="file-name">style.css</span>
                </div>

                <div class="file-item" data-file="index.html" data-language="html">
                    <i class="fab fa-html5 file-icon" style="color: #e34f26;"></i>
                    <span class="file-name">index.html</span>
                </div>
            </div>
        </div>

        <!-- Main Editor Area -->
        <div class="main-area">
            <!-- Toolbar -->
            <div class="toolbar">
                <div class="toolbar-group">
                    <button class="toolbar-btn primary" onclick="saveFile()" title="Sauvegarder (Ctrl+S)">
                        <i class="fas fa-save"></i> Sauvegarder
                    </button>
                    <button class="toolbar-btn" onclick="copyCode()" title="Copier le code">
                        <i class="fas fa-copy"></i> Copier
                    </button>
                    <button class="toolbar-btn" onclick="pasteCode()" title="Coller le code">
                        <i class="fas fa-paste"></i> Coller
                    </button>
                </div>

                <div class="toolbar-group">
                    <button class="toolbar-btn" onclick="findAndReplace()" title="Rechercher & Remplacer (Ctrl+F)">
                        <i class="fas fa-search"></i> Rechercher
                    </button>
                    <button class="toolbar-btn" onclick="formatCode()" title="Formater le code">
                        <i class="fas fa-code"></i> Formater
                    </button>
                    <button class="toolbar-btn" onclick="analyzeCode()" title="Analyser le code">
                        <i class="fas fa-chart-line"></i> Analyser
                    </button>
                    <button class="toolbar-btn" onclick="generateWithAI()" title="Générer du code avec l'IA">
                        <i class="fas fa-robot"></i> IA
                    </button>
                </div>

                <div class="toolbar-group">
                    <button class="toolbar-btn" onclick="toggleTerminal()" title="Terminal (Ctrl+`)">
                        <i class="fas fa-terminal"></i> Terminal
                    </button>
                    <button class="toolbar-btn" onclick="increaseFontSize()" title="Agrandir la police">
                        <i class="fas fa-plus"></i> A+
                    </button>
                    <button class="toolbar-btn" onclick="decreaseFontSize()" title="Réduire la police">
                        <i class="fas fa-minus"></i> A-
                    </button>
                    <button class="toolbar-btn" onclick="goHome()" title="Retour à l'accueil">
                        <i class="fas fa-home"></i> Accueil
                    </button>
                </div>
            </div>

            <!-- Tabs -->
            <div class="tabs-container" id="tabs-container">
                <div class="tab active" data-file="welcome.md">
                    <i class="fas fa-home" style="color: #ff69b4;"></i>
                    <span>Bienvenue</span>
                    <i class="fas fa-times tab-close" onclick="closeTab('welcome.md', event)"></i>
                </div>
            </div>

            <!-- Editor -->
            <div class="editor-wrapper">
                <div id="monaco-editor"></div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-group">
                    <span id="cursor-position">Ligne 1, Col 1</span>
                    <span id="file-language">Markdown</span>
                    <span id="file-encoding">UTF-8</span>
                </div>
                <div class="status-group">
                    <span id="file-size">0 octets</span>
                    <span id="last-saved">Prêt</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Terminal -->
    <div class="terminal" id="terminal">
        <div class="terminal-line">
            <span class="terminal-prompt">louna@editor:~$</span> Bienvenue dans l'éditeur de code Louna Ultra !
        </div>
        <div class="terminal-line">
            <span class="terminal-prompt">louna@editor:~$</span> Toutes les fonctionnalités sont maintenant disponibles
        </div>
        <div class="terminal-line">
            <span class="terminal-prompt">louna@editor:~$</span> Tapez 'help' pour voir les commandes disponibles
        </div>
    </div>

    <!-- New File Modal -->
    <div class="modal" id="new-file-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> Nouveau Fichier</h3>
            </div>
            <input type="text" class="modal-input" id="new-file-name" placeholder="Nom du fichier (ex: script.js, style.css, page.html)">
            <div class="modal-buttons">
                <button class="modal-btn secondary" onclick="hideNewFileModal()">Annuler</button>
                <button class="modal-btn primary" onclick="createNewFile()">Créer</button>
            </div>
        </div>
    </div>

    <!-- Find and Replace Modal -->
    <div class="modal" id="find-replace-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-search"></i> Rechercher & Remplacer</h3>
            </div>
            <input type="text" class="modal-input" id="find-text" placeholder="Rechercher...">
            <input type="text" class="modal-input" id="replace-text" placeholder="Remplacer par...">
            <div style="margin: 15px 0;">
                <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; color: white;">
                    <input type="checkbox" id="case-sensitive"> Sensible à la casse
                </label>
                <label style="display: flex; align-items: center; gap: 8px; color: white;">
                    <input type="checkbox" id="whole-words"> Mots entiers uniquement
                </label>
            </div>
            <div class="modal-buttons">
                <button class="modal-btn secondary" onclick="hideFindReplaceModal()">Annuler</button>
                <button class="modal-btn" onclick="findNext()">Suivant</button>
                <button class="modal-btn" onclick="replaceOne()">Remplacer</button>
                <button class="modal-btn primary" onclick="replaceAll()">Tout remplacer</button>
            </div>
        </div>
    </div>

    <!-- AI Generation Modal -->
    <div class="modal" id="ai-generation-modal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <h3><i class="fas fa-robot"></i> Génération de Code IA</h3>
            </div>
            <textarea class="modal-textarea" id="ai-prompt" placeholder="Décrivez le code que vous voulez générer...

Exemples:
- Créer une fonction pour calculer la moyenne d'un tableau
- Faire un formulaire de contact en HTML
- Créer une animation CSS pour un bouton
- Fonction Python pour lire un fichier CSV"></textarea>
            <select class="modal-select" id="ai-language">
                <option value="javascript">JavaScript</option>
                <option value="html">HTML</option>
                <option value="css">CSS</option>
                <option value="python">Python</option>
                <option value="java">Java</option>
                <option value="cpp">C++</option>
                <option value="json">JSON</option>
                <option value="markdown">Markdown</option>
            </select>
            <div class="modal-buttons">
                <button class="modal-btn secondary" onclick="hideAIGenerationModal()">Annuler</button>
                <button class="modal-btn primary" onclick="generateCodeWithAI()">Générer</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>
    <script src="/js/native-app.js"></script>

    <script>
        // ===== VARIABLES GLOBALES =====
        let editor = null;
        let currentFile = 'welcome.md';
        let files = {};
        let currentFontSize = 16;
        let findDecorations = [];
        let openTabs = ['welcome.md'];

        // ===== CONTENU PAR DÉFAUT DES FICHIERS =====
        const defaultFiles = {
            'welcome.md': {
                content: `# 🎉 Bienvenue dans l'Éditeur de Code Louna Ultra !

## ✨ Fonctionnalités Complètes

- **Monaco Editor** : Le même éditeur que VS Code avec coloration syntaxique
- **Multi-langages** : JavaScript, CSS, HTML, Markdown, Python, etc.
- **Interface moderne** : Design rose et noir élégant et lisible
- **Copier-Coller** : Fonctionnalités complètes avec boutons dédiés
- **Auto-complétion** : Suggestions intelligentes pendant la frappe
- **Rechercher & Remplacer** : Fonctionnalité avancée avec options
- **Génération IA** : Créez du code automatiquement avec l'IA
- **Terminal intégré** : Console complète pour vos tests
- **Sauvegarde** : Vos fichiers sont automatiquement sauvegardés

## 🚀 Comment utiliser

1. **Cliquez sur un fichier** dans la sidebar pour l'ouvrir
2. **Créez un nouveau fichier** avec le bouton "Nouveau Fichier"
3. **Sauvegardez** avec Ctrl+S ou le bouton Sauvegarder
4. **Formatez votre code** avec le bouton Formater
5. **Utilisez l'IA** pour générer du code automatiquement
6. **Copiez-collez** facilement avec les boutons dédiés
7. **Recherchez** dans votre code avec Ctrl+F

## 🎯 Raccourcis Clavier

- **Ctrl+S** : Sauvegarder
- **Ctrl+N** : Nouveau fichier
- **Ctrl+F** : Rechercher & Remplacer
- **Ctrl+\`** : Toggle Terminal
- **F5** : Exécuter le code

## 🎨 Prêt à coder !

Cet éditeur est maintenant **COMPLÈTEMENT FONCTIONNEL** !
Essayez de créer un nouveau fichier JavaScript ou CSS.
Testez la génération IA en décrivant le code que vous voulez !`,
                language: 'markdown'
            },
            'example.js': {
                content: `// 🚀 Exemple de code JavaScript avec coloration syntaxique
console.log('Bonjour depuis l\\'éditeur Louna Ultra !');

// Fonction d'exemple avec auto-complétion
function calculerSomme(a, b) {
    return a + b;
}

// Fonction pour calculer la moyenne d'un tableau
function calculerMoyenne(tableau) {
    if (tableau.length === 0) return 0;
    const somme = tableau.reduce((acc, val) => acc + val, 0);
    return somme / tableau.length;
}

// Test des fonctions
const resultat = calculerSomme(5, 3);
console.log('Résultat de la somme:', resultat);

const notes = [15, 18, 12, 16, 14];
const moyenne = calculerMoyenne(notes);
console.log('Moyenne des notes:', moyenne);

// Exemple avec DOM et événements
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page chargée !');

    // Créer un élément dynamiquement
    const button = document.createElement('button');
    button.textContent = 'Cliquez-moi !';
    button.addEventListener('click', () => {
        alert('Bouton cliqué depuis l\\'éditeur Louna !');
    });

    document.body.appendChild(button);
});

// Fonction asynchrone moderne
async function fetchData(url) {
    try {
        const response = await fetch(url);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Erreur lors du fetch:', error);
        return null;
    }
}

// Classe ES6 avec méthodes
class Calculator {
    constructor() {
        this.history = [];
    }

    add(a, b) {
        const result = a + b;
        this.history.push(\`\${a} + \${b} = \${result}\`);
        return result;
    }

    getHistory() {
        return this.history;
    }
}

// Utilisation de la classe
const calc = new Calculator();
calc.add(10, 5);
console.log('Historique:', calc.getHistory());`,
                language: 'javascript'
            },
            'style.css': {
                content: `/* 🎨 Exemple de CSS avec coloration syntaxique complète */

/* Variables CSS modernes */
:root {
    --primary-color: #ff69b4;
    --secondary-color: #e91e63;
    --background-dark: #1a1a2e;
    --text-light: #ffffff;
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--background-dark) 0%, #16213e 50%, #0f3460 100%);
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
    padding: 20px;
}

/* Container principal */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

/* Boutons modernes avec animations */
.button {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: var(--border-radius);
    color: white;
    padding: 15px 30px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 105, 180, 0.4);
}

.button:hover::before {
    left: 100%;
}

/* Cards avec effets */
.card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 25px;
    margin: 20px 0;
    border: 2px solid rgba(255, 105, 180, 0.3);
    transition: var(--transition);
    backdrop-filter: blur(5px);
}

.card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* Formulaires stylés */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--primary-color);
    font-weight: 600;
}

.form-input {
    width: 100%;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 105, 180, 0.3);
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-size: 16px;
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(255, 105, 180, 0.3);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Navigation moderne */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 2px solid rgba(255, 105, 180, 0.3);
    margin-bottom: 30px;
}

.nav-links {
    display: flex;
    gap: 25px;
    list-style: none;
}

.nav-link {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Animations keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Classes utilitaires */
.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.text-center {
    text-align: center;
}

.text-primary {
    color: var(--primary-color);
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .navbar {
        flex-direction: column;
        gap: 15px;
    }

    .nav-links {
        gap: 15px;
    }

    .button {
        padding: 12px 24px;
        font-size: 14px;
    }
}

/* Grid moderne */
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

/* Flexbox utilities */
.flex {
    display: flex;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}`,
                language: 'css'
            },
            'index.html': {
                content: `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Projet - Créé avec Louna</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-brand">
            <h2 style="color: #ff69b4;"><i class="fas fa-code"></i> Mon Projet</h2>
        </div>
        <ul class="nav-links">
            <li><a href="#home" class="nav-link">Accueil</a></li>
            <li><a href="#about" class="nav-link">À propos</a></li>
            <li><a href="#projects" class="nav-link">Projets</a></li>
            <li><a href="#contact" class="nav-link">Contact</a></li>
        </ul>
    </nav>

    <!-- Container principal -->
    <div class="container">
        <!-- Section Hero -->
        <section id="home" class="text-center fade-in">
            <h1 style="font-size: 3em; margin-bottom: 20px; color: #ff69b4;">
                🎉 Bienvenue dans mon projet !
            </h1>
            <p style="font-size: 1.2em; margin-bottom: 30px; opacity: 0.9;">
                Créé avec l'éditeur de code Louna Ultra - Maintenant avec coloration syntaxique complète !
            </p>
            <button class="button pulse" onclick="direBonjour()">
                <i class="fas fa-rocket"></i> Cliquez-moi !
            </button>
        </section>

        <!-- Section Cards -->
        <section id="about" class="grid" style="margin-top: 50px;">
            <div class="card fade-in">
                <h3 style="color: #ff69b4; margin-bottom: 15px;">
                    <i class="fas fa-code"></i> Développement
                </h3>
                <p>
                    Interface moderne créée avec HTML5, CSS3 et JavaScript ES6+.
                    Utilisation des dernières technologies web.
                </p>
            </div>

            <div class="card fade-in">
                <h3 style="color: #ff69b4; margin-bottom: 15px;">
                    <i class="fas fa-paint-brush"></i> Design
                </h3>
                <p>
                    Design élégant avec dégradés, animations fluides et
                    effets de transparence modernes.
                </p>
            </div>

            <div class="card fade-in">
                <h3 style="color: #ff69b4; margin-bottom: 15px;">
                    <i class="fas fa-mobile-alt"></i> Responsive
                </h3>
                <p>
                    Interface adaptative qui fonctionne parfaitement sur
                    tous les appareils et tailles d'écran.
                </p>
            </div>
        </section>

        <!-- Section Formulaire -->
        <section id="contact" style="margin-top: 50px;">
            <div class="card">
                <h2 style="color: #ff69b4; margin-bottom: 25px; text-align: center;">
                    <i class="fas fa-envelope"></i> Contactez-nous
                </h2>

                <form id="contact-form">
                    <div class="form-group">
                        <label class="form-label" for="name">
                            <i class="fas fa-user"></i> Nom
                        </label>
                        <input type="text" id="name" class="form-input" placeholder="Votre nom" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="email">
                            <i class="fas fa-envelope"></i> Email
                        </label>
                        <input type="email" id="email" class="form-input" placeholder="<EMAIL>" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="message">
                            <i class="fas fa-comment"></i> Message
                        </label>
                        <textarea id="message" class="form-input" rows="4" placeholder="Votre message..." required></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="button">
                            <i class="fas fa-paper-plane"></i> Envoyer
                        </button>
                    </div>
                </form>
            </div>
        </section>

        <!-- Section Résultat -->
        <div id="message-result" style="margin-top: 30px; text-align: center;"></div>
    </div>

    <!-- Scripts -->
    <script src="example.js"></script>
    <script>
        // Fonction interactive
        function direBonjour() {
            const messageDiv = document.getElementById('message-result');
            messageDiv.innerHTML = \`
                <div class="card pulse" style="background: rgba(255, 105, 180, 0.2); border-color: #ff69b4;">
                    <h3 style="color: #ff69b4;">
                        <i class="fas fa-heart"></i> Bonjour depuis Louna ! 🚀
                    </h3>
                    <p>L'éditeur de code fonctionne parfaitement avec coloration syntaxique complète !</p>
                    <p><strong>Fonctionnalités testées :</strong></p>
                    <ul style="text-align: left; margin-top: 15px;">
                        <li>✅ Coloration syntaxique HTML, CSS, JavaScript</li>
                        <li>✅ Auto-complétion intelligente</li>
                        <li>✅ Copier-coller fonctionnel</li>
                        <li>✅ Terminal intégré</li>
                        <li>✅ Génération IA</li>
                        <li>✅ Interface moderne et lisible</li>
                    </ul>
                </div>
            \`;

            // Scroll vers le résultat
            messageDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // Gestion du formulaire
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const message = document.getElementById('message').value;

            const messageDiv = document.getElementById('message-result');
            messageDiv.innerHTML = \`
                <div class="card fade-in" style="background: rgba(0, 255, 0, 0.1); border-color: #00ff00;">
                    <h3 style="color: #00ff00;">
                        <i class="fas fa-check-circle"></i> Message envoyé !
                    </h3>
                    <p>Merci <strong>\${name}</strong> ! Votre message a été reçu.</p>
                    <p><em>"\${message}"</em></p>
                </div>
            \`;

            // Reset du formulaire
            this.reset();

            // Scroll vers le résultat
            messageDiv.scrollIntoView({ behavior: 'smooth' });
        });

        // Animation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎉 Page chargée avec l\\'éditeur Louna Ultra !');

            // Ajouter des animations aux éléments
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
<script src="/js/auto-init-fixes.js"></script>
</body>
</html>`,
                language: 'html'
            }
        };

        // ===== FONCTION D'AFFICHAGE DES NOTIFICATIONS =====
        function showNotification(message, type = 'info') {
            // Utiliser le système de notifications de Louna si disponible
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                // Fallback avec alert
                alert(message);
            }
            console.log(\`📢 [\${type.toUpperCase()}] \${message}\`);
        }

        // ===== INITIALISATION DE MONACO EDITOR =====
        function initializeEditor() {
            console.log('🚀 Initialisation de Monaco Editor...');

            require.config({
                paths: {
                    'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs'
                }
            });

            require(['vs/editor/editor.main'], function () {
                // Initialiser les fichiers
                files = { ...defaultFiles };

                // Créer l'éditeur avec toutes les options
                editor = monaco.editor.create(document.getElementById('monaco-editor'), {
                    value: files[currentFile].content,
                    language: files[currentFile].language,
                    theme: 'vs-dark',
                    fontSize: currentFontSize,
                    lineNumbers: 'on',
                    automaticLayout: true,
                    minimap: { enabled: true },
                    scrollBeyondLastLine: false,
                    wordWrap: 'on',
                    suggestOnTriggerCharacters: true,
                    quickSuggestions: true,
                    wordBasedSuggestions: true,
                    folding: true,
                    bracketPairColorization: { enabled: true },
                    autoIndent: 'full',
                    formatOnPaste: true,
                    formatOnType: true,
                    renderWhitespace: 'selection',
                    renderControlCharacters: true,
                    smoothScrolling: true,
                    cursorBlinking: 'smooth',
                    cursorSmoothCaretAnimation: true,
                    mouseWheelZoom: true,
                    multiCursorModifier: 'ctrlCmd'
                });

                // Écouter les changements de contenu
                editor.onDidChangeModelContent(() => {
                    if (files[currentFile]) {
                        files[currentFile].content = editor.getValue();
                        updateStatusBar();
                        document.getElementById('last-saved').textContent = 'Modifié';
                    }
                });

                // Écouter les changements de position du curseur
                editor.onDidChangeCursorPosition((e) => {
                    document.getElementById('cursor-position').textContent =
                        \`Ligne \${e.position.lineNumber}, Col \${e.position.column}\`;
                });

                // Configurer les événements
                setupFileEvents();
                setupKeyboardShortcuts();
                updateStatusBar();

                console.log('✅ Monaco Editor initialisé avec succès !');
                showNotification('🎉 Éditeur Louna Ultra prêt ! Toutes les fonctionnalités sont disponibles.', 'success');
            });
        }

        // ===== GESTION DES FICHIERS =====
        function setupFileEvents() {
            document.querySelectorAll('.file-item').forEach(item => {
                item.addEventListener('click', function() {
                    const fileName = this.dataset.file;
                    if (fileName && files[fileName]) {
                        switchToFile(fileName);
                    }
                });
            });
        }

        function switchToFile(fileName) {
            if (!files[fileName]) {
                console.error(\`Fichier non trouvé: \${fileName}\`);
                return;
            }

            // Sauvegarder le fichier actuel
            if (editor && files[currentFile]) {
                files[currentFile].content = editor.getValue();
            }

            // Changer de fichier
            currentFile = fileName;

            // Mettre à jour l'éditeur
            if (editor) {
                const model = monaco.editor.createModel(
                    files[currentFile].content,
                    files[currentFile].language
                );
                editor.setModel(model);
            }

            // Mettre à jour l'interface
            updateFileSelection();
            updateTabSelection();
            updateStatusBar();

            console.log(\`📂 Fichier ouvert: \${fileName}\`);
            showNotification(\`Fichier \${fileName} ouvert\`, 'info');
        }

        function updateFileSelection() {
            document.querySelectorAll('.file-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.file === currentFile) {
                    item.classList.add('active');
                }
            });
        }

        function updateTabSelection() {
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.file === currentFile) {
                    tab.classList.add('active');
                }
            });
        }

        function updateStatusBar() {
            if (!files[currentFile]) return;

            const content = files[currentFile].content || '';
            const size = new Blob([content]).size;

            document.getElementById('file-language').textContent =
                files[currentFile].language.charAt(0).toUpperCase() + files[currentFile].language.slice(1);
            document.getElementById('file-size').textContent = \`\${size} octets\`;
        }

        // ===== FONCTIONS DES BOUTONS =====
        async function saveFile() {
            if (files[currentFile] && editor) {
                try {
                    // Récupérer le contenu actuel de l'éditeur
                    const content = editor.getValue();
                    files[currentFile].content = content;

                    // Envoyer au serveur
                    const response = await fetch('/api/code-editor/save', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            filename: currentFile,
                            content: content,
                            language: files[currentFile].language
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        console.log(\`💾 Sauvegarde de \${currentFile} réussie\`);
                        document.getElementById('last-saved').textContent = 'Sauvegardé';
                        showNotification(\`Fichier \${currentFile} sauvegardé !\`, 'success');
                    } else {
                        throw new Error(result.error || 'Erreur de sauvegarde');
                    }
                } catch (error) {
                    console.error('Erreur sauvegarde:', error);
                    showNotification('Erreur lors de la sauvegarde', 'error');
                }
            }
        }

        function copyCode() {
            if (!editor) return;

            const selection = editor.getSelection();
            let textToCopy;

            if (selection.isEmpty()) {
                textToCopy = editor.getValue();
                showNotification('Tout le code copié !', 'success');
            } else {
                textToCopy = editor.getModel().getValueInRange(selection);
                showNotification('Sélection copiée !', 'success');
            }

            navigator.clipboard.writeText(textToCopy).then(() => {
                console.log('📋 Code copié dans le presse-papiers');
            }).catch(err => {
                console.error('Erreur lors de la copie:', err);
                showNotification('Erreur lors de la copie', 'error');
            });
        }

        function pasteCode() {
            if (!editor) return;

            navigator.clipboard.readText().then(text => {
                const selection = editor.getSelection();
                editor.executeEdits('paste', [{
                    range: selection,
                    text: text
                }]);
                showNotification('Code collé !', 'success');
                console.log('📋 Code collé depuis le presse-papiers');
            }).catch(err => {
                console.error('Erreur lors du collage:', err);
                showNotification('Erreur lors du collage', 'error');
            });
        }

        async function formatCode() {
            if (editor) {
                try {
                    const code = editor.getValue();
                    const response = await fetch('/api/code-editor/format', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            code: code,
                            language: files[currentFile]?.language || 'javascript'
                        })
                    });

                    const result = await response.json();

                    if (result.success && result.formattedCode) {
                        editor.setValue(result.formattedCode);
                        showNotification('Code formaté !', 'success');
                    } else {
                        // Fallback vers le formatage Monaco
                        editor.getAction('editor.action.formatDocument').run();
                        showNotification('Code formaté !', 'success');
                    }
                } catch (error) {
                    console.error('Erreur formatage:', error);
                    // Fallback vers le formatage Monaco
                    editor.getAction('editor.action.formatDocument').run();
                    showNotification('Code formaté !', 'success');
                }
            }
        }

        async function analyzeCode() {
            if (editor) {
                try {
                    const code = editor.getValue();
                    console.log('🔍 Analyse du code:', code.length, 'caractères');
                    showNotification('Analyse du code en cours...', 'info');

                    const response = await fetch('/api/code-editor/analyze', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            code: code,
                            language: files[currentFile]?.language || 'javascript'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        const analysis = result.analysis;
                        const message = \`Analyse terminée !\\n\\nLignes: \${analysis.lines}\\nFonctions: \${analysis.functions}\\nVariables: \${analysis.variables}\\nScore: \${analysis.score}/100\`;

                        showNotification('Analyse terminée ! Voir la console pour les détails.', 'success');
                        console.log('📊 Résultats d\\'analyse:', analysis);

                        // Afficher les suggestions dans la console
                        if (analysis.suggestions) {
                            console.log('💡 Suggestions:', analysis.suggestions);
                        }
                    } else {
                        throw new Error(result.error || 'Erreur d\\'analyse');
                    }
                } catch (error) {
                    console.error('Erreur analyse:', error);
                    showNotification('Erreur lors de l\\'analyse', 'error');
                }
            }
        }

        function increaseFontSize() {
            if (editor && currentFontSize < 24) {
                currentFontSize += 2;
                editor.updateOptions({ fontSize: currentFontSize });
                showNotification(\`Taille de police: \${currentFontSize}px\`, 'info');
            }
        }

        function decreaseFontSize() {
            if (editor && currentFontSize > 10) {
                currentFontSize -= 2;
                editor.updateOptions({ fontSize: currentFontSize });
                showNotification(\`Taille de police: \${currentFontSize}px\`, 'info');
            }
        }

        function toggleTerminal() {
            const terminal = document.getElementById('terminal');
            if (terminal.classList.contains('visible')) {
                terminal.classList.remove('visible');
                showNotification('Terminal masqué', 'info');
            } else {
                terminal.classList.add('visible');
                showNotification('Terminal affiché', 'info');
            }
        }

        function goHome() {
            window.location.href = '/';
        }

        // ===== GESTION DES MODALS =====
        function showNewFileModal() {
            document.getElementById('new-file-modal').style.display = 'flex';
            document.getElementById('new-file-name').focus();
        }

        function hideNewFileModal() {
            document.getElementById('new-file-modal').style.display = 'none';
            document.getElementById('new-file-name').value = '';
        }

        function createNewFile() {
            const fileName = document.getElementById('new-file-name').value.trim();
            if (!fileName) {
                showNotification('Veuillez entrer un nom de fichier', 'warning');
                return;
            }

            if (files[fileName]) {
                showNotification('Un fichier avec ce nom existe déjà', 'warning');
                return;
            }

            // Déterminer le langage et le contenu
            let language = 'plaintext';
            let content = '';

            if (fileName.endsWith('.js')) {
                language = 'javascript';
                content = \`// \${fileName}\\n// Créé avec l'éditeur Louna Ultra\\n\\nconsole.log('Hello from \${fileName}!');\\n\\n\`;
            } else if (fileName.endsWith('.css')) {
                language = 'css';
                content = \`/* \${fileName} */\\n/* Créé avec l'éditeur Louna Ultra */\\n\\nbody {\\n    margin: 0;\\n    padding: 0;\\n}\\n\\n\`;
            } else if (fileName.endsWith('.html')) {
                language = 'html';
                content = \`<!DOCTYPE html>\\n<html lang="fr">\\n<head>\\n    <meta charset="UTF-8">\\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\\n    <title>\${fileName.replace('.html', '')}</title>\\n</head>\\n<body>\\n    <h1>Hello from \${fileName}!</h1>\\n<script src="/js/auto-init-fixes.js"></script>
</body>\\n</html>\\n\`;
            } else if (fileName.endsWith('.md')) {
                language = 'markdown';
                content = \`# \${fileName.replace('.md', '')}\\n\\nCréé avec l'éditeur Louna Ultra.\\n\\n## Description\\n\\nVotre description ici...\\n\\n\`;
            } else if (fileName.endsWith('.json')) {
                language = 'json';
                content = \`{\\n  "name": "\${fileName.replace('.json', '')}",\\n  "version": "1.0.0",\\n  "description": "Créé avec l'éditeur Louna Ultra"\\n}\\n\`;
            } else if (fileName.endsWith('.py')) {
                language = 'python';
                content = \`# \${fileName}\\n# Créé avec l'éditeur Louna Ultra\\n\\ndef main():\\n    print("Hello from \${fileName}!")\\n\\nif __name__ == "__main__":\\n    main()\\n\`;
            } else {
                content = \`// \${fileName}\\n// Créé avec l'éditeur Louna Ultra\\n\\n\`;
            }

            // Créer le fichier
            files[fileName] = {
                content: content,
                language: language
            };

            // Ajouter à la sidebar
            addFileToSidebar(fileName, language);

            // Ouvrir le nouveau fichier
            switchToFile(fileName);

            hideNewFileModal();
            console.log(\`📄 Nouveau fichier créé: \${fileName}\`);
            showNotification(\`Fichier \${fileName} créé avec succès !\`, 'success');
        }

        function addFileToSidebar(fileName, language) {
            const fileExplorer = document.getElementById('file-explorer');

            // Déterminer l'icône et la couleur
            let icon = 'fas fa-file';
            let color = '#ffffff';

            if (language === 'javascript') {
                icon = 'fab fa-js-square';
                color = '#f7df1e';
            } else if (language === 'css') {
                icon = 'fab fa-css3-alt';
                color = '#1572b6';
            } else if (language === 'html') {
                icon = 'fab fa-html5';
                color = '#e34f26';
            } else if (language === 'markdown') {
                icon = 'fab fa-markdown';
                color = '#083fa1';
            } else if (language === 'json') {
                icon = 'fas fa-cog';
                color = '#68217a';
            } else if (language === 'python') {
                icon = 'fab fa-python';
                color = '#3776ab';
            }

            // Créer l'élément
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.dataset.file = fileName;
            fileItem.dataset.language = language;
            fileItem.innerHTML = \`
                <i class="\${icon} file-icon" style="color: \${color};"></i>
                <span class="file-name">\${fileName}</span>
            \`;

            // Ajouter l'événement de clic
            fileItem.addEventListener('click', function() {
                switchToFile(fileName);
            });

            fileExplorer.appendChild(fileItem);
        }

        // ===== RECHERCHER & REMPLACER =====
        function findAndReplace() {
            document.getElementById('find-replace-modal').style.display = 'flex';
            document.getElementById('find-text').focus();
        }

        function hideFindReplaceModal() {
            document.getElementById('find-replace-modal').style.display = 'none';
            if (editor && findDecorations.length > 0) {
                editor.deltaDecorations(findDecorations, []);
                findDecorations = [];
            }
        }

        function findNext() {
            const findText = document.getElementById('find-text').value;
            if (!findText || !editor) return;

            const caseSensitive = document.getElementById('case-sensitive').checked;
            const wholeWords = document.getElementById('whole-words').checked;

            const matches = editor.getModel().findMatches(
                findText,
                true,
                false,
                caseSensitive,
                wholeWords ? '\\\\b' : null,
                false
            );

            if (matches.length > 0) {
                findDecorations = editor.deltaDecorations(findDecorations, matches.map(match => ({
                    range: match.range,
                    options: {
                        className: 'findMatch',
                        stickiness: 1
                    }
                })));

                editor.setSelection(matches[0].range);
                editor.revealRangeInCenter(matches[0].range);
                showNotification(\`\${matches.length} résultat(s) trouvé(s)\`, 'success');
            } else {
                showNotification('Aucun résultat trouvé', 'warning');
            }
        }

        function replaceOne() {
            const findText = document.getElementById('find-text').value;
            const replaceText = document.getElementById('replace-text').value;
            if (!findText || !editor) return;

            const selection = editor.getSelection();
            const selectedText = editor.getModel().getValueInRange(selection);

            if (selectedText === findText) {
                editor.executeEdits('replace', [{
                    range: selection,
                    text: replaceText
                }]);
                showNotification('Texte remplacé', 'success');
                findNext();
            } else {
                findNext();
            }
        }

        function replaceAll() {
            const findText = document.getElementById('find-text').value;
            const replaceText = document.getElementById('replace-text').value;
            if (!findText || !editor) return;

            const caseSensitive = document.getElementById('case-sensitive').checked;
            const wholeWords = document.getElementById('whole-words').checked;

            const matches = editor.getModel().findMatches(
                findText,
                true,
                false,
                caseSensitive,
                wholeWords ? '\\\\b' : null,
                false
            );

            if (matches.length > 0) {
                editor.executeEdits('replaceAll', matches.map(match => ({
                    range: match.range,
                    text: replaceText
                })));
                showNotification(\`\${matches.length} occurrence(s) remplacée(s)\`, 'success');
                hideFindReplaceModal();
            } else {
                showNotification('Aucun résultat trouvé', 'warning');
            }
        }

        // ===== GÉNÉRATION IA =====
        function generateWithAI() {
            document.getElementById('ai-generation-modal').style.display = 'flex';
            document.getElementById('ai-prompt').focus();
        }

        function hideAIGenerationModal() {
            document.getElementById('ai-generation-modal').style.display = 'none';
            document.getElementById('ai-prompt').value = '';
        }

        async function generateCodeWithAI() {
            const prompt = document.getElementById('ai-prompt').value.trim();
            const language = document.getElementById('ai-language').value;

            if (!prompt) {
                showNotification('Veuillez décrire le code à générer', 'warning');
                return;
            }

            showNotification('🤖 Génération en cours...', 'info');

            try {
                const response = await fetch('/api/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: \`Génère du code \${language} pour: \${prompt}. Réponds uniquement avec le code, sans explication.\`,
                        userId: 'code-editor'
                    })
                });

                const data = await response.json();

                if (data.response) {
                    let generatedCode = data.response;
                    const codeMatch = generatedCode.match(/\`\`\`[\\w]*\\n([\\s\\S]*?)\\n\`\`\`/);
                    if (codeMatch) {
                        generatedCode = codeMatch[1];
                    }

                    if (editor) {
                        const selection = editor.getSelection();
                        editor.executeEdits('ai-generation', [{
                            range: selection,
                            text: generatedCode
                        }]);
                    }

                    hideAIGenerationModal();
                    showNotification('🎉 Code généré avec succès !', 'success');
                } else {
                    showNotification('Erreur lors de la génération', 'error');
                }
            } catch (error) {
                console.error('Erreur IA:', error);
                showNotification('Erreur de connexion à l\\'IA', 'error');
            }
        }

        // ===== RACCOURCIS CLAVIER =====
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    saveFile();
                }
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    showNewFileModal();
                }
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    findAndReplace();
                }
                if (e.ctrlKey && e.key === '`') {
                    e.preventDefault();
                    toggleTerminal();
                }
                if (e.key === 'F5') {
                    e.preventDefault();
                    showNotification('Fonctionnalité d\\'exécution en cours de développement', 'info');
                }
                if (e.key === 'Escape') {
                    hideNewFileModal();
                    hideFindReplaceModal();
                    hideAIGenerationModal();
                }
            });
        }

        function closeTab(fileName, event) {
            if (event) {
                event.stopPropagation();
            }
            console.log(\`❌ Fermeture de l'onglet: \${fileName}\`);
            showNotification(\`Onglet \${fileName} fermé\`, 'info');
        }

        // ===== NAVIGATION =====
        function goHome() {
            console.log('🏠 Retour à l\'accueil');
            showNotification('Retour à l\'accueil...', 'info');
            window.location.href = '/';
        }

        function goToChat() {
            console.log('💬 Redirection vers le chat');
            showNotification('Ouverture du chat...', 'info');
            window.location.href = '/chat';
        }

        // ===== INITIALISATION =====
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initialisation de l\\'éditeur Louna Ultra...');
            initializeEditor();
        });

        console.log('📝 JavaScript de l\\'éditeur Louna Ultra chargé - COMPLÈTEMENT FONCTIONNEL !');
    </script>
<script src="/js/auto-init-fixes.js"></script>
</body>
</html>
