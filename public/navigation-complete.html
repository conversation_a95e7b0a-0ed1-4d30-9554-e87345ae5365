<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Complète - Louna</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .category {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .category h2 {
            color: #4ecdc4;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
        }

        .link {
            display: block;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            text-decoration: none;
            color: #ffffff;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            text-align: center;
            font-size: 0.9rem;
        }

        .link:hover {
            background: rgba(78, 205, 196, 0.2);
            border-color: #4ecdc4;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3);
        }

        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #4ecdc4;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4ecdc4;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Navigation Complète Louna</h1>
            <p>Accès à toutes les interfaces et fonctionnalités</p>
        </div>

        <div class="status">
            <div class="status-item">
                <div class="status-dot"></div>
                <span>Serveur Principal: 3000</span>
            </div>
            <div class="status-item">
                <div class="status-dot"></div>
                <span>Serveur Louna: 3005</span>
            </div>
        </div>

        <div class="categories">
            <!-- Interfaces Principales -->
            <div class="category">
                <h2>🏠 Interfaces Principales</h2>
                <div class="links">
                    <a href="/" class="link">🏠 Accueil</a>
                    <a href="/louna" class="link">🌟 Louna Vision Ultra</a>
                    <a href="/unified-hub.html" class="link">🌟 Hub Central</a>
                    <a href="/agi-dashboard.html" class="link">🧠 AGI Dashboard</a>
                    <a href="/presentation.html" class="link">📋 Présentation</a>
                </div>
            </div>

            <!-- Chat et Communication -->
            <div class="category">
                <h2>💬 Chat et Communication</h2>
                <div class="links">
                    <a href="/chat-simple" class="link">💬 Chat Simple</a>
                    <a href="/chat-complet" class="link">💬 Chat Complet</a>
                    <a href="/chat-ultra-complet" class="link">💬 Chat Ultra</a>
                    <a href="/chat-with-thoughts.html" class="link">💭 Chat + Réflexions</a>
                </div>
            </div>

            <!-- Mémoire et Cerveau -->
            <div class="category">
                <h2>🧠 Mémoire et Cerveau</h2>
                <div class="links">
                    <a href="/futuristic-interface.html" class="link">🌡️ Mémoire Thermique</a>
                    <a href="/brain-visualization.html" class="link">🧠 Cerveau 3D</a>
                    <a href="/real-time-brain-monitor.html" class="link">📊 Monitor Cerveau</a>
                    <a href="/memory-fusion.html" class="link">🔗 Fusion Mémoire</a>
                    <a href="/memory-sync.html" class="link">🔄 Sync Mémoire</a>
                    <a href="/memory-graph.html" class="link">📈 Graphique Mémoire</a>
                </div>
            </div>

            <!-- Monitoring et Performance -->
            <div class="category">
                <h2>📊 Monitoring et Performance</h2>
                <div class="links">
                    <a href="/qi-neuron-monitor.html" class="link">🧠 QI & Neurones</a>
                    <a href="/kyber-dashboard.html" class="link">⚡ Kyber Dashboard</a>
                    <a href="/performance.html" class="link">📈 Performance</a>
                    <a href="/performance-comparison.html" class="link">📊 Comparaison</a>
                    <a href="/system-monitor.html" class="link">🖥️ Système</a>
                </div>
            </div>

            <!-- Développement et Code -->
            <div class="category">
                <h2>💻 Développement et Code</h2>
                <div class="links">
                    <a href="/code-editor.html" class="link">💻 Éditeur Code</a>
                    <a href="/code-extensions.html" class="link">🔌 Extensions</a>
                    <a href="/coding-evolution.html" class="link">📈 Évolution Code</a>
                    <a href="/advanced-course-monitor.html" class="link">🎓 Cours Avancé</a>
                </div>
            </div>

            <!-- Multimédia et Génération -->
            <div class="category">
                <h2>🎨 Multimédia et Génération</h2>
                <div class="links">
                    <a href="/generation-studio.html" class="link">🎨 Studio Génération</a>
                    <a href="/camera-vision-interface.html" class="link">📷 Caméra Vision</a>
                    <a href="/video-analysis.html" class="link">🎬 Analyse Vidéo</a>
                    <a href="/ltx-video.html" class="link">🎥 LTX Vidéo</a>
                    <a href="/cinema-3d.html" class="link">🎬 Cinéma 3D</a>
                </div>
            </div>

            <!-- Agents et Formation -->
            <div class="category">
                <h2>🤖 Agents et Formation</h2>
                <div class="links">
                    <a href="/agents.html" class="link">🤖 Agents</a>
                    <a href="/agent-navigation.html" class="link">🧭 Navigation Agent</a>
                    <a href="/training.html" class="link">🎓 Formation</a>
                    <a href="/training-results.html" class="link">📊 Résultats</a>
                </div>
            </div>

            <!-- Sécurité et Système -->
            <div class="category">
                <h2>🔒 Sécurité et Système</h2>
                <div class="links">
                    <a href="/security-dashboard.html" class="link">🔒 Sécurité</a>
                    <a href="/settings-new.html" class="link">⚙️ Paramètres</a>
                    <a href="/dreams.html" class="link">💭 Rêves</a>
                    <a href="/dreams-new.html" class="link">💭 Nouveaux Rêves</a>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🚀 Application Louna - Toutes les interfaces restaurées et fonctionnelles</p>
            <p>Développé par Jean-Luc Passave - Sainte-Anne, Guadeloupe</p>
        </div>
    </div>

    <script>
        // Animation des liens au chargement
        document.addEventListener('DOMContentLoaded', () => {
            const links = document.querySelectorAll('.link');
            links.forEach((link, index) => {
                link.style.opacity = '0';
                link.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    link.style.transition = 'all 0.5s ease';
                    link.style.opacity = '1';
                    link.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });

        // Test de connectivité
        function testConnectivity() {
            fetch('/api/system/status')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Connectivité OK');
                    }
                })
                .catch(error => {
                    console.log('⚠️ Problème de connectivité:', error);
                });
        }

        // Test initial
        testConnectivity();
        
        // Test périodique
        setInterval(testConnectivity, 30000);
    </script>
</body>
</html>
