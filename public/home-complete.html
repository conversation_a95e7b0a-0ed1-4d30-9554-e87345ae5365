<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Hub Central Complet</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .app-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .app-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-color: #667eea;
        }

        .app-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #667eea;
            text-align: center;
        }

        .app-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
            color: #333;
        }

        .app-description {
            color: #666;
            text-align: center;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .app-status {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #28a745;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            color: white;
        }

        .stats-title {
            text-align: center;
            font-size: 1.8rem;
            margin-bottom: 25px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: white;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .category-title {
            color: white;
            font-size: 1.5rem;
            margin: 40px 0 20px 0;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        /* Couleurs spécifiques par catégorie */
        .app-card.chat .app-icon { color: #e91e63; }
        .app-card.memory .app-icon { color: #ff5722; }
        .app-card.brain .app-icon { color: #9c27b0; }
        .app-card.generation .app-icon { color: #2196f3; }
        .app-card.monitoring .app-icon { color: #4caf50; }
        .app-card.development .app-icon { color: #ff9800; }
        .app-card.system .app-icon { color: #607d8b; }

        @media (max-width: 768px) {
            .apps-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-brain"></i> Louna - Hub Central Complet</h1>
        <p>Accédez à toutes vos applications d'intelligence artificielle</p>
    </div>

    <div class="container">
        <!-- Statistiques en temps réel -->
        <div class="stats-section">
            <h2 class="stats-title">Statistiques du Système</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="qi-value">150</div>
                    <div class="stat-label">QI Système</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="memory-temp">42.3°C</div>
                    <div class="stat-label">Mémoire Thermique</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="neuron-count">71</div>
                    <div class="stat-label">Neurones Actifs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="kyber-count">8</div>
                    <div class="stat-label">Accélérateurs Kyber</div>
                </div>
            </div>
        </div>

        <!-- Applications principales -->
        <h2 class="category-title">🎯 Applications Principales</h2>
        <div class="apps-grid">
            <!-- Chat Intelligent -->
            <div class="app-card chat" onclick="openApp('/chat')">
                <div class="app-icon"><i class="fas fa-comments"></i></div>
                <div class="app-title">Chat Intelligent</div>
                <div class="app-description">Interface de conversation avancée avec l'agent Louna</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>En ligne</span>
                </div>
            </div>

            <!-- Mémoire Thermique -->
            <div class="app-card memory" onclick="openApp('/futuristic-interface.html')">
                <div class="app-icon"><i class="fas fa-fire"></i></div>
                <div class="app-title">Mémoire Thermique</div>
                <div class="app-description">Système de mémoire thermique avec 5 zones distinctes</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Actif - 42.3°C</span>
                </div>
            </div>

            <!-- Cerveau 3D -->
            <div class="app-card brain" onclick="openApp('/brain-visualization.html')">
                <div class="app-icon"><i class="fas fa-brain"></i></div>
                <div class="app-title">Visualisation Cerveau 3D</div>
                <div class="app-description">Visualisation 3D du cerveau artificiel en temps réel</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>71 neurones actifs</span>
                </div>
            </div>

            <!-- QI Monitor -->
            <div class="app-card monitoring" onclick="openApp('/qi-neuron-monitor.html')">
                <div class="app-icon"><i class="fas fa-yin-yang"></i></div>
                <div class="app-title">Monitoring QI & Neurones</div>
                <div class="app-description">Surveillance en temps réel du QI et de l'activité neuronale</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>QI: 150</span>
                </div>
            </div>

            <!-- Accélérateurs Kyber -->
            <div class="app-card system" onclick="openApp('/kyber-dashboard.html')">
                <div class="app-icon"><i class="fas fa-bolt"></i></div>
                <div class="app-title">Accélérateurs Kyber</div>
                <div class="app-description">Système d'accélération quantique pour boost de performance</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>8 accélérateurs actifs</span>
                </div>
            </div>

            <!-- Studio de Génération -->
            <div class="app-card generation" onclick="openApp('/generation-studio.html')">
                <div class="app-icon"><i class="fas fa-magic"></i></div>
                <div class="app-title">Studio de Génération</div>
                <div class="app-description">Création de contenu multimédia : vidéos, images, musique, 3D</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Prêt à créer</span>
                </div>
            </div>
        </div>

        <!-- Applications secondaires -->
        <h2 class="category-title">🛠️ Outils & Développement</h2>
        <div class="apps-grid">
            <!-- Éditeur de Code -->
            <div class="app-card development" onclick="openApp('/code-editor.html')">
                <div class="app-icon"><i class="fas fa-code"></i></div>
                <div class="app-title">Éditeur de Code</div>
                <div class="app-description">Éditeur de code intelligent avec assistance IA</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Prêt</span>
                </div>
            </div>

            <!-- Gestion des Agents -->
            <div class="app-card system" onclick="openApp('/agents.html')">
                <div class="app-icon"><i class="fas fa-users"></i></div>
                <div class="app-title">Gestion des Agents</div>
                <div class="app-description">Configuration et gestion des agents IA</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Disponible</span>
                </div>
            </div>

            <!-- Formation -->
            <div class="app-card development" onclick="openApp('/training.html')">
                <div class="app-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="app-title">Module de Formation</div>
                <div class="app-description">Formation et amélioration continue de l'agent</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>En cours</span>
                </div>
            </div>
        </div>

        <!-- Applications système -->
        <h2 class="category-title">⚙️ Système & Sécurité</h2>
        <div class="apps-grid">
            <!-- Sécurité -->
            <div class="app-card system" onclick="openApp('/security-dashboard.html')">
                <div class="app-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="app-title">Dashboard Sécurité</div>
                <div class="app-description">Monitoring de sécurité et protection système</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Sécurisé</span>
                </div>
            </div>

            <!-- Performance -->
            <div class="app-card monitoring" onclick="openApp('/performance.html')">
                <div class="app-icon"><i class="fas fa-chart-line"></i></div>
                <div class="app-title">Monitoring Performance</div>
                <div class="app-description">Surveillance des performances système en temps réel</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Optimal</span>
                </div>
            </div>

            <!-- Paramètres -->
            <div class="app-card system" onclick="openApp('/settings-new.html')">
                <div class="app-icon"><i class="fas fa-cog"></i></div>
                <div class="app-title">Paramètres Système</div>
                <div class="app-description">Configuration générale de l'application</div>
                <div class="app-status">
                    <div class="status-dot"></div>
                    <span>Accessible</span>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>Louna - Agent IA Révolutionnaire &copy; 2025</p>
        <p>Créé par Jean-Luc Passave - Sainte-Anne, Guadeloupe</p>
    </div>

    <script>
        // Fonction pour ouvrir une application
        function openApp(url) {
            console.log('🚀 Ouverture de:', url);
            window.location.href = url;
        }

        // Animation des statistiques
        function updateStats() {
            const qiValue = document.getElementById('qi-value');
            const memoryTemp = document.getElementById('memory-temp');
            const neuronCount = document.getElementById('neuron-count');
            const kyberCount = document.getElementById('kyber-count');

            // Simulation de données en temps réel
            setInterval(() => {
                const qi = 145 + Math.floor(Math.random() * 10);
                const temp = (40 + Math.random() * 5).toFixed(1);
                const neurons = 65 + Math.floor(Math.random() * 15);
                const kyber = 6 + Math.floor(Math.random() * 4);

                qiValue.textContent = qi;
                memoryTemp.textContent = temp + '°C';
                neuronCount.textContent = neurons;
                kyberCount.textContent = kyber;
            }, 3000);
        }

        // Démarrer les animations
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            console.log('🎯 Hub Central Louna initialisé');
        });
    </script>
</body>
</html>
