<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Cognitif Complet - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-unified-design.css">
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/native-app.css">
    <link rel="stylesheet" href="/css/chat-cognitif-advanced.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-title h1 {
            font-size: 1.5rem;
            background: linear-gradient(45deg, #ff6b9d, #c44569);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-controls {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Layout principal */
        .main-layout {
            display: grid;
            grid-template-columns: 1fr 350px 300px;
            height: calc(100vh - 80px);
            gap: 1px;
            background: rgba(255, 255, 255, 0.1);
        }

        /* Zone de chat */
        .chat-zone {
            background: rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .chat-status {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #4ade80;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #4ade80;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            color: white;
        }

        .message.agent {
            align-self: flex-start;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-content {
            margin-bottom: 5px;
        }

        .message-time {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .message-stats {
            font-size: 0.7rem;
            opacity: 0.6;
            margin-top: 5px;
        }

        /* Zone d'input */
        .chat-input-zone {
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-wrapper {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: white;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }

        .chat-input:focus {
            border-color: #ff6b9d;
            box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .control-btn {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .control-btn.active {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            box-shadow: 0 0 15px rgba(255, 107, 157, 0.5);
        }

        .send-btn {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
        }

        .send-btn:hover {
            background: linear-gradient(135deg, #c44569, #ff6b9d);
        }

        /* Zone caméra/vidéo */
        .camera-zone {
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
        }

        .camera-header {
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .camera-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .camera-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }

        .camera-placeholder {
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
        }

        .camera-controls {
            padding: 15px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        /* Zone de réflexions */
        .reflections-zone {
            background: rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }

        .reflections-header {
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .reflection-filters {
            display: flex;
            gap: 5px;
        }

        .filter-btn {
            padding: 4px 8px;
            font-size: 0.8rem;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
        }

        .reflections-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }

        .reflection-item {
            padding: 8px 12px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid;
            font-size: 0.9rem;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(-10px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .reflection-item.thinking { border-color: #3b82f6; }
        .reflection-item.memory { border-color: #8b5cf6; }
        .reflection-item.internet { border-color: #10b981; }
        .reflection-item.response { border-color: #f59e0b; }
        .reflection-item.complete { border-color: #ef4444; }

        .reflection-time {
            font-size: 0.7rem;
            opacity: 0.6;
            margin-top: 4px;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr 300px;
            }
            .camera-zone {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
            .reflections-zone {
                display: none;
            }
        }

        /* Typing indicator */
        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 18px;
            max-width: 80px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #ff6b9d;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-title">
            <i class="fas fa-brain"></i>
            <h1>Chat Cognitif Complet - Louna</h1>
        </div>
        <div class="header-controls">
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i> Accueil
            </a>
            <a href="/futuristic-interface.html" class="nav-btn">
                <i class="fas fa-fire"></i> Mémoire
            </a>
            <a href="/brain-visualization.html" class="nav-btn">
                <i class="fas fa-cube"></i> Cerveau 3D
            </a>
        </div>
    </div>

    <!-- Layout principal -->
    <div class="main-layout">
        <!-- Zone de chat -->
        <div class="chat-zone">
            <div class="chat-header">
                <div class="chat-title">
                    <i class="fas fa-comments"></i>
                    Conversation avec Louna
                </div>
                <div class="chat-status">
                    <div class="status-dot"></div>
                    <span>Agent connecté</span>
                </div>
            </div>

            <!-- Métriques de performance -->
            <div class="performance-metrics">
                <div class="metric-item">
                    <div class="metric-label">Temps de réponse</div>
                    <div class="metric-value" id="response-time">--ms</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Boost KYBER</div>
                    <div class="metric-value" id="kyber-boost">--x</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Mémoire</div>
                    <div class="metric-value" id="memory-usage">--%</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Température</div>
                    <div class="metric-value" id="thermal-temp">--°C</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">QI</div>
                    <div class="metric-value" id="qi-level">--</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Neurones</div>
                    <div class="metric-value" id="neuron-count">--</div>
                </div>
            </div>

            <div class="chat-messages" id="chat-messages">
                <div class="message agent">
                    <div class="message-content">
                        🧠 Bonjour ! Je suis Louna, votre assistant IA révolutionnaire créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe.
                        <br><br>
                        ✨ <strong>Mes capacités cognitives complètes :</strong>
                        <br>🔥 Mémoire thermique biologique
                        <br>⚡ Accélérateurs KYBER ultra-rapides
                        <br>🌐 Accès Internet complet via MCP
                        <br>📹 Vision par caméra en temps réel
                        <br>🎤 Reconnaissance vocale avancée
                        <br>🧠 Système de réflexion visible
                        <br><br>
                        Posez-moi une question et observez mes réflexions internes !
                    </div>
                    <div class="message-time">Maintenant</div>
                </div>

                <div class="typing-indicator" id="typing-indicator">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <div class="chat-input-zone">
                <div class="input-wrapper">
                    <button class="control-btn" id="camera-btn" title="Activer/Désactiver la caméra">
                        <i class="fas fa-video"></i>
                    </button>
                    <button class="control-btn" id="mic-btn" title="Activer/Désactiver le micro">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <input type="text" class="chat-input" id="chat-input"
                           placeholder="Tapez votre message ou parlez..." autocomplete="off">
                    <button class="control-btn send-btn" id="send-btn" title="Envoyer le message">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Zone caméra/vidéo -->
        <div class="camera-zone">
            <div class="camera-header">
                <h3><i class="fas fa-video"></i> Vision Cognitive</h3>
            </div>
            <div class="camera-container" id="camera-container">
                <div class="camera-placeholder" id="camera-placeholder">
                    <i class="fas fa-video-slash" style="font-size: 3rem; margin-bottom: 10px;"></i>
                    <p>Caméra désactivée</p>
                    <p style="font-size: 0.8rem; margin-top: 5px;">Cliquez sur le bouton caméra pour activer</p>
                </div>
                <video class="camera-video" id="camera-video" autoplay muted style="display: none;"></video>
            </div>
            <div class="camera-controls">
                <button class="control-btn" id="snapshot-btn" title="Prendre une capture">
                    <i class="fas fa-camera"></i>
                </button>
                <button class="control-btn" id="record-btn" title="Enregistrer vidéo">
                    <i class="fas fa-record-vinyl"></i>
                </button>
            </div>
        </div>

        <!-- Zone de réflexions -->
        <div class="reflections-zone">
            <div class="reflections-header">
                <h3><i class="fas fa-brain"></i> Réflexions</h3>
                <div class="reflection-filters">
                    <button class="filter-btn active" data-filter="all">Tout</button>
                    <button class="filter-btn" data-filter="thinking">💭</button>
                    <button class="filter-btn" data-filter="memory">🧠</button>
                    <button class="filter-btn" data-filter="internet">🌐</button>
                    <button class="filter-btn" data-filter="response">💬</button>
                </div>
            </div>
            <div class="reflections-content" id="reflections-content">
                <div class="reflection-item thinking">
                    <div>🧠 Système cognitif initialisé avec succès</div>
                    <div class="reflection-time">Maintenant</div>
                </div>
                <div class="reflection-item memory">
                    <div>🔥 Mémoire thermique biologique active</div>
                    <div class="reflection-time">Maintenant</div>
                </div>
                <div class="reflection-item internet">
                    <div>🌐 Connexion MCP établie - Accès Internet opérationnel</div>
                    <div class="reflection-time">Maintenant</div>
                </div>
            </div>

            <!-- Contrôles avancés -->
            <div class="advanced-controls" id="advanced-controls">
                <div class="advanced-controls-header" id="advanced-controls-toggle">
                    <span class="advanced-controls-title">Contrôles Avancés</span>
                    <i class="fas fa-chevron-down advanced-controls-toggle"></i>
                </div>
                <div class="advanced-controls-panel" id="advanced-controls-panel">
                    <div class="advanced-controls-content">
                        <!-- Accélérateurs KYBER -->
                        <div class="control-section">
                            <h4><i class="fas fa-rocket"></i> Accélérateurs KYBER</h4>
                            <div class="accelerator-list" id="accelerator-list">
                                <!-- Les accélérateurs apparaîtront ici -->
                            </div>
                        </div>

                        <!-- Actions rapides -->
                        <div class="control-section">
                            <h4><i class="fas fa-bolt"></i> Actions Rapides</h4>
                            <div class="quick-actions">
                                <button class="action-btn" onclick="optimizeMemory()">
                                    <i class="fas fa-memory"></i> Optimiser Mémoire
                                </button>
                                <button class="action-btn" onclick="boostPerformance()">
                                    <i class="fas fa-tachometer-alt"></i> Boost Performance
                                </button>
                                <button class="action-btn" onclick="clearCache()">
                                    <i class="fas fa-trash"></i> Vider Cache
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/notifications.js"></script>
    <script src="/js/chat-cognitif-advanced.js"></script>
    <script>
        // Variables globales
        let cameraStream = null;
        let cameraActive = false;
        let micActive = false;
        let isRecording = false;
        let recognition = null;
        let messageHistory = [];
        let reflectionStream = null;

        // Éléments DOM
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        const cameraBtn = document.getElementById('camera-btn');
        const micBtn = document.getElementById('mic-btn');
        const cameraVideo = document.getElementById('camera-video');
        const cameraPlaceholder = document.getElementById('camera-placeholder');
        const typingIndicator = document.getElementById('typing-indicator');
        const reflectionsContent = document.getElementById('reflections-content');

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Chat Cognitif Complet initialisé');

            // Configurer les événements
            setupEventListeners();

            // Initialiser la reconnaissance vocale
            initSpeechRecognition();

            // Charger toutes les réflexions existantes
            loadAllReflections();

            // Initialiser le stream de réflexions
            initReflectionStream();

            // Afficher une notification de bienvenue
            showNotification('🧠 Chat Cognitif Complet prêt !', 'success');
        });

        function setupEventListeners() {
            // Bouton d'envoi
            sendBtn.addEventListener('click', sendMessage);

            // Entrée clavier
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Boutons de contrôle
            cameraBtn.addEventListener('click', toggleCamera);
            micBtn.addEventListener('click', toggleMicrophone);

            // Filtres de réflexions
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    filterReflections(this.dataset.filter);
                });
            });
        }

        // === FONCTIONS DE CAMÉRA ===
        async function toggleCamera() {
            if (cameraActive) {
                stopCamera();
            } else {
                await startCamera();
            }
        }

        async function startCamera() {
            try {
                console.log('📹 Démarrage de la caméra...');
                addReflection('thinking', '📹 Activation du système de vision cognitive...');

                cameraStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        aspectRatio: 16/9
                    },
                    audio: false
                });

                cameraVideo.srcObject = cameraStream;
                cameraVideo.style.display = 'block';
                cameraPlaceholder.style.display = 'none';

                cameraActive = true;
                cameraBtn.classList.add('active');
                cameraBtn.querySelector('i').className = 'fas fa-video';

                console.log('✅ Caméra activée');
                addReflection('complete', '✅ Vision cognitive activée - Je peux maintenant vous voir !');
                showNotification('📹 Caméra activée - Vision cognitive opérationnelle !', 'success');

            } catch (error) {
                console.error('❌ Erreur caméra:', error);
                addReflection('response', '❌ Impossible d\'activer la caméra: ' + error.message);
                showNotification('❌ Impossible d\'activer la caméra', 'error');
            }
        }

        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
            }

            cameraVideo.style.display = 'none';
            cameraPlaceholder.style.display = 'block';

            cameraActive = false;
            cameraBtn.classList.remove('active');
            cameraBtn.querySelector('i').className = 'fas fa-video-slash';

            console.log('📹 Caméra arrêtée');
            addReflection('thinking', '📹 Vision cognitive désactivée');
            showNotification('📹 Caméra désactivée', 'info');
        }

        // === FONCTIONS DE MICROPHONE ===
        function toggleMicrophone() {
            if (micActive) {
                stopMicrophone();
            } else {
                startMicrophone();
            }
        }

        function startMicrophone() {
            if (!recognition) {
                showNotification('❌ Reconnaissance vocale non supportée', 'error');
                return;
            }

            try {
                micActive = true;
                micBtn.classList.add('active');
                micBtn.querySelector('i').className = 'fas fa-microphone';

                recognition.start();

                console.log('🎤 Microphone activé');
                addReflection('thinking', '🎤 Système de reconnaissance vocale activé');
                showNotification('🎤 Microphone activé - Parlez maintenant !', 'success');

            } catch (error) {
                console.error('❌ Erreur microphone:', error);
                addReflection('response', '❌ Erreur microphone: ' + error.message);
                showNotification('❌ Erreur microphone', 'error');
            }
        }

        function stopMicrophone() {
            if (recognition) {
                recognition.stop();
            }

            micActive = false;
            micBtn.classList.remove('active');
            micBtn.querySelector('i').className = 'fas fa-microphone-slash';

            console.log('🎤 Microphone arrêté');
            addReflection('thinking', '🎤 Reconnaissance vocale désactivée');
            showNotification('🎤 Microphone désactivé', 'info');
        }

        // === RECONNAISSANCE VOCALE ===
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'fr-FR';

                recognition.onstart = function() {
                    console.log('🎤 Reconnaissance vocale démarrée');
                    addReflection('thinking', '🎤 Écoute en cours...');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    console.log('🎤 Texte reconnu:', transcript);

                    chatInput.value = transcript;
                    addReflection('response', '🎤 Texte reconnu: "' + transcript + '"');

                    // Envoyer automatiquement le message
                    setTimeout(() => {
                        sendMessage();
                    }, 500);
                };

                recognition.onerror = function(event) {
                    console.error('❌ Erreur reconnaissance vocale:', event.error);
                    addReflection('response', '❌ Erreur reconnaissance: ' + event.error);
                    stopMicrophone();
                };

                recognition.onend = function() {
                    console.log('🎤 Reconnaissance vocale terminée');
                    stopMicrophone();
                };

                console.log('✅ Reconnaissance vocale initialisée');
                addReflection('complete', '✅ Système de reconnaissance vocale prêt');
            } else {
                console.warn('⚠️ Reconnaissance vocale non supportée');
                addReflection('response', '⚠️ Reconnaissance vocale non supportée par ce navigateur');
            }
        }

        // === FONCTIONS DE CHAT ===
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            // Ajouter le message utilisateur
            addMessage('user', message);
            chatInput.value = '';

            // Afficher l'indicateur de frappe
            showTypingIndicator();

            // Ajouter des réflexions
            addReflection('thinking', '💭 Analyse du message: "' + message + '"');
            addReflection('memory', '🧠 Recherche dans la mémoire thermique...');

            try {
                const response = await fetch('/api/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: message })
                });

                if (!response.ok) {
                    throw new Error('Erreur réseau: ' + response.status);
                }

                const data = await response.json();

                // Masquer l'indicateur de frappe
                hideTypingIndicator();

                // Ajouter la réponse
                addMessage('agent', data.response, data.stats);

                // Ajouter des réflexions sur la réponse
                if (data.stats) {
                    addReflection('complete', `⚡ Réponse générée en ${data.stats.processingTime}ms`);
                    if (data.stats.kyberBoost) {
                        addReflection('response', `🚀 Accélérateurs KYBER: ${data.stats.kyberBoost.toFixed(1)}x boost`);
                    }
                    if (data.stats.memoryUsed) {
                        addReflection('memory', `🧠 Mémoires utilisées: ${data.stats.memoryUsed}`);
                    }
                }

            } catch (error) {
                console.error('❌ Erreur envoi message:', error);
                hideTypingIndicator();
                addMessage('agent', '❌ Désolé, une erreur est survenue: ' + error.message);
                addReflection('response', '❌ Erreur de communication: ' + error.message);
                showNotification('❌ Erreur de communication', 'error');
            }
        }

        function addMessage(type, content, stats = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const now = new Date();
            const timeStr = now.toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit'
            });

            let statsHtml = '';
            if (stats) {
                statsHtml = `<div class="message-stats">
                    ⚡ ${stats.processingTime}ms
                    ${stats.kyberBoost ? ` | 🚀 ${stats.kyberBoost.toFixed(1)}x boost` : ''}
                    ${stats.memoryUsed ? ` | 🧠 ${stats.memoryUsed} mémoires` : ''}
                </div>`;
            }

            messageDiv.innerHTML = `
                <div class="message-content">${content}</div>
                <div class="message-time">${timeStr}</div>
                ${statsHtml}
            `;

            // Insérer avant l'indicateur de frappe
            chatMessages.insertBefore(messageDiv, typingIndicator);

            // Faire défiler vers le bas
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // Ajouter à l'historique
            messageHistory.push({ type, content, time: now, stats });
        }

        function showTypingIndicator() {
            typingIndicator.style.display = 'flex';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        // === FONCTIONS DE RÉFLEXIONS ===
        function addReflection(type, content) {
            const reflectionDiv = document.createElement('div');
            reflectionDiv.className = `reflection-item ${type}`;

            const now = new Date();
            const timeStr = now.toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            reflectionDiv.innerHTML = `
                <div>${content}</div>
                <div class="reflection-time">${timeStr}</div>
            `;

            reflectionsContent.appendChild(reflectionDiv);
            reflectionsContent.scrollTop = reflectionsContent.scrollHeight;
        }

        function filterReflections(filter) {
            const reflections = document.querySelectorAll('.reflection-item');
            reflections.forEach(reflection => {
                if (filter === 'all' || reflection.classList.contains(filter)) {
                    reflection.style.display = 'block';
                } else {
                    reflection.style.display = 'none';
                }
            });
        }

        // === CHARGEMENT DE TOUTES LES RÉFLEXIONS ===
        async function loadAllReflections() {
            try {
                showNotification('📖 Chargement de toutes les réflexions...', 'info');
                console.log('📖 Chargement de toutes les réflexions stockées...');

                const response = await fetch('/api/reflections/load-complete');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.reflections) {
                        // Vider le conteneur de réflexions (garder seulement les réflexions de base)
                        const baseReflections = reflectionsContent.querySelectorAll('.reflection-item');

                        // Ajouter toutes les réflexions chargées
                        data.reflections.forEach(reflection => {
                            const reflectionDiv = document.createElement('div');
                            reflectionDiv.className = `reflection-item ${reflection.type}`;

                            const timeStr = reflection.displayTime || new Date(reflection.timestamp).toLocaleTimeString('fr-FR', {
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });

                            reflectionDiv.innerHTML = `
                                <div>${reflection.text}</div>
                                <div class="reflection-time">${timeStr}</div>
                            `;

                            reflectionsContent.appendChild(reflectionDiv);
                        });

                        // Faire défiler vers le bas pour voir les plus récentes
                        reflectionsContent.scrollTop = reflectionsContent.scrollHeight;

                        showNotification(`✅ ${data.total} réflexions chargées depuis ${data.files} fichiers + mémoire`, 'success');
                        console.log(`📖 Toutes les réflexions chargées: ${data.total} total depuis ${data.files} fichiers + ${data.memoryReflections} en mémoire`);

                        // Ajouter une réflexion pour indiquer le chargement complet
                        addReflection('complete', `📖 Historique complet chargé: ${data.total} réflexions depuis ${data.files} fichiers`);
                    }
                } else {
                    throw new Error('Erreur serveur: ' + response.status);
                }
            } catch (error) {
                console.error('❌ Erreur chargement réflexions:', error);
                showNotification('❌ Erreur lors du chargement des réflexions', 'error');
                addReflection('response', '❌ Erreur chargement historique: ' + error.message);

                // Fallback vers les réflexions récentes
                try {
                    const fallbackResponse = await fetch('/api/reflections?limit=50');
                    if (fallbackResponse.ok) {
                        const fallbackData = await fallbackResponse.json();
                        if (fallbackData.success && fallbackData.reflections) {
                            fallbackData.reflections.forEach(reflection => {
                                addReflection(reflection.type, reflection.text);
                            });
                            showNotification(`⚠️ Chargement partiel: ${fallbackData.total} réflexions récentes`, 'warning');
                        }
                    }
                } catch (fallbackError) {
                    console.error('❌ Erreur fallback:', fallbackError);
                }
            }
        }

        // === STREAM DE RÉFLEXIONS ===
        function initReflectionStream() {
            // Simuler des réflexions automatiques
            setInterval(() => {
                const reflections = [
                    { type: 'thinking', content: '🧠 Analyse des patterns de conversation...' },
                    { type: 'memory', content: '🔥 Optimisation de la mémoire thermique...' },
                    { type: 'internet', content: '🌐 Vérification des connexions MCP...' },
                    { type: 'response', content: '⚡ Accélérateurs KYBER en veille...' }
                ];

                const randomReflection = reflections[Math.floor(Math.random() * reflections.length)];
                addReflection(randomReflection.type, randomReflection.content);
            }, 15000); // Toutes les 15 secondes
        }

        // === NOTIFICATIONS ===
        function showNotification(message, type = 'info') {
            // Utiliser le système de notifications existant si disponible
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                console.log(`[${type.toUpperCase()}] ${message}`);
            }
        }

        // === ACTIONS RAPIDES ===
        async function optimizeMemory() {
            showNotification('🧠 Optimisation de la mémoire en cours...', 'info');
            addReflection('memory', '🧠 Démarrage de l\'optimisation mémoire...');

            try {
                const response = await fetch('/api/thermal/memory/optimize', {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    showNotification('✅ Mémoire optimisée avec succès !', 'success');
                    addReflection('complete', `✅ Optimisation terminée: ${data.memoryFreed} libérés`);
                } else {
                    throw new Error('Erreur serveur');
                }
            } catch (error) {
                showNotification('❌ Erreur lors de l\'optimisation', 'error');
                addReflection('response', '❌ Erreur optimisation: ' + error.message);
            }
        }

        async function boostPerformance() {
            showNotification('⚡ Activation du boost de performance...', 'info');
            addReflection('thinking', '⚡ Activation des accélérateurs KYBER...');

            try {
                const response = await fetch('/api/kyber/boost', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ level: 'max' })
                });

                if (response.ok) {
                    const data = await response.json();
                    showNotification('🚀 Performance boostée !', 'success');
                    addReflection('complete', `🚀 Boost activé: ${data.boostLevel}x performance`);
                } else {
                    throw new Error('Erreur serveur');
                }
            } catch (error) {
                showNotification('❌ Erreur lors du boost', 'error');
                addReflection('response', '❌ Erreur boost: ' + error.message);
            }
        }

        async function clearCache() {
            showNotification('🗑️ Nettoyage du cache en cours...', 'info');
            addReflection('thinking', '🗑️ Suppression des fichiers temporaires...');

            try {
                const response = await fetch('/api/system/clear-cache', {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    showNotification('✅ Cache nettoyé !', 'success');
                    addReflection('complete', `✅ Cache nettoyé: ${data.filesRemoved} fichiers supprimés`);
                } else {
                    throw new Error('Erreur serveur');
                }
            } catch (error) {
                showNotification('❌ Erreur lors du nettoyage', 'error');
                addReflection('response', '❌ Erreur nettoyage: ' + error.message);
            }
        }
    </script>
</body>
</html>
