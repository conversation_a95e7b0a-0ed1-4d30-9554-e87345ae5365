<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Interface de Chat Ultra-Complète</title>

    <!-- Styles CSS -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/native-app.css">
    <link rel="stylesheet" href="/css/contrast-fixes.css">

    <!-- Icônes Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        /* ===== STYLES POUR L'INTERFACE DE CHAT ULTRA-COMPLÈTE ===== */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* ===== HEADER ===== */
        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1600px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 24px;
            font-weight: 700;
            color: #9c89b8;
        }

        .logo i {
            font-size: 28px;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        .status-dot.disconnected {
            background: #f44336;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* ===== CONTENEUR PRINCIPAL ===== */
        .main-container {
            max-width: 1600px;
            margin: 20px auto;
            padding: 0 20px;
            display: flex;
            gap: 15px;
            height: calc(100vh - 120px);
        }

        /* ===== INTERFACE DE CHAT ===== */
        .chat-interface {
            flex: 2;
            display: flex;
            flex-direction: column;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
            min-width: 0;
        }

        .chat-header {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #9c89b8;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-controls {
            display: flex;
            gap: 8px;
        }

        .chat-control-btn {
            background: rgba(156, 137, 184, 0.2);
            border: 1px solid rgba(156, 137, 184, 0.3);
            color: #9c89b8;
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .chat-control-btn:hover {
            background: rgba(156, 137, 184, 0.4);
            color: white;
        }

        /* ===== ZONE DE MESSAGES ===== */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .messages-container::-webkit-scrollbar {
            width: 8px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: rgba(156, 137, 184, 0.5);
            border-radius: 4px;
        }

        /* ===== MESSAGES ===== */
        .message {
            display: flex;
            gap: 12px;
            max-width: 80%;
            animation: fadeInUp 0.3s ease-out;
            position: relative;
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message.assistant {
            align-self: flex-start;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 18px;
        }

        .message-avatar.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message-avatar.assistant {
            background: linear-gradient(135deg, #9c89b8 0%, #a8a4ce 100%);
            color: white;
        }

        .message-bubble {
            padding: 15px 20px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.assistant .message-bubble {
            background: rgba(156, 137, 184, 0.2);
            border: 1px solid rgba(156, 137, 184, 0.3);
            color: #ffffff;
            border-bottom-left-radius: 5px;
        }

        .message-content {
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            text-align: right;
        }

        .message.assistant .message-time {
            text-align: left;
        }

        .message-actions {
            position: absolute;
            top: -10px;
            right: 10px;
            display: none;
            gap: 5px;
        }

        .message:hover .message-actions {
            display: flex;
        }

        .message-action-btn {
            background: rgba(0, 0, 0, 0.7);
            border: none;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* ===== OPTIONS DE CHAT ===== */
        .chat-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 10px;
            padding: 0 15px;
        }

        .chat-option-btn {
            background-color: rgba(255, 105, 180, 0.2);
            color: #ff69b4;
            border: 1px solid rgba(255, 105, 180, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .chat-option-btn:hover {
            background-color: rgba(255, 105, 180, 0.4);
            color: #ffffff;
            transform: translateY(-2px);
        }

        .chat-option-btn.active {
            background-color: rgba(255, 105, 180, 0.6);
            color: #ffffff;
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        /* ===== ZONE DE SAISIE ===== */
        .input-container {
            padding: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.1);
        }

        .input-wrapper {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 20px;
            color: #ffffff;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            transition: all 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #9c89b8;
            box-shadow: 0 0 0 2px rgba(156, 137, 184, 0.2);
        }

        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .send-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #9c89b8 0%, #a8a4ce 100%);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(156, 137, 184, 0.4);
        }

        .send-button:active {
            transform: scale(0.95);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* ===== SIDEBAR ===== */
        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .sidebar-section {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: #9c89b8;
            margin-bottom: 10px;
        }

        .qi-display {
            text-align: center;
            padding: 20px;
        }

        .qi-value {
            font-size: 36px;
            font-weight: 700;
            color: #9c89b8;
            margin-bottom: 5px;
        }

        .qi-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 1200px) {
            .main-container {
                flex-direction: column;
                gap: 10px;
            }

            .sidebar {
                order: -1;
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 0 10px;
            }

            .message {
                max-width: 90%;
            }

            .chat-options {
                gap: 5px;
            }

            .chat-option-btn {
                padding: 6px 10px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-moon"></i>
                <span>Louna</span>
            </div>
            <div class="header-controls">
                <div class="status-indicator">
                    <div class="status-dot" id="connectionStatus"></div>
                    <span id="connectionText">Connexion...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Conteneur principal -->
    <div class="main-container">
        <!-- Interface de chat -->
        <div class="chat-interface">
            <div class="chat-header">
                <div class="chat-title">
                    <i class="fas fa-robot"></i>
                    Vision Ultra
                </div>
                <div class="chat-controls">
                    <button class="chat-control-btn" id="clearChatBtn" title="Effacer la conversation">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="chat-control-btn" id="exportChatBtn" title="Exporter la conversation">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>

            <div class="messages-container" id="messagesContainer">
                <!-- Message de bienvenue -->
                <div class="message assistant">
                    <div class="message-avatar assistant">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-bubble">
                        <div class="message-content">
                            Bonjour ! Je suis Vision Ultra, votre assistant cognitif créé par Jean Passave à Sainte-Anne, Guadeloupe. Je suis équipé d'une mémoire thermique, d'accélérateurs Kyber, et de nombreuses fonctionnalités avancées. Comment puis-je vous aider aujourd'hui ?
                        </div>
                        <div class="message-time" id="welcomeTime"></div>
                        <div class="message-actions">
                            <button class="message-action-btn" onclick="copyMessage(this)" title="Copier">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="message-action-btn" onclick="speakMessage(this)" title="Lire">
                                <i class="fas fa-volume-up"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Options de chat -->
            <div class="chat-options">
                <button class="chat-option-btn" id="clearChatOptionBtn" title="Effacer la conversation">
                    <i class="fas fa-trash"></i>
                    <span>Effacer</span>
                </button>
                <button class="chat-option-btn" id="memoryBtn" title="Voir la mémoire associée">
                    <i class="fas fa-brain"></i>
                    <span>Mémoire</span>
                </button>
                <button class="chat-option-btn" id="speakerBtn" title="Activer/Couper le haut-parleur">
                    <i class="fas fa-volume-up"></i>
                    <span>Audio</span>
                </button>
                <button class="chat-option-btn" id="voiceBtn" title="Voix féminine Louna">
                    <i class="fas fa-female"></i>
                    <span>Voix</span>
                </button>
                <button class="chat-option-btn" id="micBtn" title="Reconnaissance vocale">
                    <i class="fas fa-microphone"></i>
                    <span>Micro</span>
                </button>
                <button class="chat-option-btn" id="cameraBtn" title="Activer/désactiver la caméra">
                    <i class="fas fa-video"></i>
                    <span>Caméra</span>
                </button>
                <button class="chat-option-btn" id="codeEditorBtn" title="Ouvrir l'éditeur de code">
                    <i class="fas fa-code"></i>
                    <span>Code</span>
                </button>
                <button class="chat-option-btn" id="generateCodeBtn" title="Raccourcis de génération de code">
                    <i class="fas fa-magic"></i>
                    <span>Générer</span>
                </button>
                <button class="chat-option-btn" id="connectivityBtn" title="Connectivité et partage">
                    <i class="fas fa-wifi"></i>
                    <span>Partage</span>
                </button>
            </div>

            <!-- Zone de saisie -->
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea
                        id="messageInput"
                        class="input-field"
                        placeholder="Tapez votre message ici... (ou demandez-moi de générer du code !)"
                        rows="1"
                    ></textarea>
                    <button id="sendButton" class="send-button">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Panneau latéral -->
        <div class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-title">QI de l'Agent</div>
                <div class="qi-display">
                    <div class="qi-value" id="qiValue">120</div>
                    <div class="qi-label">Intelligent</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">Neurones Actifs</div>
                <div class="qi-display">
                    <div class="qi-value" id="neuronsValue">71</div>
                    <div class="qi-label">Neurones</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">Mémoire Thermique</div>
                <div style="font-size: 14px; color: rgba(255, 255, 255, 0.8);">
                    <div style="margin-bottom: 8px;">
                        <strong>Zone Chaude:</strong> <span id="hotZone">48°C</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <strong>Zone Tiède:</strong> <span id="warmZone">25°C</span>
                    </div>
                    <div>
                        <strong>Zone Froide:</strong> <span id="coldZone">5°C</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="/js/chat-ultra-complet.js"></script>
    <script src="/js/auto-init-fixes.js"></script>
</body>
</html>
