<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Intelligent - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/native-app.css">
    <link rel="stylesheet" href="/css/contrast-fixes.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
        }

        /* Sidebar gauche */
        .left-sidebar {
            width: 280px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 20px;
        }

        .sidebar-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-title {
            color: #ff69b4;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .sidebar-nav-section {
            margin-bottom: 20px;
        }

        .sidebar-nav-title {
            color: #ff69b4;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .sidebar-nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            border-radius: 10px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sidebar-nav-item:hover {
            background: rgba(255, 105, 180, 0.2);
            color: #ffffff;
            transform: translateX(5px);
        }

        .sidebar-nav-item.active {
            background: rgba(255, 105, 180, 0.3);
            color: #ff69b4;
            border-left: 3px solid #ff69b4;
        }

        .sidebar-nav-item i {
            width: 16px;
            text-align: center;
        }

        /* Contenu principal */
        .main-content {
            flex: 1;
            display: flex;
            margin: 20px;
            gap: 20px;
        }

        .chat-container {
            flex: 2;
            display: flex;
            flex-direction: column;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-header {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .chat-messages {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 15px 12px 50px;
            border-radius: 18px;
            position: relative;
            animation: fadeIn 0.3s ease;
        }

        .message::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 12px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            font-size: 14px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            align-self: flex-end;
            background-color: #007bff;
            color: #ffffff !important;
            border-bottom-right-radius: 5px;
        }

        .message.user::before {
            content: '\f007';
            background-color: #0056b3;
            color: #ffffff;
        }

        .message.agent {
            align-self: flex-start;
            background-color: #28a745;
            color: #ffffff !important;
            border-bottom-left-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message.agent::before {
            content: '\f544';
            background-color: #1e7e34;
            color: #ffffff;
        }

        .message-content {
            font-size: 14px;
            line-height: 1.5;
        }

        .message-time {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.8) !important;
            margin-top: 5px;
            text-align: right;
            font-weight: 500;
        }

        .chat-input-container {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.2);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-input {
            flex-grow: 1;
            padding: 12px 15px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background-color: rgba(255, 255, 255, 0.15);
            color: #ffffff !important;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        .chat-send-btn {
            background-color: #ff69b4;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            margin-left: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .chat-send-btn:hover {
            background-color: #ff1493;
            transform: scale(1.05);
        }

        /* Panneau de droite */
        .right-panel {
            width: 300px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* État du Système */
        .system-status {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-header h3 {
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 15px 0;
            text-align: center;
        }

        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .status-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
        }

        /* Panneau de réflexions */
        .thoughts-panel {
            display: flex;
            flex-direction: column;
            background-color: rgba(255, 105, 180, 0.1);
            border-radius: 15px;
            overflow: hidden;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 105, 180, 0.3);
            max-height: 300px;
        }

        .view-reflections-btn {
            background: rgba(255, 105, 180, 0.3);
            border: 1px solid rgba(255, 105, 180, 0.5);
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .view-reflections-btn:hover {
            background: rgba(255, 105, 180, 0.5);
            transform: translateY(-1px);
        }

        /* Actions Rapides */
        .quick-actions {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .actions-header h3 {
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 15px 0;
            text-align: center;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            padding: 12px 8px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 11px;
        }

        .action-btn:hover {
            background: rgba(255, 105, 180, 0.2);
            color: #ffffff;
            transform: translateY(-2px);
        }

        .action-btn i {
            font-size: 16px;
            color: #ff69b4;
        }

        /* Activité Récente */
        .recent-activity {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .activity-header h3 {
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 15px 0;
            text-align: center;
        }

        .activity-content {
            text-align: center;
        }

        .activity-item {
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .activity-text {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
        }

        .thoughts-header {
            background-color: rgba(255, 105, 180, 0.2);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(255, 105, 180, 0.3);
            gap: 10px;
        }

        .thoughts-header h3 {
            margin: 0;
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
        }

        .thoughts-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            font-size: 13px;
            line-height: 1.4;
        }

        .thought-section {
            margin-bottom: 20px;
            padding: 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid rgba(255, 105, 180, 0.6);
        }

        .thought-title {
            color: #ff69b4;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thought-detail {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 6px;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            padding: 10px 15px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 18px;
            align-self: flex-start;
            margin-top: 5px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background-color: #ffffff;
            border-radius: 50%;
            margin: 0 2px;
            animation: typingAnimation 1.5s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingAnimation {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-5px); }
        }

        /* Bouton retour à l'accueil */
        .home-button {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .home-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        /* Responsive */
        @media (max-width: 1200px) {
            body {
                flex-direction: column;
            }

            .left-sidebar {
                width: 100%;
                height: auto;
            }

            .main-content {
                flex-direction: column;
                margin: 10px;
            }

            .thoughts-panel {
                max-height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Bouton retour à l'accueil -->
    <a href="/" class="home-button">
        <i class="fas fa-home"></i>
        <span>Accueil</span>
    </a>

    <!-- Sidebar gauche -->
    <div class="left-sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">LOUNA</div>
            <div class="sidebar-subtitle">INTELLIGENCE ÉVOLUTIVE</div>
        </div>

        <div class="sidebar-nav">
            <div class="sidebar-nav-section">
                <div class="sidebar-nav-title">PRINCIPAL</div>
                <a href="/chat" class="sidebar-nav-item active">
                    <i class="fas fa-comments"></i>
                    <span>Chat Intelligent</span>
                </a>
                <a href="/generation-studio.html" class="sidebar-nav-item">
                    <i class="fas fa-magic"></i>
                    <span>Génération</span>
                </a>
                <a href="/code-editor.html" class="sidebar-nav-item">
                    <i class="fas fa-code"></i>
                    <span>Développement</span>
                </a>
                <a href="/brain-visualization.html" class="sidebar-nav-item">
                    <i class="fas fa-brain"></i>
                    <span>Analyse</span>
                </a>
            </div>

            <div class="sidebar-nav-section">
                <div class="sidebar-nav-title">SYSTÈME</div>
                <a href="/futuristic-interface.html" class="sidebar-nav-item">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire Thermique</span>
                </a>
                <a href="/qi-neuron-monitor.html" class="sidebar-nav-item">
                    <i class="fas fa-yin-yang"></i>
                    <span>Monitoring</span>
                </a>
                <a href="/agents" class="sidebar-nav-item">
                    <i class="fas fa-robot"></i>
                    <span>Agents</span>
                </a>
                <a href="/settings" class="sidebar-nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="main-content">
        <!-- Chat -->
        <div class="chat-container">
            <div class="chat-header">
                <i class="fas fa-robot"></i>
                <div class="chat-title">Chat avec Louna</div>
            </div>

            <div class="chat-messages" id="chat-messages">
                <div class="message agent">
                    <div class="message-content">Bonjour ! Je suis Louna, votre assistant IA révolutionnaire créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe. Posez-moi une question et vous pourrez voir mes réflexions internes !</div>
                    <div class="message-time">10:00</div>
                </div>

                <div class="typing-indicator" id="typing-indicator">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chat-input" placeholder="Salut comment vas-tu ?" autocomplete="off">
                <button class="chat-send-btn" id="chat-send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>

        <!-- Panneau de droite -->
        <div class="right-panel">
            <!-- État du Système -->
            <div class="system-status">
                <div class="status-header">
                    <h3>État du Système</h3>
                </div>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value">120</div>
                        <div class="status-label">QI</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">71</div>
                        <div class="status-label">Neurones</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">42°C</div>
                        <div class="status-label">Mémoire</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">84%</div>
                        <div class="status-label">Efficacité</div>
                    </div>
                </div>
            </div>

            <!-- Réflexions Agent -->
            <div class="thoughts-panel" id="thoughts-panel">
                <div class="thoughts-header">
                    <i class="fas fa-brain"></i>
                    <h3>Réflexions Agent</h3>
                    <button class="view-reflections-btn">
                        <i class="fas fa-eye"></i>
                        Voir Réflexions
                    </button>
                </div>
                <div class="thoughts-content" id="thoughts-content">
                    <div class="thought-section">
                        <div class="thought-title">
                            <i class="fas fa-lightbulb"></i>
                            En attente d'une question...
                        </div>
                        <div class="thought-detail">
                            Posez-moi une question et vous verrez ici toutes mes réflexions internes :
                            <ul>
                                <li>🔍 Analyse du type de question</li>
                                <li>🧠 Utilisation de la mémoire thermique</li>
                                <li>🌐 Recherche Internet si nécessaire</li>
                                <li>💭 Processus de réflexion étape par étape</li>
                                <li>📝 Construction du prompt enrichi</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions Rapides -->
            <div class="quick-actions">
                <div class="actions-header">
                    <h3>Actions Rapides</h3>
                </div>
                <div class="actions-grid">
                    <button class="action-btn">
                        <i class="fas fa-robot"></i>
                        <span>Test Agent</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-sync"></i>
                        <span>Diagnostic</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-network-wired"></i>
                        <span>Net Reflexion</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-cogs"></i>
                        <span>Optimiser</span>
                    </button>
                </div>
            </div>

            <!-- Activité Récente -->
            <div class="recent-activity">
                <div class="activity-header">
                    <h3>Activité Récente</h3>
                </div>
                <div class="activity-content">
                    <div class="activity-item">
                        <div class="activity-text">Aucune activité récente</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Interface de chat avec réflexions initialisée');

            // Éléments du DOM
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.getElementById('chat-input');
            const chatSendBtn = document.getElementById('chat-send-btn');
            const typingIndicator = document.getElementById('typing-indicator');
            const thoughtsContent = document.getElementById('thoughts-content');

            // Fonction pour ajouter un message
            function addMessage(content, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user' : 'agent'}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;

                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                const now = new Date();
                timeDiv.textContent = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(timeDiv);

                chatMessages.insertBefore(messageDiv, typingIndicator);
                chatMessages.scrollTop = chatMessages.scrollHeight;

                return messageDiv;
            }

            // Fonction pour envoyer un message
            function sendMessage() {
                const message = chatInput.value.trim();
                if (!message) return;

                // Ajouter le message de l'utilisateur
                addMessage(message, true);

                // Effacer l'input
                chatInput.value = '';

                // Simuler une réponse (à remplacer par l'API réelle)
                setTimeout(() => {
                    addMessage("Merci pour votre message ! Je suis en cours de développement et bientôt je pourrai vous répondre avec mes réflexions complètes.");
                }, 1000);
            }

            // Événements
            chatSendBtn.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            console.log('Interface prête !');
        });
    </script>

    <script src="/js/native-app.js"></script>
    <script src="/js/auto-init-fixes.js"></script>
</body>
</html>
