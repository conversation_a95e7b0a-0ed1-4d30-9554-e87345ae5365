<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Intelligent - Louna</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/native-app.css">
    <link rel="stylesheet" href="/css/contrast-fixes.css">
    <link rel="stylesheet" href="/css/chat-interface.css">
    <link rel="stylesheet" href="/css/right-panel.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Styles personnalisés pour cette page -->
    <style>
        /* Styles spécifiques pour l'intégration */
        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
    </style>
</head>
<body>
    <!-- Bouton retour à l'accueil -->
    <a href="/" class="home-button">
        <i class="fas fa-home"></i>
        <span>Accueil</span>
    </a>

    <!-- Sidebar gauche -->
    <div class="left-sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">LOUNA</div>
            <div class="sidebar-subtitle">INTELLIGENCE ÉVOLUTIVE</div>
        </div>

        <div class="sidebar-nav">
            <div class="sidebar-nav-section">
                <div class="sidebar-nav-title">PRINCIPAL</div>
                <a href="/chat" class="sidebar-nav-item active">
                    <i class="fas fa-comments"></i>
                    <span>Chat Intelligent</span>
                </a>
                <a href="/generation-studio.html" class="sidebar-nav-item">
                    <i class="fas fa-magic"></i>
                    <span>Génération</span>
                </a>
                <a href="/code-editor.html" class="sidebar-nav-item">
                    <i class="fas fa-code"></i>
                    <span>Développement</span>
                </a>
                <a href="/brain-visualization.html" class="sidebar-nav-item">
                    <i class="fas fa-brain"></i>
                    <span>Analyse</span>
                </a>
            </div>

            <div class="sidebar-nav-section">
                <div class="sidebar-nav-title">SYSTÈME</div>
                <a href="/futuristic-interface.html" class="sidebar-nav-item">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire Thermique</span>
                </a>
                <a href="/qi-neuron-monitor.html" class="sidebar-nav-item">
                    <i class="fas fa-yin-yang"></i>
                    <span>Monitoring</span>
                </a>
                <a href="/agents" class="sidebar-nav-item">
                    <i class="fas fa-robot"></i>
                    <span>Agents</span>
                </a>
                <a href="/settings" class="sidebar-nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="main-content">
        <!-- Chat -->
        <div class="chat-container">
            <div class="chat-header">
                <i class="fas fa-robot"></i>
                <div class="chat-title">Chat avec Louna</div>
            </div>

            <div class="chat-messages" id="chat-messages">
                <div class="message agent">
                    <div class="message-content">Bonjour ! Je suis Louna, votre assistant IA révolutionnaire créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe. Posez-moi une question et vous pourrez voir mes réflexions internes !</div>
                    <div class="message-time">10:00</div>
                </div>

                <div class="typing-indicator" id="typing-indicator">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chat-input" placeholder="Salut comment vas-tu ?" autocomplete="off">
                <button class="chat-send-btn" id="chat-send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>

        <!-- Panneau de droite -->
        <div class="right-panel">
            <!-- État du Système -->
            <div class="system-status">
                <div class="status-header">
                    <h3>État du Système</h3>
                </div>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value">120</div>
                        <div class="status-label">QI</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">71</div>
                        <div class="status-label">Neurones</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">42°C</div>
                        <div class="status-label">Mémoire</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">84%</div>
                        <div class="status-label">Efficacité</div>
                    </div>
                </div>
            </div>

            <!-- Réflexions Agent -->
            <div class="thoughts-panel" id="thoughts-panel">
                <div class="thoughts-header">
                    <i class="fas fa-brain"></i>
                    <h3>Réflexions Agent</h3>
                    <button class="view-reflections-btn">
                        <i class="fas fa-eye"></i>
                        Voir Réflexions
                    </button>
                </div>
                <div class="thoughts-content" id="thoughts-content">
                    <div class="thought-section">
                        <div class="thought-title">
                            <i class="fas fa-lightbulb"></i>
                            En attente d'une question...
                        </div>
                        <div class="thought-detail">
                            Posez-moi une question et vous verrez ici toutes mes réflexions internes :
                            <ul>
                                <li>🔍 Analyse du type de question</li>
                                <li>🧠 Utilisation de la mémoire thermique</li>
                                <li>🌐 Recherche Internet si nécessaire</li>
                                <li>💭 Processus de réflexion étape par étape</li>
                                <li>📝 Construction du prompt enrichi</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions Rapides -->
            <div class="quick-actions">
                <div class="actions-header">
                    <h3>Actions Rapides</h3>
                </div>
                <div class="actions-grid">
                    <button class="action-btn">
                        <i class="fas fa-robot"></i>
                        <span>Test Agent</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-sync"></i>
                        <span>Diagnostic</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-network-wired"></i>
                        <span>Net Reflexion</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-cogs"></i>
                        <span>Optimiser</span>
                    </button>
                </div>
            </div>

            <!-- Activité Récente -->
            <div class="recent-activity">
                <div class="activity-header">
                    <h3>Activité Récente</h3>
                </div>
                <div class="activity-content">
                    <div class="activity-item">
                        <div class="activity-text">Aucune activité récente</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Interface de chat avec réflexions initialisée');

            // Éléments du DOM
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.getElementById('chat-input');
            const chatSendBtn = document.getElementById('chat-send-btn');
            const typingIndicator = document.getElementById('typing-indicator');
            const thoughtsContent = document.getElementById('thoughts-content');

            // Variables
            let messageHistory = [];
            let isConnected = false;
            let reflectionStream = null;

            // Initialiser la connexion aux réflexions
            initReflectionStream();

            // Vérifier le statut de l'agent au démarrage
            checkAgentStatus();

            // Fonction pour vérifier le statut de l'agent
            async function checkAgentStatus() {
                try {
                    const response = await fetch('/api/chat/status');
                    const data = await response.json();

                    if (data.success) {
                        isConnected = data.agent.isOnline;
                        updateThoughtsContent(`Agent ${isConnected ? 'en ligne' : 'hors ligne'} - ${data.agent.claudeAgent.name || 'Claude'}`, 'connected');

                        if (data.agent.capabilities) {
                            const capabilities = Object.keys(data.agent.capabilities).filter(cap => data.agent.capabilities[cap]);
                            updateThoughtsContent(`Capacités disponibles: ${capabilities.join(', ')}`, 'analyzing');
                        }
                    } else {
                        updateThoughtsContent('Erreur lors de la vérification du statut de l\'agent', 'error');
                    }
                } catch (error) {
                    console.error('Erreur statut agent:', error);
                    updateThoughtsContent('Impossible de vérifier le statut de l\'agent', 'error');
                }
            }

            // Fonction pour initialiser le stream de réflexions
            function initReflectionStream() {
                try {
                    reflectionStream = new EventSource('/api/agent/reflections/stream');

                    reflectionStream.onopen = () => {
                        console.log('🧠 Connexion aux réflexions établie');
                        updateThoughtsContent('Connexion aux réflexions établie...', 'connected');
                    };

                    reflectionStream.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            updateThoughtsContent(data.text, data.type);
                        } catch (error) {
                            console.error('Erreur parsing réflexion:', error);
                        }
                    };

                    reflectionStream.onerror = (error) => {
                        console.error('❌ Erreur stream réflexions:', error);
                        updateThoughtsContent('Erreur de connexion aux réflexions', 'error');
                    };
                } catch (error) {
                    console.error('❌ Erreur initialisation stream:', error);
                }
            }

            // Fonction pour mettre à jour les réflexions
            function updateThoughtsContent(text, type = 'thinking') {
                const iconMap = {
                    'thinking': 'fas fa-brain',
                    'analyzing': 'fas fa-search',
                    'learning': 'fas fa-graduation-cap',
                    'processing': 'fas fa-cogs',
                    'decision': 'fas fa-lightbulb',
                    'memory': 'fas fa-memory',
                    'connected': 'fas fa-wifi',
                    'error': 'fas fa-exclamation-triangle'
                };

                const icon = iconMap[type] || 'fas fa-brain';
                const timestamp = new Date().toLocaleTimeString();

                const thoughtSection = document.createElement('div');
                thoughtSection.className = 'thought-section';
                thoughtSection.innerHTML = `
                    <div class="thought-title">
                        <i class="${icon}"></i>
                        ${type.charAt(0).toUpperCase() + type.slice(1)} - ${timestamp}
                    </div>
                    <div class="thought-detail">${text}</div>
                `;

                thoughtsContent.appendChild(thoughtSection);
                thoughtsContent.scrollTop = thoughtsContent.scrollHeight;

                // Garder seulement les 10 dernières réflexions
                while (thoughtsContent.children.length > 10) {
                    thoughtsContent.removeChild(thoughtsContent.firstChild);
                }
            }

            // Fonction pour ajouter un message
            function addMessage(content, isUser = false, thoughts = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user' : 'agent'}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;

                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                const now = new Date();
                timeDiv.textContent = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(timeDiv);

                chatMessages.insertBefore(messageDiv, typingIndicator);
                chatMessages.scrollTop = chatMessages.scrollHeight;

                // Ajouter à l'historique
                messageHistory.push({
                    role: isUser ? 'user' : 'assistant',
                    content: content,
                    timestamp: new Date().toISOString()
                });

                // Garder seulement les 20 derniers messages
                if (messageHistory.length > 20) {
                    messageHistory = messageHistory.slice(-20);
                }

                return messageDiv;
            }

            // Fonction pour afficher l'indicateur de frappe
            function showTypingIndicator() {
                typingIndicator.style.display = 'flex';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Fonction pour cacher l'indicateur de frappe
            function hideTypingIndicator() {
                typingIndicator.style.display = 'none';
            }

            // Fonction pour envoyer un message à l'API
            async function sendToAgent(message) {
                try {
                    console.log('🚀 Envoi du message à l\'agent:', message);

                    // Afficher l'indicateur de frappe
                    showTypingIndicator();

                    // Ajouter une réflexion de traitement
                    updateThoughtsContent(`Traitement de votre message: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`, 'processing');

                    // Envoyer le message à l'API
                    const response = await fetch('/api/chat/message', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: message,
                            history: messageHistory.slice(-5), // Envoyer les 5 derniers messages
                            conversationId: 'chat-interface-' + Date.now()
                        })
                    });

                    const data = await response.json();
                    console.log('📦 Données reçues:', data);

                    // Cacher l'indicateur de frappe
                    hideTypingIndicator();

                    if (data.success && data.response) {
                        // Ajouter la réponse de l'agent
                        addMessage(data.response, false);

                        // Ajouter une réflexion de succès
                        updateThoughtsContent('Réponse générée avec succès', 'decision');
                        updateRecentActivity('Message traité par l\'agent');

                        // Mettre à jour les statistiques si disponibles
                        if (data.usage) {
                            updateThoughtsContent(`Tokens utilisés: ${data.usage.total_tokens || 'N/A'}`, 'memory');
                        }
                    } else {
                        // Erreur de l'API
                        addMessage(`Erreur: ${data.error || 'Erreur inconnue'}`, false);
                        updateThoughtsContent(`Erreur lors du traitement: ${data.error || 'Erreur inconnue'}`, 'error');
                        showNotification(`Erreur: ${data.error || 'Erreur inconnue'}`, 'error');
                        updateRecentActivity('Erreur lors du traitement');
                    }
                } catch (error) {
                    console.error('💥 Erreur:', error);
                    hideTypingIndicator();
                    addMessage(`Erreur de connexion: ${error.message}`, false);
                    updateThoughtsContent(`Erreur de connexion: ${error.message}`, 'error');
                    showNotification('Erreur de connexion avec l\'agent', 'error');
                    updateRecentActivity('Erreur de connexion');
                }
            }

            // Fonction pour envoyer un message
            function sendMessage() {
                const message = chatInput.value.trim();
                if (!message) return;

                // Ajouter le message de l'utilisateur
                addMessage(message, true);

                // Effacer l'input
                chatInput.value = '';

                // Envoyer à l'agent
                sendToAgent(message);
            }

            // Fonction pour mettre à jour les statistiques système
            async function updateSystemStats() {
                try {
                    // Récupérer les métriques du chat
                    const response = await fetch('/api/chat/metrics');
                    const data = await response.json();

                    if (data) {
                        // Mettre à jour les statistiques affichées
                        const statusValues = document.querySelectorAll('.status-value');
                        const statusLabels = document.querySelectorAll('.status-label');

                        statusValues.forEach((element, index) => {
                            const label = statusLabels[index]?.textContent.toLowerCase();

                            switch (label) {
                                case 'qi':
                                    // Calculer un QI basé sur les performances
                                    const qi = Math.min(150, 100 + (data.responses?.successRate || 0.95) * 50);
                                    element.textContent = Math.round(qi);
                                    break;
                                case 'neurones':
                                    // Simuler des neurones actifs
                                    element.textContent = Math.floor(50 + Math.random() * 50);
                                    break;
                                case 'mémoire':
                                    // Température de mémoire simulée
                                    element.textContent = Math.floor(35 + Math.random() * 15) + '°C';
                                    break;
                                case 'efficacité':
                                    // Efficacité basée sur les métriques
                                    const efficiency = (data.responses?.successRate || 0.95) * 100;
                                    element.textContent = Math.round(efficiency) + '%';
                                    break;
                            }
                        });

                        updateThoughtsContent('Statistiques système mises à jour', 'memory');
                    }
                } catch (error) {
                    console.error('Erreur mise à jour statistiques:', error);
                    // Fallback: mise à jour simulée
                    const qiElement = document.querySelector('.status-value');
                    if (qiElement) {
                        const currentQI = parseInt(qiElement.textContent);
                        const newQI = Math.max(100, Math.min(150, currentQI + Math.floor(Math.random() * 3) - 1));
                        qiElement.textContent = newQI;
                    }
                }
            }

            // Événements
            chatSendBtn.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Bouton "Voir Réflexions"
            const viewReflectionsBtn = document.querySelector('.view-reflections-btn');
            if (viewReflectionsBtn) {
                viewReflectionsBtn.addEventListener('click', () => {
                    thoughtsContent.scrollTop = thoughtsContent.scrollHeight;
                    updateThoughtsContent('Affichage des réflexions demandé par l\'utilisateur', 'decision');
                });
            }

            // Boutons d'actions rapides
            const actionBtns = document.querySelectorAll('.action-btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const action = btn.querySelector('span').textContent;
                    updateThoughtsContent(`Action rapide exécutée: ${action}`, 'processing');
                    showNotification(`Exécution de l'action: ${action}`, 'info');
                    updateRecentActivity(`Action: ${action}`);

                    // Simuler l'exécution de l'action
                    setTimeout(() => {
                        updateThoughtsContent(`Action "${action}" terminée avec succès`, 'decision');
                        showNotification(`Action "${action}" terminée`, 'success');
                        updateRecentActivity(`${action} terminé avec succès`);
                    }, 1000);
                });
            });

            // Mettre à jour les statistiques périodiquement
            setInterval(updateSystemStats, 30000); // Toutes les 30 secondes

            // Fonction pour afficher des notifications
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                `;

                // Styles pour la notification
                Object.assign(notification.style, {
                    position: 'fixed',
                    top: '80px',
                    right: '20px',
                    background: type === 'success' ? 'rgba(40, 167, 69, 0.9)' :
                               type === 'error' ? 'rgba(220, 53, 69, 0.9)' :
                               'rgba(23, 162, 184, 0.9)',
                    color: 'white',
                    padding: '12px 20px',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px',
                    zIndex: '1001',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.3)',
                    animation: 'slideInRight 0.3s ease-out'
                });

                document.body.appendChild(notification);

                // Supprimer après 3 secondes
                setTimeout(() => {
                    notification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }

            // Ajouter les animations CSS pour les notifications
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }

                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);

            // Fonction pour mettre à jour l'activité récente
            function updateRecentActivity(activity) {
                const activityContent = document.querySelector('.activity-content');
                if (activityContent) {
                    const activityItem = document.createElement('div');
                    activityItem.className = 'activity-item';
                    activityItem.innerHTML = `
                        <div class="activity-text">${activity}</div>
                        <div style="font-size: 10px; color: rgba(255, 255, 255, 0.5); margin-top: 5px;">
                            ${new Date().toLocaleTimeString()}
                        </div>
                    `;

                    // Ajouter en premier
                    activityContent.insertBefore(activityItem, activityContent.firstChild);

                    // Garder seulement les 5 dernières activités
                    while (activityContent.children.length > 5) {
                        activityContent.removeChild(activityContent.lastChild);
                    }
                }
            }

            // Mettre à jour les statistiques périodiquement
            setInterval(updateSystemStats, 30000); // Toutes les 30 secondes

            // Ajouter une réflexion initiale
            updateThoughtsContent('Interface de chat initialisée. Prêt à recevoir vos messages !', 'connected');
            updateRecentActivity('Interface de chat démarrée');
            showNotification('Interface de chat connectée avec succès !', 'success');

            console.log('✅ Interface prête !');
        });
    </script>

    <script src="/js/native-app.js"></script>
    <script src="/js/auto-init-fixes.js"></script>
</body>
</html>
