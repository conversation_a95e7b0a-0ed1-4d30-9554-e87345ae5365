<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport d'Unification - Louna</title>
    <link rel="stylesheet" href="/css/louna-unified-theme.css?v=2025">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="louna-unified theme-2025">
    <div class="louna-container">
        <div class="page-header fade-in-up">
            <h1 class="page-title">
                <i class="fas fa-clipboard-check"></i>
                Rapport d'Unification des Interfaces Louna
            </h1>
            <p class="page-subtitle">
                État complet de la standardisation et de la cohérence des interfaces
            </p>
        </div>

        <div class="louna-grid grid-2 mb-lg">
            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-chart-line"></i>
                    <h3 class="card-title">Résumé de l'Unification</h3>
                </div>
                <div id="summary-stats">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                        <span>Interfaces testées :</span>
                        <span id="total-interfaces">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                        <span>Navigation unifiée :</span>
                        <span id="unified-nav">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                        <span>Thème appliqué :</span>
                        <span id="theme-applied">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                        <span>Taux de réussite :</span>
                        <span id="success-rate">0%</span>
                    </div>
                </div>
                <button class="louna-btn btn-primary" onclick="runFullTest()">
                    <i class="fas fa-play"></i>
                    Lancer Test Complet
                </button>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-tasks"></i>
                    <h3 class="card-title">Problèmes Résolus</h3>
                </div>
                <div class="mb-md">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #4caf50; margin-right: 10px;"></i>
                        <span>Navigation incohérente entre les pages</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #4caf50; margin-right: 10px;"></i>
                        <span>Designs différents selon les interfaces</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #4caf50; margin-right: 10px;"></i>
                        <span>Absence de bouton retour à l'accueil</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #4caf50; margin-right: 10px;"></i>
                        <span>Couleurs et styles non harmonisés</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #4caf50; margin-right: 10px;"></i>
                        <span>Navigation difficile entre interfaces</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="louna-card mb-lg fade-in-up">
            <div class="card-header">
                <i class="card-icon fas fa-list"></i>
                <h3 class="card-title">Test des Interfaces Principales</h3>
            </div>
            <div id="interface-tests">
                <p>Cliquez sur "Lancer Test Complet" pour vérifier toutes les interfaces...</p>
            </div>
        </div>

        <div class="louna-grid grid-3 mb-lg">
            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-palette"></i>
                    <h3 class="card-title">Thème Unifié</h3>
                </div>
                <p class="mb-md">Système de design cohérent :</p>
                <ul style="margin-left: 20px; font-size: 0.9rem;">
                    <li>Variables CSS centralisées</li>
                    <li>Couleurs harmonisées</li>
                    <li>Typographie unifiée</li>
                    <li>Composants réutilisables</li>
                </ul>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-compass"></i>
                    <h3 class="card-title">Navigation</h3>
                </div>
                <p class="mb-md">Système de navigation unifié :</p>
                <ul style="margin-left: 20px; font-size: 0.9rem;">
                    <li>Barre de navigation fixe</li>
                    <li>Boutons de navigation rapide</li>
                    <li>Retour accueil depuis toute page</li>
                    <li>Navigation responsive</li>
                </ul>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-magic"></i>
                    <h3 class="card-title">Auto-Unification</h3>
                </div>
                <p class="mb-md">Application automatique :</p>
                <ul style="margin-left: 20px; font-size: 0.9rem;">
                    <li>Script d'injection automatique</li>
                    <li>Middleware serveur</li>
                    <li>Transformation des éléments</li>
                    <li>Cohérence garantie</li>
                </ul>
            </div>
        </div>

        <div class="louna-card fade-in-up">
            <div class="card-header">
                <i class="card-icon fas fa-link"></i>
                <h3 class="card-title">Accès Rapide aux Interfaces</h3>
            </div>
            <div class="louna-grid grid-4">
                <a href="/" class="louna-btn btn-primary">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/unified-hub.html" class="louna-btn btn-primary">
                    <i class="fas fa-th-large"></i>
                    Hub Central
                </a>
                <a href="/futuristic-interface.html" class="louna-btn btn-primary">
                    <i class="fas fa-fire"></i>
                    Mémoire Thermique
                </a>
                <a href="/brain-visualization.html" class="louna-btn btn-primary">
                    <i class="fas fa-brain"></i>
                    Cerveau 3D
                </a>
                <a href="/qi-neuron-monitor.html" class="louna-btn btn-primary">
                    <i class="fas fa-yin-yang"></i>
                    QI Monitor
                </a>
                <a href="/generation-studio.html" class="louna-btn btn-primary">
                    <i class="fas fa-magic"></i>
                    Studio
                </a>
                <a href="/chat-simple" class="louna-btn btn-primary">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
                <a href="/navigation" class="louna-btn btn-secondary">
                    <i class="fas fa-sitemap"></i>
                    Navigation Complète
                </a>
            </div>
        </div>
    </div>

    <script src="/js/auto-unify-interfaces.js?v=2025"></script>
    <script>
        const interfaces = [
            { name: 'Accueil', url: '/', key: 'home' },
            { name: 'Hub Central', url: '/unified-hub.html', key: 'hub' },
            { name: 'Mémoire Thermique', url: '/futuristic-interface.html', key: 'memory' },
            { name: 'Cerveau 3D', url: '/brain-visualization.html', key: 'brain' },
            { name: 'QI Monitor', url: '/qi-neuron-monitor.html', key: 'qi' },
            { name: 'Studio Génération', url: '/generation-studio.html', key: 'studio' },
            { name: 'Chat Simple', url: '/chat-simple', key: 'chat' },
            { name: 'Agents', url: '/agents.html', key: 'agents' },
            { name: 'Formation', url: '/training.html', key: 'training' },
            { name: 'Sécurité', url: '/security-dashboard.html', key: 'security' },
            { name: 'Performance', url: '/performance.html', key: 'performance' },
            { name: 'Caméra', url: '/camera-vision-interface.html', key: 'camera' }
        ];

        let testResults = [];

        function testInterface(interfaceData) {
            return new Promise((resolve) => {
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = interfaceData.url;
                
                iframe.onload = () => {
                    try {
                        const doc = iframe.contentDocument || iframe.contentWindow.document;
                        const hasUnifiedNav = doc.querySelector('.louna-nav') !== null;
                        const hasUnifiedTheme = doc.querySelector('link[href*="louna-unified-theme"]') !== null;
                        const hasUnifiedClass = doc.body.classList.contains('louna-unified');
                        
                        resolve({
                            name: interfaceData.name,
                            url: interfaceData.url,
                            hasNav: hasUnifiedNav,
                            hasTheme: hasUnifiedTheme,
                            hasClass: hasUnifiedClass,
                            success: hasUnifiedNav || hasUnifiedTheme
                        });
                    } catch (error) {
                        resolve({
                            name: interfaceData.name,
                            url: interfaceData.url,
                            hasNav: false,
                            hasTheme: false,
                            hasClass: false,
                            success: false,
                            error: 'Erreur d\'accès'
                        });
                    }
                    
                    document.body.removeChild(iframe);
                };
                
                iframe.onerror = () => {
                    resolve({
                        name: interfaceData.name,
                        url: interfaceData.url,
                        hasNav: false,
                        hasTheme: false,
                        hasClass: false,
                        success: false,
                        error: 'Erreur de chargement'
                    });
                    document.body.removeChild(iframe);
                };
                
                document.body.appendChild(iframe);
                
                // Timeout de sécurité
                setTimeout(() => {
                    if (document.body.contains(iframe)) {
                        document.body.removeChild(iframe);
                        resolve({
                            name: interfaceData.name,
                            url: interfaceData.url,
                            hasNav: false,
                            hasTheme: false,
                            hasClass: false,
                            success: false,
                            error: 'Timeout'
                        });
                    }
                }, 5000);
            });
        }

        async function runFullTest() {
            const button = document.querySelector('button[onclick="runFullTest()"]');
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test en cours...';
            button.disabled = true;
            
            const testsDiv = document.getElementById('interface-tests');
            testsDiv.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> Test des interfaces en cours...</p>';
            
            testResults = [];
            
            for (let i = 0; i < interfaces.length; i++) {
                const result = await testInterface(interfaces[i]);
                testResults.push(result);
                
                // Mise à jour en temps réel
                updateTestDisplay();
                updateSummary();
            }
            
            button.innerHTML = '<i class="fas fa-check"></i> Test Terminé';
            button.disabled = false;
            
            showToast('success', `Test terminé ! ${testResults.filter(r => r.success).length}/${testResults.length} interfaces unifiées`);
        }

        function updateTestDisplay() {
            const testsDiv = document.getElementById('interface-tests');
            testsDiv.innerHTML = testResults.map(result => {
                const statusIcon = result.success ? 
                    '<i class="fas fa-check-circle" style="color: #4caf50;"></i>' : 
                    '<i class="fas fa-times-circle" style="color: #f44336;"></i>';
                
                const details = result.error ? 
                    `<span style="color: #f44336;">${result.error}</span>` :
                    `Nav: ${result.hasNav ? '✅' : '❌'} | Thème: ${result.hasTheme ? '✅' : '❌'} | Classe: ${result.hasClass ? '✅' : '❌'}`;
                
                return `
                    <div style="display: flex; align-items: center; justify-content: space-between; padding: 10px; border-bottom: 1px solid var(--border-color);">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            ${statusIcon}
                            <strong>${result.name}</strong>
                        </div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">
                            ${details}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function updateSummary() {
            const total = testResults.length;
            const unified = testResults.filter(r => r.success).length;
            const navCount = testResults.filter(r => r.hasNav).length;
            const themeCount = testResults.filter(r => r.hasTheme).length;
            const successRate = total > 0 ? Math.round((unified / total) * 100) : 0;
            
            document.getElementById('total-interfaces').textContent = total;
            document.getElementById('unified-nav').textContent = navCount;
            document.getElementById('theme-applied').textContent = themeCount;
            document.getElementById('success-rate').textContent = successRate + '%';
        }

        function showToast(type, message) {
            const toast = document.createElement('div');
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : 'info'}"></i>
                <span>${message}</span>
            `;
            
            Object.assign(toast.style, {
                position: 'fixed',
                top: '100px',
                right: '20px',
                background: type === 'success' ? '#4caf50' : '#2196f3',
                color: 'white',
                padding: '12px 20px',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                zIndex: '9999',
                opacity: '0',
                transform: 'translateX(100%)',
                transition: 'all 0.3s ease'
            });
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 4000);
        }

        // Auto-test au chargement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                showToast('success', 'Rapport d\'unification chargé !');
            }, 1000);
        });
    </script>
</body>
</html>
