<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration Design Unifié - Louna</title>
    <link rel="stylesheet" href="/css/louna-unified-theme.css?v=2025">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="louna-unified theme-2025">
    <!-- Navigation sera injectée automatiquement -->
    
    <div class="louna-container">
        <div class="page-header fade-in-up">
            <h1 class="page-title">
                <i class="fas fa-palette"></i>
                Démonstration du Design Unifié
            </h1>
            <p class="page-subtitle">
                Toutes les interfaces Louna suivent maintenant le même design cohérent et moderne
            </p>
        </div>

        <div class="louna-grid grid-2 mb-lg">
            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-check-circle"></i>
                    <h3 class="card-title">Navigation Unifiée</h3>
                </div>
                <p class="mb-md">Toutes les pages ont maintenant la même navigation en haut avec :</p>
                <ul style="margin-left: 20px; margin-bottom: 15px;">
                    <li>Logo Louna avec icône cerveau</li>
                    <li>Liens vers les principales interfaces</li>
                    <li>Bouton de retour à l'accueil</li>
                    <li>Navigation rapide flottante</li>
                </ul>
                <button class="louna-btn btn-primary" onclick="testNavigation()">
                    <i class="fas fa-compass"></i>
                    Tester Navigation
                </button>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-paint-brush"></i>
                    <h3 class="card-title">Thème Cohérent</h3>
                </div>
                <p class="mb-md">Design uniforme avec :</p>
                <ul style="margin-left: 20px; margin-bottom: 15px;">
                    <li>Couleurs rose et noir cohérentes</li>
                    <li>Cartes avec effet glassmorphism</li>
                    <li>Animations fluides</li>
                    <li>Typographie harmonisée</li>
                </ul>
                <button class="louna-btn btn-secondary" onclick="showColorPalette()">
                    <i class="fas fa-palette"></i>
                    Voir Palette
                </button>
            </div>
        </div>

        <div class="louna-grid grid-3 mb-lg">
            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-home"></i>
                    <h3 class="card-title">Accueil</h3>
                </div>
                <p>Page d'accueil Louna Vision Ultra avec présentation complète</p>
                <a href="/" class="louna-btn btn-primary">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-th-large"></i>
                    <h3 class="card-title">Hub Central</h3>
                </div>
                <p>Interface principale de contrôle et navigation intelligente</p>
                <a href="/unified-hub.html" class="louna-btn btn-primary">
                    <i class="fas fa-th-large"></i>
                    Hub Central
                </a>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-fire"></i>
                    <h3 class="card-title">Mémoire Thermique</h3>
                </div>
                <p>Interface de gestion de la mémoire thermique avancée</p>
                <a href="/futuristic-interface.html" class="louna-btn btn-primary">
                    <i class="fas fa-fire"></i>
                    Mémoire
                </a>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-brain"></i>
                    <h3 class="card-title">Cerveau 3D</h3>
                </div>
                <p>Visualisation 3D du cerveau artificiel en temps réel</p>
                <a href="/brain-visualization.html" class="louna-btn btn-primary">
                    <i class="fas fa-brain"></i>
                    Cerveau 3D
                </a>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-yin-yang"></i>
                    <h3 class="card-title">QI Monitor</h3>
                </div>
                <p>Monitoring en temps réel du QI et des neurones</p>
                <a href="/qi-neuron-monitor.html" class="louna-btn btn-primary">
                    <i class="fas fa-yin-yang"></i>
                    QI Monitor
                </a>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-magic"></i>
                    <h3 class="card-title">Studio Génération</h3>
                </div>
                <p>Studio de génération multimédia illimitée</p>
                <a href="/generation-studio.html" class="louna-btn btn-primary">
                    <i class="fas fa-magic"></i>
                    Studio
                </a>
            </div>
        </div>

        <div class="louna-card mb-lg fade-in-up">
            <div class="card-header">
                <i class="card-icon fas fa-cogs"></i>
                <h3 class="card-title">Fonctionnalités du Design Unifié</h3>
            </div>
            
            <div class="louna-grid grid-2">
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 10px;">
                        <i class="fas fa-check"></i> Navigation
                    </h4>
                    <ul style="margin-left: 20px;">
                        <li>Navigation fixe en haut de page</li>
                        <li>Boutons de navigation rapide</li>
                        <li>Retour à l'accueil depuis toute page</li>
                        <li>Navigation responsive</li>
                    </ul>
                </div>
                
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 10px;">
                        <i class="fas fa-check"></i> Design
                    </h4>
                    <ul style="margin-left: 20px;">
                        <li>Thème sombre avec accents roses</li>
                        <li>Cartes avec effet de verre</li>
                        <li>Animations fluides</li>
                        <li>Icônes cohérentes</li>
                    </ul>
                </div>
                
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 10px;">
                        <i class="fas fa-check"></i> Interactions
                    </h4>
                    <ul style="margin-left: 20px;">
                        <li>Boutons avec effets hover</li>
                        <li>Transitions fluides</li>
                        <li>Feedback visuel</li>
                        <li>Notifications toast</li>
                    </ul>
                </div>
                
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 10px;">
                        <i class="fas fa-check"></i> Responsive
                    </h4>
                    <ul style="margin-left: 20px;">
                        <li>Adaptation mobile/tablette</li>
                        <li>Grilles flexibles</li>
                        <li>Navigation mobile optimisée</li>
                        <li>Texte lisible sur tous écrans</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="louna-card fade-in-up">
            <div class="card-header">
                <i class="card-icon fas fa-rocket"></i>
                <h3 class="card-title">Test des Fonctionnalités</h3>
            </div>
            
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                <button class="louna-btn btn-primary" onclick="showToast('success', 'Test réussi !')">
                    <i class="fas fa-check"></i>
                    Toast Succès
                </button>
                
                <button class="louna-btn btn-warning" onclick="showToast('warning', 'Attention !')">
                    <i class="fas fa-exclamation"></i>
                    Toast Warning
                </button>
                
                <button class="louna-btn btn-danger" onclick="showToast('error', 'Erreur de test')">
                    <i class="fas fa-times"></i>
                    Toast Erreur
                </button>
                
                <button class="louna-btn btn-secondary" onclick="testAnimations()">
                    <i class="fas fa-play"></i>
                    Test Animations
                </button>
            </div>
            
            <div id="color-palette" style="display: none; margin-top: 20px;">
                <h4 style="color: var(--primary-color); margin-bottom: 15px;">Palette de Couleurs Unifiée</h4>
                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <div style="background: var(--primary-color); width: 60px; height: 60px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; text-align: center;">Primary</div>
                    <div style="background: var(--secondary-color); width: 60px; height: 60px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; text-align: center;">Secondary</div>
                    <div style="background: var(--accent-color); width: 60px; height: 60px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; text-align: center;">Accent</div>
                    <div style="background: var(--bg-card); width: 60px; height: 60px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; text-align: center; border: 1px solid var(--border-color);">Card BG</div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/auto-unify-interfaces.js?v=2025"></script>
    <script>
        function showToast(type, message) {
            const toast = document.createElement('div');
            toast.className = `louna-toast toast-${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'exclamation'}"></i>
                <span>${message}</span>
            `;
            
            Object.assign(toast.style, {
                position: 'fixed',
                top: '100px',
                right: '20px',
                background: type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196f3',
                color: 'white',
                padding: '12px 20px',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                zIndex: '9999',
                opacity: '0',
                transform: 'translateX(100%)',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 15px rgba(0,0,0,0.3)'
            });
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        function showColorPalette() {
            const palette = document.getElementById('color-palette');
            palette.style.display = palette.style.display === 'none' ? 'block' : 'none';
        }

        function testNavigation() {
            showToast('success', 'Navigation unifiée active sur toutes les pages !');
        }

        function testAnimations() {
            const cards = document.querySelectorAll('.louna-card');
            cards.forEach((card, index) => {
                card.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    card.style.transition = 'transform 0.3s ease';
                    card.style.transform = 'scale(1)';
                }, index * 100);
            });
            showToast('success', 'Animations testées avec succès !');
        }

        // Auto-test au chargement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                showToast('success', 'Design unifié Louna chargé !');
            }, 1000);
        });
    </script>
</body>
</html>
