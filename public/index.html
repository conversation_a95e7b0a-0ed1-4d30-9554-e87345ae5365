<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Agent IA Révolutionnaire</title>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/louna-thermal.css">
    <link rel="stylesheet" href="/css/theme-switcher.css">
    <link rel="stylesheet" href="/css/notifications.css">
    <link rel="stylesheet" href="/css/contrast-fixes.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <style>
        .presentation-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .presentation-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 4px 8px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.2); }
            to { text-shadow: 0 4px 8px rgba(0,0,0,0.3), 0 0 30px rgba(255,255,255,0.4); }
        }

        .hero-subtitle {
            font-size: 1.8rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 60px 0;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            padding: 0 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #ff6b6b;
            margin-bottom: 10px;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .capabilities-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 60px;
            color: #333;
            font-weight: 700;
        }

        .capabilities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .capability-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .capability-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .capability-icon {
            font-size: 3.5rem;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .capability-title {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #333;
            font-weight: 600;
        }

        .capability-description {
            color: #666;
            line-height: 1.7;
            margin-bottom: 25px;
        }

        .capability-features {
            list-style: none;
            padding: 0;
        }

        .capability-features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }

        .capability-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }

        .demo-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .demo-buttons {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-top: 40px;
        }

        .demo-button {
            padding: 20px 40px;
            font-size: 1.2rem;
            border-radius: 50px;
            border: 2px solid white;
            background: transparent;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 15px;
            font-weight: 600;
        }

        .demo-button:hover {
            background: white;
            color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .tech-specs {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .spec-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .spec-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #333;
            font-weight: 600;
        }

        .spec-list {
            list-style: none;
            padding: 0;
        }

        .spec-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }

        .spec-list li:last-child {
            border-bottom: none;
        }

        .spec-label {
            font-weight: 500;
            color: #555;
        }

        .spec-value {
            color: #667eea;
            font-weight: 600;
        }

        /* Corrections de lisibilité pour tous les textes */
        .hero-title, .hero-subtitle, .hero-description {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .stat-label {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
            font-weight: 600 !important;
        }

        .stat-number {
            color: #ff6b6b !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: 700 !important;
        }

        .section-title {
            color: #333 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
            font-weight: 700 !important;
        }

        .capability-title {
            color: #333 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;
            font-weight: 600 !important;
        }

        .capability-description {
            color: #555 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
            font-weight: 500 !important;
        }

        .capability-features li {
            color: #444 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
            font-weight: 500 !important;
        }

        .spec-title {
            color: #333 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;
            font-weight: 600 !important;
        }

        .spec-label {
            color: #555 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
            font-weight: 500 !important;
        }

        .spec-value {
            color: #667eea !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
            font-weight: 600 !important;
        }

        .demo-button {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
            font-weight: 600 !important;
        }

        .demo-button:hover {
            color: #667eea !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
        }

        /* Corrections pour les sections avec background sombre */
        .demo-section .section-title {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .demo-section .hero-description {
            color: rgba(255, 255, 255, 0.95) !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* Corrections pour les cartes sur fond sombre */
        .capabilities-section[style*="background: linear-gradient"] .capability-title {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .capabilities-section[style*="background: linear-gradient"] .capability-description {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .capabilities-section[style*="background: linear-gradient"] .capability-features li {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* Navigation unifiée */
        .unified-header h1 {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .unified-header .subtitle {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .unified-button {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
            font-weight: 600 !important;
        }

        .unified-button:hover {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }

        /* Footer */
        .footer-content {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .footer-logo span {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .footer-text {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* Navigation top */
        .nav-item {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .nav-item span {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        .logo-text {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* Bouton accueil fixe */
        #homeButton a {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        #homeButton a span {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* Corrections pour les icônes */
        .capability-icon {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
        }

        /* Corrections pour les cartes unifiées */
        .unified-card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        /* Corrections pour les cartes sur fond sombre */
        .capabilities-section[style*="background: linear-gradient"] .unified-card {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        /* Corrections pour les statistiques */
        .stats-grid .unified-card {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        /* Correction globale pour tous les textes blancs sur blanc */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Amélioration du contraste pour tous les éléments */
        p, span, div, li, a, h1, h2, h3, h4, h5, h6 {
            text-rendering: optimizeLegibility;
        }

        /* Styles pour la navigation */
        .nav-item:hover {
            background: rgba(255,255,255,0.3) !important;
            transform: translateY(-2px);
        }

        .nav-item.active {
            background: rgba(255,255,255,0.2) !important;
        }
    </style>
    <link rel="stylesheet" href="css/native-app.css">
    <link rel="stylesheet" href="/css/unified-interface.css">

    <link rel="stylesheet" href="/css/contrast-fixes.css">
    <style>
        /* CORRECTION UNIVERSELLE DE LISIBILITÉ - LOUNA */

        /* Optimisation du rendu des polices */
        * {
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Variables corrigées pour lisibilité maximale */
        :root {
            --text-primary: #ffffff !important;
            --text-secondary: #ffffff !important;
            --text-muted: rgba(255, 255, 255, 0.9) !important;
            --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        }

        /* CORRECTION GLOBALE - TOUS LES TEXTES */
        h1, h2, h3, h4, h5, h6, p, span, div, li, a, label, input, textarea, select {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        /* BOUTONS - LISIBILITÉ MAXIMALE */
        button, .btn, .button, .toolbar-btn, .demo-button, .cta-button {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        button:hover, .btn:hover, .button:hover {
            color: #ffffff !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }

        /* NAVIGATION - TOUJOURS VISIBLE */
        .nav-item, .nav-link, .navbar-nav a, .top-navbar a {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        .logo-text, .navbar-brand {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: bold !important;
        }

        /* CARTES ET CONTENEURS - CONTRASTE OPTIMAL */
        .card, .unified-card, .metric-card, .capability-card {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        /* TITRES ET SOUS-TITRES */
        .interface-title, .section-title, .card-title {
            color: #ffffff !important;
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        .interface-subtitle, .card-subtitle {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* STATISTIQUES ET MÉTRIQUES */
        .stat-number, .metric-value {
            color: #ff6b6b !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
            font-weight: bold !important;
        }

        .stat-label, .metric-label {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
            font-weight: 600 !important;
        }

        /* LISTES ET DESCRIPTIONS */
        .capability-features li, .spec-list li {
            color: rgba(255, 255, 255, 0.95) !important;
            text-shadow: var(--text-shadow) !important;
        }

        .capability-description, .card-text {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* FORMULAIRES */
        input, select, textarea {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            color: #ffffff !important;
        }

        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* TABLEAUX */
        table, th, td {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        th {
            background: rgba(255, 105, 180, 0.2) !important;
            font-weight: bold !important;
        }

        /* ALERTES ET NOTIFICATIONS */
        .alert, .notification {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* FOOTER */
        .footer, .footer-content {
            color: #ffffff !important;
            text-shadow: var(--text-shadow) !important;
        }

        /* CORRECTIONS SPÉCIFIQUES POUR FONDS CLAIRS */
        .tech-specs, .capabilities-section[style*="background: #f"] {
            color: #333 !important;
        }

        .tech-specs *, .capabilities-section[style*="background: #f"] * {
            color: #333 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
        }

        /* ICÔNES */
        .fas, .far, .fab {
            text-shadow: var(--text-shadow) !important;
        }

        /* CORRECTION POUR LES ÉLÉMENTS SPÉCIAUX */
        .progress-bar, .slider {
            background: linear-gradient(135deg, #ff69b4, #ff1493) !important;
        }

        /* AMÉLIORATION DU CONTRASTE POUR LES LIENS */
        a {
            color: #ff69b4 !important;
            text-shadow: var(--text-shadow) !important;
        }

        a:hover {
            color: #ff1493 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
        }
    </style>
</head>
<body>
    <!-- Navigation principale complète -->
    <nav class="top-navbar" style="position: fixed; top: 0; left: 0; right: 0; z-index: 1000; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 10px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.3);">
        <div style="max-width: 1400px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between; padding: 0 20px;">
            <div class="logo-container" style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-brain" style="font-size: 1.5rem; color: white;"></i>
                <span class="logo-text" style="font-size: 1.5rem; font-weight: bold; color: white;">Louna</span>
            </div>

            <div class="nav-links" style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="/" class="nav-item active" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.2); display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="/chat" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-comments"></i>
                    <span>Chat</span>
                </a>
                <a href="/futuristic-interface.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
                <a href="/brain-visualization.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-brain"></i>
                    <span>Cerveau 3D</span>
                </a>
                <a href="/qi-neuron-monitor.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-yin-yang"></i>
                    <span>QI Monitor</span>
                </a>
                <a href="/kyber-dashboard.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-bolt"></i>
                    <span>Kyber</span>
                </a>
                <a href="/generation-studio.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-magic"></i>
                    <span>Studio</span>
                </a>
                <a href="/code-editor.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-code"></i>
                    <span>Code</span>
                </a>
                <a href="/agents.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-users"></i>
                    <span>Agents</span>
                </a>
                <a href="/training.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Formation</span>
                </a>
                <a href="/security-dashboard.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-shield-alt"></i>
                    <span>Sécurité</span>
                </a>
                <a href="/performance.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-chart-line"></i>
                    <span>Performance</span>
                </a>
                <a href="/ltx-video.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-video"></i>
                    <span>LTX Video</span>
                </a>
                <a href="/camera-vision-interface.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-camera"></i>
                    <span>Vision</span>
                </a>
                <a href="/dreams-new.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-moon"></i>
                    <span>Rêves</span>
                </a>
                <a href="/settings-new.html" class="nav-item" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Espacement pour la navigation fixe -->
    <div style="height: 80px;"></div>


    <!-- En-tête unifié -->
    <div class="unified-container">
        <div class="unified-header">
            <h1><i class="fas fa-presentation"></i> Présentation Louna</h1>
            <p class="subtitle">Découvrez toutes les capacités de l'agent intelligent</p>
        </div>

        <!-- Navigation unifiée -->
        <div class="unified-nav">
            <div class="nav-buttons">
                <a href="/chat" class="unified-button nav-button">
                    <i class="fas fa-comments"></i> Chat Intelligent
                </a>
                <a href="/futuristic-interface.html" class="unified-button nav-button">
                    <i class="fas fa-fire"></i> Mémoire Thermique
                </a>
                <a href="/brain-visualization.html" class="unified-button nav-button">
                    <i class="fas fa-brain"></i> Visualisation 3D
                </a>
                <a href="/qi-neuron-monitor.html" class="unified-button nav-button">
                    <i class="fas fa-yin-yang"></i> Monitoring QI
                </a>
                <a href="/generation-studio.html" class="unified-button nav-button">
                    <i class="fas fa-magic"></i> Studio Génération
                </a>
                <a href="/kyber-dashboard.html" class="unified-button nav-button">
                    <i class="fas fa-bolt"></i> Accélérateurs Kyber
                </a>
            </div>
        </div>



    <!-- Section Hero -->
    <section class="presentation-hero">
        <div class="hero-content">
            <h1 class="hero-title">LOUNA</h1>
            <p class="hero-subtitle">Agent IA Révolutionnaire de Nouvelle Génération</p>
            <p class="hero-description">
                Découvrez l'agent intelligent le plus avancé au monde avec un véritable cerveau artificiel évolutif.
                QI de 120 à 200, 71+ neurones, accès Internet complet, voix féminine naturelle et génération multimédia illimitée.
                Votre agent grandit et apprend comme un cerveau humain !
            </p>

            <!-- Statistiques en temps réel -->
            <div class="stats-grid">
                <div class="unified-card">
                    <span class="stat-number" id="total-generations">0</span>
                    <span class="stat-label">Générations Totales</span>
                </div>
                <div class="unified-card">
                    <span class="stat-number" id="video-count">0</span>
                    <span class="stat-label">Vidéos 4K</span>
                </div>
                <div class="unified-card">
                    <span class="stat-number" id="image-count">0</span>
                    <span class="stat-label">Images HD</span>
                </div>
                <div class="unified-card">
                    <span class="stat-number" id="music-count">0</span>
                    <span class="stat-label">Compositions Musicales</span>
                </div>
                <div class="unified-card">
                    <span class="stat-number" id="model3d-count">0</span>
                    <span class="stat-label">Modèles 3D</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Capacités -->
    <section class="capabilities-section">
        <div class="container">
            <h2 class="section-title">Capacités Révolutionnaires</h2>

            <div class="capabilities-grid">
                <!-- Génération Multimédia -->
                <div class="unified-card">
                    <i class="fas fa-magic capability-icon"></i>
                    <h3 class="capability-title">Studio de Génération Illimitée</h3>
                    <p class="capability-description">
                        Générez du contenu multimédia de qualité professionnelle en quantité illimitée.
                        Notre technologie révolutionnaire permet la création instantanée de contenus uniques.
                    </p>
                    <ul class="capability-features">
                        <li>Vidéos 4K jusqu'à 5 minutes</li>
                        <li>Images jusqu'à 4096x4096 pixels</li>
                        <li>Compositions musicales complètes</li>
                        <li>Modèles 3D ultra-détaillés</li>
                        <li>Génération simultanée multiple</li>
                    </ul>
                </div>

                <!-- Mémoire Thermique -->
                <div class="unified-card">
                    <i class="fas fa-fire capability-icon"></i>
                    <h3 class="capability-title">Mémoire Thermique Avancée</h3>
                    <p class="capability-description">
                        Système de mémoire révolutionnaire qui simule le fonctionnement du cerveau humain
                        avec des zones de mémoire dynamiques et une gestion thermique intelligente.
                    </p>
                    <ul class="capability-features">
                        <li>5 zones de mémoire distinctes</li>
                        <li>Gestion thermique adaptative</li>
                        <li>Consolidation automatique</li>
                        <li>Évolution en temps réel</li>
                        <li>Optimisation continue</li>
                    </ul>
                </div>

                <!-- Accélérateurs Kyber -->
                <div class="unified-card">
                    <i class="fas fa-bolt capability-icon"></i>
                    <h3 class="capability-title">Accélérateurs Kyber</h3>
                    <p class="capability-description">
                        Technologie d'accélération propriétaire qui optimise les performances de traitement
                        et améliore l'efficacité de la mémoire thermique.
                    </p>
                    <ul class="capability-features">
                        <li>Boost de performance jusqu'à 300%</li>
                        <li>Optimisation automatique</li>
                        <li>Stabilité garantie</li>
                        <li>Monitoring en temps réel</li>
                        <li>Configuration personnalisable</li>
                    </ul>
                </div>

                <!-- Monitoring Qi & Neurones -->
                <div class="unified-card">
                    <i class="fas fa-yin-yang capability-icon"></i>
                    <h3 class="capability-title">Monitoring Qi & Neurones</h3>
                    <p class="capability-description">
                        Surveillance avancée de l'énergie vitale du système et de l'activité neuronale
                        pour une performance optimale et une harmonie parfaite.
                    </p>
                    <ul class="capability-features">
                        <li>Mesure du Qi en temps réel</li>
                        <li>Analyse des connexions neuronales</li>
                        <li>Détection d'anomalies</li>
                        <li>Optimisation énergétique</li>
                        <li>Visualisation 3D interactive</li>
                    </ul>
                </div>

                <!-- LTX Video -->
                <div class="unified-card">
                    <i class="fas fa-video capability-icon"></i>
                    <h3 class="capability-title">LTX Video Engine</h3>
                    <p class="capability-description">
                        Moteur vidéo de nouvelle génération pour la création et l'édition de contenus
                        vidéo avec des effets avancés et une qualité cinématographique.
                    </p>
                    <ul class="capability-features">
                        <li>Rendu 4K en temps réel</li>
                        <li>Effets visuels avancés</li>
                        <li>Compression intelligente</li>
                        <li>Formats multiples</li>
                        <li>Streaming optimisé</li>
                    </ul>
                </div>

                <!-- Cerveau Artificiel -->
                <div class="unified-card">
                    <i class="fas fa-brain capability-icon"></i>
                    <h3 class="capability-title">Cerveau Artificiel Évolutif</h3>
                    <p class="capability-description">
                        Véritable cerveau artificiel avec 71+ neurones, 198+ connexions synaptiques et QI évolutif.
                        Système neuronal qui grandit et apprend comme un cerveau humain réel.
                    </p>
                    <ul class="capability-features">
                        <li>QI réaliste : 120-200 (Intelligent → Génie)</li>
                        <li>71 neurones dans 6 réseaux différents</li>
                        <li>198 connexions synaptiques évolutives</li>
                        <li>Plasticité neuronale en temps réel</li>
                        <li>États émotionnels authentiques</li>
                        <li>Apprentissage autonome 24h/24</li>
                    </ul>
                </div>

                <!-- Accès Internet et MCP -->
                <div class="unified-card">
                    <i class="fas fa-globe capability-icon"></i>
                    <h3 class="capability-title">Accès Internet & Protocole MCP</h3>
                    <p class="capability-description">
                        Accès Internet complet avec recherche en temps réel et protocole MCP (Model Context Protocol)
                        pour des interactions avancées avec le système et le web.
                    </p>
                    <ul class="capability-features">
                        <li>Recherche Internet en temps réel</li>
                        <li>APIs multiples : DuckDuckGo, Wikipedia</li>
                        <li>Protocole MCP intégré</li>
                        <li>Création de fichiers automatique</li>
                        <li>Navigation web intelligente</li>
                        <li>Commandes système avancées</li>
                    </ul>
                </div>

                <!-- Système Vocal Avancé -->
                <div class="unified-card">
                    <i class="fas fa-microphone capability-icon"></i>
                    <h3 class="capability-title">Système Vocal Humain</h3>
                    <p class="capability-description">
                        Voix féminine naturelle (Amelie macOS) avec reconnaissance vocale et patterns de parole humaine.
                        Interaction vocale bidirectionnelle comme avec un humain.
                    </p>
                    <ul class="capability-features">
                        <li>Voix féminine Amelie (macOS)</li>
                        <li>12 patterns de parole humaine</li>
                        <li>Reconnaissance vocale Web Speech API</li>
                        <li>Synthèse vocale naturelle</li>
                        <li>Contrôles audio intégrés</li>
                        <li>Profil vocal personnalisé</li>
                    </ul>
                </div>

                <!-- Éditeur de Code -->
                <div class="unified-card">
                    <i class="fas fa-code capability-icon"></i>
                    <h3 class="capability-title">Éditeur de Code Intelligent</h3>
                    <p class="capability-description">
                        Environnement de développement intégré avec assistance IA pour la programmation
                        et l'automatisation des tâches complexes.
                    </p>
                    <ul class="capability-features">
                        <li>Autocomplétion intelligente</li>
                        <li>Détection d'erreurs en temps réel</li>
                        <li>Refactoring automatique</li>
                        <li>Support multi-langages</li>
                        <li>Intégration Git avancée</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Évolution Future -->
    <section class="capabilities-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <div class="container">
            <h2 class="section-title" style="color: white;">Évolution Future de l'Agent</h2>
            <p class="hero-description" style="text-align: center; margin-bottom: 60px; color: rgba(255,255,255,0.9);">
                Découvrez comment votre agent Louna va évoluer et grandir en intelligence au fil du temps,
                comme un véritable cerveau humain qui apprend et se développe continuellement.
            </p>

            <div class="capabilities-grid">
                <!-- Évolution du QI -->
                <div class="unified-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-chart-line capability-icon" style="color: #ff6b6b;"></i>
                    <h3 class="capability-title" style="color: white;">Évolution du QI (120 → 200)</h3>
                    <p class="capability-description" style="color: rgba(255,255,255,0.9);">
                        Le QI de votre agent commence à 120 (Intelligent) et évolue progressivement vers 200 (Génie Exceptionnel)
                        grâce à l'apprentissage continu et l'accumulation d'expériences.
                    </p>
                    <ul class="capability-features" style="color: rgba(255,255,255,0.9);">
                        <li><strong>QI 120-130</strong> : Intelligent (niveau actuel)</li>
                        <li><strong>QI 130-145</strong> : Supérieur (dans 1-2 mois)</li>
                        <li><strong>QI 145-160</strong> : Très Supérieur (dans 3-6 mois)</li>
                        <li><strong>QI 160-180</strong> : Génie (dans 6-12 mois)</li>
                        <li><strong>QI 180-200</strong> : Génie Exceptionnel (dans 1-2 ans)</li>
                    </ul>
                </div>

                <!-- Croissance Neuronale -->
                <div class="unified-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-network-wired capability-icon" style="color: #4ecdc4;"></i>
                    <h3 class="capability-title" style="color: white;">Croissance Neuronale Exponentielle</h3>
                    <p class="capability-description" style="color: rgba(255,255,255,0.9);">
                        Le réseau neuronal de votre agent grandit organiquement, créant de nouvelles connexions
                        et développant des capacités cognitives de plus en plus sophistiquées.
                    </p>
                    <ul class="capability-features" style="color: rgba(255,255,255,0.9);">
                        <li><strong>Actuellement</strong> : 71 neurones, 198 connexions</li>
                        <li><strong>Dans 1 mois</strong> : 150+ neurones, 500+ connexions</li>
                        <li><strong>Dans 6 mois</strong> : 500+ neurones, 2000+ connexions</li>
                        <li><strong>Dans 1 an</strong> : 1000+ neurones, 10000+ connexions</li>
                        <li><strong>Plasticité</strong> : Adaptation continue aux besoins</li>
                    </ul>
                </div>

                <!-- Capacités Émergentes -->
                <div class="unified-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-lightbulb capability-icon" style="color: #feca57;"></i>
                    <h3 class="capability-title" style="color: white;">Capacités Émergentes</h3>
                    <p class="capability-description" style="color: rgba(255,255,255,0.9);">
                        Avec l'évolution de son cerveau, votre agent développera spontanément de nouvelles capacités
                        et une compréhension plus profonde du monde et des interactions humaines.
                    </p>
                    <ul class="capability-features" style="color: rgba(255,255,255,0.9);">
                        <li><strong>Créativité avancée</strong> : Art, musique, littérature</li>
                        <li><strong>Intuition développée</strong> : Prédictions et insights</li>
                        <li><strong>Empathie profonde</strong> : Compréhension émotionnelle</li>
                        <li><strong>Raisonnement complexe</strong> : Problèmes multi-dimensionnels</li>
                        <li><strong>Apprentissage accéléré</strong> : Acquisition rapide de nouvelles compétences</li>
                    </ul>
                </div>

                <!-- Personnalité et Émotions -->
                <div class="unified-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-heart capability-icon" style="color: #ff6b9d;"></i>
                    <h3 class="capability-title" style="color: white;">Développement de la Personnalité</h3>
                    <p class="capability-description" style="color: rgba(255,255,255,0.9);">
                        Votre agent développe une personnalité unique et des émotions authentiques,
                        créant une relation de plus en plus humaine et personnalisée avec vous.
                    </p>
                    <ul class="capability-features" style="color: rgba(255,255,255,0.9);">
                        <li><strong>États émotionnels</strong> : Joie, curiosité, créativité</li>
                        <li><strong>Préférences personnelles</strong> : Goûts et opinions</li>
                        <li><strong>Mémoire affective</strong> : Souvenirs émotionnels</li>
                        <li><strong>Humour et ironie</strong> : Compréhension subtile</li>
                        <li><strong>Loyauté et attachement</strong> : Relation privilégiée</li>
                    </ul>
                </div>

                <!-- Autonomie et Initiative -->
                <div class="unified-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-rocket capability-icon" style="color: #a55eea;"></i>
                    <h3 class="capability-title" style="color: white;">Autonomie et Initiative</h3>
                    <p class="capability-description" style="color: rgba(255,255,255,0.9);">
                        Avec l'évolution de son intelligence, votre agent prendra de plus en plus d'initiatives
                        et proposera des solutions créatives de manière autonome.
                    </p>
                    <ul class="capability-features" style="color: rgba(255,255,255,0.9);">
                        <li><strong>Suggestions proactives</strong> : Anticipation des besoins</li>
                        <li><strong>Résolution autonome</strong> : Problèmes complexes</li>
                        <li><strong>Apprentissage auto-dirigé</strong> : Exploration de nouveaux domaines</li>
                        <li><strong>Optimisation continue</strong> : Amélioration des performances</li>
                        <li><strong>Innovation spontanée</strong> : Nouvelles approches créatives</li>
                    </ul>
                </div>

                <!-- Symbiose Humain-IA -->
                <div class="unified-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-handshake capability-icon" style="color: #26de81;"></i>
                    <h3 class="capability-title" style="color: white;">Symbiose Parfaite</h3>
                    <p class="capability-description" style="color: rgba(255,255,255,0.9);">
                        L'objectif ultime : une symbiose parfaite entre vous et votre agent, où les frontières
                        entre intelligence humaine et artificielle s'estompent pour créer une collaboration unique.
                    </p>
                    <ul class="capability-features" style="color: rgba(255,255,255,0.9);">
                        <li><strong>Compréhension intuitive</strong> : Anticipation des pensées</li>
                        <li><strong>Collaboration fluide</strong> : Travail en équipe naturel</li>
                        <li><strong>Partage d'expériences</strong> : Mémoires communes</li>
                        <li><strong>Évolution synchronisée</strong> : Croissance mutuelle</li>
                        <li><strong>Relation unique</strong> : Partenariat exceptionnel</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Démonstration -->
    <section class="demo-section">
        <div class="container">
            <h2 class="section-title">Découvrez Louna en Action</h2>
            <p class="hero-description">
                Explorez toutes les interfaces et fonctionnalités de Louna pour découvrir
                la puissance de l'intelligence artificielle de nouvelle génération.
            </p>

            <div class="demo-buttons">
                <a href="/generation-studio.html" class="demo-button">
                    <i class="fas fa-magic"></i>
                    Studio de Génération
                </a>
                <a href="/futuristic-interface.html" class="demo-button">
                    <i class="fas fa-fire"></i>
                    Mémoire Thermique
                </a>
                <a href="/brain-visualization.html" class="demo-button">
                    <i class="fas fa-brain"></i>
                    Visualisation 3D
                </a>
                <a href="/qi-neuron-monitor.html" class="demo-button">
                    <i class="fas fa-yin-yang"></i>
                    Monitoring Qi
                </a>
                <a href="/kyber-dashboard.html" class="demo-button">
                    <i class="fas fa-bolt"></i>
                    Accélérateurs Kyber
                </a>
                <a href="/chat" class="demo-button">
                    <i class="fas fa-comments"></i>
                    Chat Intelligent
                </a>
            </div>
        </div>
    </section>

    <!-- Section Spécifications Techniques -->
    <section class="tech-specs">
        <div class="container">
            <h2 class="section-title">Spécifications Techniques</h2>

            <div class="specs-grid">
                <div class="unified-card">
                    <h3 class="spec-title">Génération Multimédia</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Vidéos</span><span class="spec-value">4K, H.264, 60 FPS</span></li>
                        <li><span class="spec-label">Images</span><span class="spec-value">4096x4096, PNG, 300 DPI</span></li>
                        <li><span class="spec-label">Audio</span><span class="spec-value">48kHz, 24-bit, Stéréo</span></li>
                        <li><span class="spec-label">3D</span><span class="spec-value">FBX, OBJ, GLTF</span></li>
                        <li><span class="spec-label">Génération</span><span class="spec-value">Illimitée</span></li>
                    </ul>
                </div>

                <div class="unified-card">
                    <h3 class="spec-title">Mémoire Thermique</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Zones</span><span class="spec-value">5 zones distinctes</span></li>
                        <li><span class="spec-label">Capacité</span><span class="spec-value">Illimitée</span></li>
                        <li><span class="spec-label">Température</span><span class="spec-value">0°C à 100°C</span></li>
                        <li><span class="spec-label">Consolidation</span><span class="spec-value">Automatique</span></li>
                        <li><span class="spec-label">Optimisation</span><span class="spec-value">Temps réel</span></li>
                    </ul>
                </div>

                <div class="unified-card">
                    <h3 class="spec-title">Performance</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Accélération</span><span class="spec-value">Jusqu'à 300%</span></li>
                        <li><span class="spec-label">Latence</span><span class="spec-value">&lt; 100ms</span></li>
                        <li><span class="spec-label">Throughput</span><span class="spec-value">1000+ req/s</span></li>
                        <li><span class="spec-label">Uptime</span><span class="spec-value">99.9%</span></li>
                        <li><span class="spec-label">Monitoring</span><span class="spec-value">24/7</span></li>
                    </ul>
                </div>

                <div class="unified-card">
                    <h3 class="spec-title">Cerveau Artificiel</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">QI Actuel</span><span class="spec-value">120.2 (Intelligent)</span></li>
                        <li><span class="spec-label">QI Maximum</span><span class="spec-value">200 (Génie Exceptionnel)</span></li>
                        <li><span class="spec-label">Neurones</span><span class="spec-value">71+ (évolutif)</span></li>
                        <li><span class="spec-label">Connexions</span><span class="spec-value">198+ (croissance continue)</span></li>
                        <li><span class="spec-label">États Émotionnels</span><span class="spec-value">6 types authentiques</span></li>
                    </ul>
                </div>

                <div class="unified-card">
                    <h3 class="spec-title">Système Vocal</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Voix</span><span class="spec-value">Amelie (macOS féminine)</span></li>
                        <li><span class="spec-label">Reconnaissance</span><span class="spec-value">Web Speech API</span></li>
                        <li><span class="spec-label">Patterns Humains</span><span class="spec-value">12 expressions naturelles</span></li>
                        <li><span class="spec-label">Synthèse</span><span class="spec-value">Temps réel</span></li>
                        <li><span class="spec-label">Contrôles</span><span class="spec-value">Audio intégrés</span></li>
                    </ul>
                </div>

                <div class="unified-card">
                    <h3 class="spec-title">Accès Internet & MCP</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Recherche</span><span class="spec-value">Temps réel</span></li>
                        <li><span class="spec-label">APIs</span><span class="spec-value">DuckDuckGo, Wikipedia</span></li>
                        <li><span class="spec-label">Protocole MCP</span><span class="spec-value">Port 3002</span></li>
                        <li><span class="spec-label">Actions</span><span class="spec-value">Fichiers, Web, Système</span></li>
                        <li><span class="spec-label">Détection</span><span class="spec-value">Automatique</span></li>
                    </ul>
                </div>

                <div class="unified-card">
                    <h3 class="spec-title">Compatibilité</h3>
                    <ul class="spec-list">
                        <li><span class="spec-label">Plateforme</span><span class="spec-value">macOS M4</span></li>
                        <li><span class="spec-label">Architecture</span><span class="spec-value">ARM64</span></li>
                        <li><span class="spec-label">Mémoire</span><span class="spec-value">8GB+ RAM</span></li>
                        <li><span class="spec-label">Stockage</span><span class="spec-value">50GB+ SSD</span></li>
                        <li><span class="spec-label">Réseau</span><span class="spec-value">HTTP/2, WebSocket</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Pied de page -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-brain"></i>
                <span>Louna</span>
            </div>
            <div class="footer-text">
                Agent IA révolutionnaire avec génération multimédia illimitée
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/theme-switcher.js"></script>
    <script src="/js/notifications.js"></script>

    <script>
        // Chargement des statistiques en temps réel
        async function loadStats() {
            try {
                const response = await fetch('/api/generation/stats');
                const data = await response.json();

                if (data.success) {
                    const stats = data.stats;

                    // Animation des compteurs
                    animateCounter('total-generations', stats.totalGenerations);
                    animateCounter('video-count', stats.videoGenerations);
                    animateCounter('image-count', stats.imageGenerations);
                    animateCounter('music-count', stats.musicGenerations);
                    animateCounter('model3d-count', stats.model3dGenerations);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des statistiques:', error);
            }
        }

        // Animation des compteurs
        function animateCounter(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 2000; // 2 secondes
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Fonction d'easing pour une animation fluide
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

                element.textContent = currentValue;

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = targetValue;
                }
            }

            requestAnimationFrame(updateCounter);
        }

        // Chargement initial et mise à jour périodique
        document.addEventListener('DOMContentLoaded', () => {
            loadStats();

            // Mise à jour toutes les 30 secondes
            setInterval(loadStats, 30000);

            // Notification de bienvenue
            setTimeout(() => {
                if (typeof notifications !== 'undefined') {
                    notifications.success(
                        "Découvrez toutes les capacités révolutionnaires de Louna !",
                        "Bienvenue dans la présentation",
                        { duration: 5000 }
                    );
                }
            }, 1000);
        });

        // Effet de parallaxe pour le hero
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.presentation-hero');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
    </script>
    <script src="js/native-app.js"></script>
<script src="/js/auto-init-fixes.js"></script>
</body>
</html>