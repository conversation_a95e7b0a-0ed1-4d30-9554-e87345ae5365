<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Intelligence Artificielle Unifiée</title>
    <link rel="stylesheet" href="/css/louna-unified-theme.css?v=2025">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="louna-unified theme-2025">
    <div class="louna-container">
        <div class="page-header fade-in-up">
            <h1 class="page-title">
                <i class="fas fa-brain"></i>
                Louna - Intelligence Artificielle Évolutive
            </h1>
            <p class="page-subtitle">
                Système d'intelligence artificielle avec mémoire thermique, cerveau 3D et génération multimédia illimitée
            </p>
        </div>

        <!-- Statistiques en temps réel -->
        <div class="louna-grid grid-4 mb-lg">
            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-yin-yang"></i>
                    <h3 class="card-title">QI Système</h3>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2.5rem; font-weight: bold; color: var(--primary-color);" id="qi-value">120</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Intelligence Quantique</div>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-fire"></i>
                    <h3 class="card-title">Mémoire Thermique</h3>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2.5rem; font-weight: bold; color: var(--secondary-color);" id="thermal-temp">42.3°C</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Température Active</div>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-network-wired"></i>
                    <h3 class="card-title">Neurones</h3>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2.5rem; font-weight: bold; color: var(--accent-color);" id="neuron-count">21</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Réseaux Actifs</div>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-rocket"></i>
                    <h3 class="card-title">Accélérateurs</h3>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2.5rem; font-weight: bold; color: var(--primary-color);" id="accelerator-count">3</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Kyber Actifs</div>
                </div>
            </div>
        </div>

        <!-- Interfaces principales -->
        <div class="louna-grid grid-3 mb-lg">
            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-th-large"></i>
                    <h3 class="card-title">Hub Central Intelligent</h3>
                </div>
                <p class="mb-md">Interface principale de contrôle et navigation avec toutes les fonctionnalités unifiées.</p>
                <div style="display: flex; gap: 10px;">
                    <a href="/unified-hub.html" class="louna-btn btn-primary">
                        <i class="fas fa-th-large"></i>
                        Accéder
                    </a>
                    <button class="louna-btn btn-secondary" onclick="previewInterface('hub')">
                        <i class="fas fa-eye"></i>
                        Aperçu
                    </button>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-fire"></i>
                    <h3 class="card-title">Mémoire Thermique</h3>
                </div>
                <p class="mb-md">Système de mémoire thermique avancé avec gestion automatique des températures et évolution continue.</p>
                <div style="display: flex; gap: 10px;">
                    <a href="/futuristic-interface.html" class="louna-btn btn-primary">
                        <i class="fas fa-fire"></i>
                        Accéder
                    </a>
                    <button class="louna-btn btn-secondary" onclick="previewInterface('memory')">
                        <i class="fas fa-eye"></i>
                        Aperçu
                    </button>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-brain"></i>
                    <h3 class="card-title">Cerveau 3D</h3>
                </div>
                <p class="mb-md">Visualisation 3D en temps réel du cerveau artificiel avec monitoring des connexions neuronales.</p>
                <div style="display: flex; gap: 10px;">
                    <a href="/brain-visualization.html" class="louna-btn btn-primary">
                        <i class="fas fa-brain"></i>
                        Accéder
                    </a>
                    <button class="louna-btn btn-secondary" onclick="previewInterface('brain')">
                        <i class="fas fa-eye"></i>
                        Aperçu
                    </button>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-yin-yang"></i>
                    <h3 class="card-title">QI & Neurones Monitor</h3>
                </div>
                <p class="mb-md">Monitoring en temps réel du QI et de l'activité neuronale avec graphiques et statistiques.</p>
                <div style="display: flex; gap: 10px;">
                    <a href="/qi-neuron-monitor.html" class="louna-btn btn-primary">
                        <i class="fas fa-yin-yang"></i>
                        Accéder
                    </a>
                    <button class="louna-btn btn-secondary" onclick="previewInterface('qi')">
                        <i class="fas fa-eye"></i>
                        Aperçu
                    </button>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-magic"></i>
                    <h3 class="card-title">Studio de Génération</h3>
                </div>
                <p class="mb-md">Génération multimédia illimitée : vidéos, photos, musique, 3D avec qualité professionnelle.</p>
                <div style="display: flex; gap: 10px;">
                    <a href="/generation-studio.html" class="louna-btn btn-primary">
                        <i class="fas fa-magic"></i>
                        Accéder
                    </a>
                    <button class="louna-btn btn-secondary" onclick="previewInterface('studio')">
                        <i class="fas fa-eye"></i>
                        Aperçu
                    </button>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-comments"></i>
                    <h3 class="card-title">Chat Intelligent</h3>
                </div>
                <p class="mb-md">Interface de chat avec l'agent principal, recherche Internet et capacités cognitives avancées.</p>
                <div style="display: flex; gap: 10px;">
                    <a href="/chat-simple" class="louna-btn btn-primary">
                        <i class="fas fa-comments"></i>
                        Accéder
                    </a>
                    <button class="louna-btn btn-secondary" onclick="previewInterface('chat')">
                        <i class="fas fa-eye"></i>
                        Aperçu
                    </button>
                </div>
            </div>
        </div>

        <!-- Fonctionnalités avancées -->
        <div class="louna-grid grid-2 mb-lg">
            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-cogs"></i>
                    <h3 class="card-title">Fonctionnalités Avancées</h3>
                </div>
                <div class="louna-grid grid-2" style="gap: 10px;">
                    <a href="/agents.html" class="louna-btn btn-secondary">
                        <i class="fas fa-users"></i>
                        Agents
                    </a>
                    <a href="/training.html" class="louna-btn btn-secondary">
                        <i class="fas fa-graduation-cap"></i>
                        Formation
                    </a>
                    <a href="/security-dashboard.html" class="louna-btn btn-secondary">
                        <i class="fas fa-shield-alt"></i>
                        Sécurité
                    </a>
                    <a href="/performance.html" class="louna-btn btn-secondary">
                        <i class="fas fa-chart-line"></i>
                        Performance
                    </a>
                    <a href="/camera-vision-interface.html" class="louna-btn btn-secondary">
                        <i class="fas fa-camera"></i>
                        Vision
                    </a>
                    <a href="/code-editor.html" class="louna-btn btn-secondary">
                        <i class="fas fa-code"></i>
                        Code
                    </a>
                </div>
            </div>

            <div class="louna-card fade-in-up">
                <div class="card-header">
                    <i class="card-icon fas fa-info-circle"></i>
                    <h3 class="card-title">Informations Système</h3>
                </div>
                <div style="font-size: 0.9rem; line-height: 1.6;">
                    <p><strong>Version :</strong> Louna 2025 - Design Unifié</p>
                    <p><strong>Créateur :</strong> Jean-Luc Passave</p>
                    <p><strong>Localisation :</strong> Sainte-Anne, Guadeloupe</p>
                    <p><strong>Architecture :</strong> Electron natif macOS M4</p>
                    <p><strong>Serveurs :</strong> Principal (3000) + Louna (3005)</p>
                    <p><strong>Statut :</strong> <span style="color: var(--secondary-color);">✅ Tous systèmes opérationnels</span></p>
                </div>
                <div style="margin-top: 15px; display: flex; gap: 10px;">
                    <a href="/navigation" class="louna-btn btn-primary">
                        <i class="fas fa-sitemap"></i>
                        Navigation Complète
                    </a>
                    <a href="/rapport" class="louna-btn btn-secondary">
                        <i class="fas fa-clipboard-check"></i>
                        Rapport
                    </a>
                </div>
            </div>
        </div>

        <!-- Aperçu modal -->
        <div id="preview-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 10000; padding: 20px;">
            <div style="background: var(--bg-card); border-radius: var(--radius-lg); padding: 20px; max-width: 800px; margin: 0 auto; position: relative;">
                <button onclick="closePreview()" style="position: absolute; top: 15px; right: 15px; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
                <div id="preview-content">
                    <!-- Contenu de l'aperçu -->
                </div>
            </div>
        </div>
    </div>

    <script src="/js/auto-unify-interfaces.js?v=2025"></script>
    <script>
        // Données de prévisualisation
        const previews = {
            hub: {
                title: 'Hub Central Intelligent',
                description: 'Interface principale avec navigation unifiée, contrôle de tous les systèmes et accès rapide à toutes les fonctionnalités.',
                features: ['Navigation unifiée', 'Contrôle système', 'Monitoring temps réel', 'Accès rapide']
            },
            memory: {
                title: 'Mémoire Thermique',
                description: 'Système de mémoire thermique avec gestion automatique des températures et évolution continue des données.',
                features: ['Gestion thermique', 'Évolution automatique', 'Compression intelligente', 'Sauvegarde continue']
            },
            brain: {
                title: 'Cerveau 3D',
                description: 'Visualisation 3D en temps réel du cerveau artificiel avec monitoring des connexions neuronales.',
                features: ['Visualisation 3D', 'Monitoring neurones', 'Connexions temps réel', 'Interface immersive']
            },
            qi: {
                title: 'QI & Neurones Monitor',
                description: 'Monitoring en temps réel du QI et de l\'activité neuronale avec graphiques et statistiques détaillées.',
                features: ['QI temps réel', 'Graphiques neurones', 'Statistiques avancées', 'Alertes intelligentes']
            },
            studio: {
                title: 'Studio de Génération',
                description: 'Génération multimédia illimitée avec qualité professionnelle pour tous types de contenus.',
                features: ['Génération vidéo', 'Création photos', 'Musique IA', 'Modèles 3D']
            },
            chat: {
                title: 'Chat Intelligent',
                description: 'Interface de chat avec l\'agent principal, recherche Internet et capacités cognitives avancées.',
                features: ['Agent principal', 'Recherche Internet', 'Réflexions visibles', 'Réponses vocales']
            }
        };

        function previewInterface(type) {
            const preview = previews[type];
            if (!preview) return;

            const content = `
                <h2 style="color: var(--primary-color); margin-bottom: 15px;">
                    <i class="fas fa-${getIconForType(type)}"></i>
                    ${preview.title}
                </h2>
                <p style="margin-bottom: 20px; line-height: 1.6;">${preview.description}</p>
                <h3 style="color: var(--secondary-color); margin-bottom: 10px;">Fonctionnalités principales :</h3>
                <ul style="margin-left: 20px; margin-bottom: 20px;">
                    ${preview.features.map(f => `<li>${f}</li>`).join('')}
                </ul>
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <a href="${getUrlForType(type)}" class="louna-btn btn-primary">
                        <i class="fas fa-external-link-alt"></i>
                        Ouvrir l'interface
                    </a>
                    <button onclick="closePreview()" class="louna-btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Fermer
                    </button>
                </div>
            `;

            document.getElementById('preview-content').innerHTML = content;
            document.getElementById('preview-modal').style.display = 'block';
        }

        function closePreview() {
            document.getElementById('preview-modal').style.display = 'none';
        }

        function getIconForType(type) {
            const icons = {
                hub: 'th-large',
                memory: 'fire',
                brain: 'brain',
                qi: 'yin-yang',
                studio: 'magic',
                chat: 'comments'
            };
            return icons[type] || 'cog';
        }

        function getUrlForType(type) {
            const urls = {
                hub: '/unified-hub.html',
                memory: '/futuristic-interface.html',
                brain: '/brain-visualization.html',
                qi: '/qi-neuron-monitor.html',
                studio: '/generation-studio.html',
                chat: '/chat-simple'
            };
            return urls[type] || '/';
        }

        // Mise à jour des statistiques en temps réel
        function updateStats() {
            // Simulation de données temps réel
            const qi = 120 + Math.floor(Math.random() * 10);
            const temp = (42.3 + Math.random() * 2).toFixed(1);
            const neurons = 21 + Math.floor(Math.random() * 5);
            const accelerators = 3 + Math.floor(Math.random() * 2);

            document.getElementById('qi-value').textContent = qi;
            document.getElementById('thermal-temp').textContent = temp + '°C';
            document.getElementById('neuron-count').textContent = neurons;
            document.getElementById('accelerator-count').textContent = accelerators;
        }

        // Mise à jour toutes les 3 secondes
        setInterval(updateStats, 3000);

        // Animation d'accueil
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const toast = document.createElement('div');
                toast.innerHTML = `
                    <i class="fas fa-brain"></i>
                    <span>Bienvenue dans Louna - Toutes les interfaces sont maintenant unifiées !</span>
                `;
                
                Object.assign(toast.style, {
                    position: 'fixed',
                    top: '100px',
                    right: '20px',
                    background: 'linear-gradient(135deg, var(--primary-color), var(--secondary-color))',
                    color: 'white',
                    padding: '15px 25px',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    zIndex: '9999',
                    opacity: '0',
                    transform: 'translateX(100%)',
                    transition: 'all 0.5s ease',
                    boxShadow: '0 8px 25px rgba(233, 30, 99, 0.3)',
                    fontSize: '1rem',
                    fontWeight: 'bold'
                });
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.style.opacity = '1';
                    toast.style.transform = 'translateX(0)';
                }, 100);
                
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 500);
                }, 5000);
            }, 1500);
        });

        // Fermer modal avec Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePreview();
            }
        });
    </script>
</body>
</html>
