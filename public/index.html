<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Assistant <PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            text-align: center;
        }

        .logo {
            font-size: 36px;
            font-weight: 700;
            color: #ff69b4;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }

        .welcome-card {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 40px;
            max-width: 800px;
            text-align: center;
            margin-bottom: 40px;
        }

        .welcome-title {
            font-size: 32px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 20px;
        }

        .welcome-text {
            font-size: 18px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            max-width: 1200px;
            width: 100%;
        }

        .feature-card {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #ff69b4;
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.2);
        }

        .feature-icon {
            font-size: 48px;
            color: #ff69b4;
            margin-bottom: 20px;
        }

        .feature-title {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 15px;
        }

        .feature-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.5;
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .action-btn {
            background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }

        .action-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .action-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">🌙 Louna</div>
        <div class="subtitle">Assistant IA Avancé avec Mémoire Thermique</div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Welcome Card -->
        <div class="welcome-card">
            <div class="welcome-title">Bienvenue dans Louna</div>
            <div class="welcome-text">
                Votre assistant IA avancé créé par Jean-Luc Passave à Sainte-Anne, Guadeloupe. 
                Équipé d'une mémoire thermique révolutionnaire, d'accélérateurs Kyber et d'un cerveau artificiel naturel.
            </div>
            
            <div class="quick-actions">
                <a href="/chat" class="action-btn">💬 Chat</a>
                <a href="/futuristic-interface.html" class="action-btn secondary">🧠 Mémoire Thermique</a>
                <a href="/brain-visualization.html" class="action-btn secondary">🎯 Cerveau 3D</a>
                <a href="/qi-neuron-monitor.html" class="action-btn secondary">📊 Monitoring QI</a>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card" onclick="window.location.href='/chat'">
                <div class="feature-icon">💬</div>
                <div class="feature-title">Chat Intelligent</div>
                <div class="feature-description">Interface de conversation avancée avec IA cognitive</div>
            </div>

            <div class="feature-card" onclick="window.location.href='/futuristic-interface.html'">
                <div class="feature-icon">🧠</div>
                <div class="feature-title">Mémoire Thermique</div>
                <div class="feature-description">Système de mémoire révolutionnaire avec zones de température</div>
            </div>

            <div class="feature-card" onclick="window.location.href='/brain-visualization.html'">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">Cerveau 3D</div>
                <div class="feature-description">Visualisation 3D du cerveau artificiel en temps réel</div>
            </div>

            <div class="feature-card" onclick="window.location.href='/qi-neuron-monitor.html'">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Monitoring QI</div>
                <div class="feature-description">Surveillance en temps réel du QI et des neurones</div>
            </div>

            <div class="feature-card" onclick="window.location.href='/kyber-dashboard.html'">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Accélérateurs Kyber</div>
                <div class="feature-description">Tableau de bord des accélérateurs de performance</div>
            </div>

            <div class="feature-card" onclick="window.location.href='/generation-studio.html'">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">Studio Génération</div>
                <div class="feature-description">Génération multimédia illimitée (vidéo, audio, 3D)</div>
            </div>
        </div>
    </div>

    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-item">
            <div class="status-dot"></div>
            <span>Système Actif</span>
        </div>
        <div class="status-item">
            <span>🧠 QI: 120</span>
        </div>
        <div class="status-item">
            <span>⚡ Accélérateurs: 24</span>
        </div>
        <div class="status-item">
            <span>🌡️ Mémoire: Optimale</span>
        </div>
    </div>

    <script>
        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card, .welcome-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // Mise à jour du statut en temps réel
        function updateStatus() {
            fetch('/api/system/status')
                .then(response => response.json())
                .then(data => {
                    if (data.qi) {
                        document.querySelector('.status-item:nth-child(2) span').textContent = `🧠 QI: ${data.qi}`;
                    }
                    if (data.accelerators) {
                        document.querySelector('.status-item:nth-child(3) span').textContent = `⚡ Accélérateurs: ${data.accelerators}`;
                    }
                })
                .catch(error => console.log('Status update failed:', error));
        }

        // Mise à jour toutes les 5 secondes
        setInterval(updateStatus, 5000);
        updateStatus();
    </script>
</body>
</html>
