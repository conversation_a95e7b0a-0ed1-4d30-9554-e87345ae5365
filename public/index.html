<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Louna - Interface Cognitive Avancée</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --louna-primary: #9c89b8;
      --louna-secondary: #f0a6ca;
      --louna-accent: #b8bedd;
      --louna-dark: #1a1a2e;
      --louna-light: #edf2fb;
      --louna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
      --header-height: 60px;
      --sidebar-width: 250px;
    }

    body {
      font-family: 'Quicksand', sans-serif;
      background-color: var(--louna-light);
      color: var(--louna-dark);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .header {
      background: var(--louna-gradient);
      color: white;
      height: var(--header-height);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      display: flex;
      align-items: center;
      padding: 0 1rem;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .header h1 {
      font-size: 1.5rem;
      margin: 0;
      font-weight: 700;
    }

    .sidebar {
      background-color: white;
      width: var(--sidebar-width);
      position: fixed;
      top: var(--header-height);
      left: 0;
      bottom: 0;
      padding: 1rem 0;
      box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
      overflow-y: auto;
      z-index: 900;
    }

    .sidebar-menu {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .sidebar-menu li {
      padding: 0.5rem 1rem;
    }

    .sidebar-menu a {
      color: var(--louna-dark);
      text-decoration: none;
      display: flex;
      align-items: center;
      padding: 0.5rem;
      border-radius: 5px;
      transition: all 0.3s ease;
    }

    .sidebar-menu a:hover {
      background-color: rgba(156, 137, 184, 0.1);
    }

    .sidebar-menu a.active {
      background-color: var(--louna-primary);
      color: white;
    }

    .sidebar-menu i {
      margin-right: 0.5rem;
      font-size: 1.2rem;
    }

    .main-content {
      margin-left: var(--sidebar-width);
      margin-top: var(--header-height);
      padding: 2rem;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .home-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      padding: 2rem;
    }

    .welcome-section {
      text-align: center;
      margin-bottom: 3rem;
    }

    .welcome-section h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      background: var(--louna-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }

    .welcome-section p {
      font-size: 1.2rem;
      color: #666;
      max-width: 800px;
      margin: 0 auto;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .feature-card {
      background-color: #f9f9f9;
      border-radius: 10px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--louna-primary);
    }

    .feature-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .feature-description {
      color: #666;
      font-size: 0.9rem;
    }

    .stats-section {
      background-color: #f9f9f9;
      border-radius: 10px;
      padding: 1.5rem;
    }

    .stats-title {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1.5rem;
    }

    .stat-card {
      background-color: white;
      border-radius: 10px;
      padding: 1.5rem;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      color: var(--louna-primary);
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: #666;
      font-size: 0.9rem;
    }

    .footer {
      background-color: var(--louna-dark);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: auto;
      margin-left: var(--sidebar-width);
    }

    .system-status {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0.5rem;
    }

    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }

    .status-indicator.active {
      background-color: #4CAF50;
    }

    .status-indicator.inactive {
      background-color: #F44336;
    }

    .status-text {
      font-size: 0.8rem;
      margin-right: 1rem;
    }

    .version {
      font-size: 0.8rem;
      opacity: 0.7;
    }

    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }

      .sidebar.show {
        transform: translateX(0);
      }

      .main-content, .footer {
        margin-left: 0;
      }

      .menu-toggle {
        display: block;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <button class="menu-toggle btn btn-link text-white me-3 d-md-none">
      <i class="bi bi-list"></i>
    </button>
    <h1>Louna - Interface Cognitive Avancée</h1>
  </header>

  <nav class="sidebar">
    <ul class="sidebar-menu">
      <li><a href="/" class="active"><i class="bi bi-house"></i> Accueil</a></li>
      <li><a href="/chat"><i class="bi bi-chat"></i> Chat</a></li>
      <li><a href="/futuristic-interface.html"><i class="bi bi-brain"></i> Mémoire Thermique</a></li>
      <li><a href="/brain-visualization.html"><i class="bi bi-eye"></i> Cerveau 3D</a></li>
      <li><a href="/qi-neuron-monitor.html"><i class="bi bi-activity"></i> Monitoring QI</a></li>
      <li><a href="/kyber-dashboard.html"><i class="bi bi-lightning"></i> Accélérateurs Kyber</a></li>
      <li><a href="/generation-studio.html"><i class="bi bi-palette"></i> Studio Génération</a></li>
      <li><a href="/code-editor.html"><i class="bi bi-code-square"></i> Éditeur Code</a></li>
      <li><a href="/agents.html"><i class="bi bi-people"></i> Gestion Agents</a></li>
      <li><a href="/training.html"><i class="bi bi-mortarboard"></i> Formation</a></li>
      <li><a href="/security-dashboard.html"><i class="bi bi-shield-lock"></i> Sécurité</a></li>
      <li><a href="/performance.html"><i class="bi bi-speedometer2"></i> Performances</a></li>
      <li><a href="/settings"><i class="bi bi-gear"></i> Paramètres</a></li>
    </ul>
  </nav>

  <main class="main-content">
    <div class="home-container">
      <section class="welcome-section">
        <h2>Bienvenue sur Louna</h2>
        <p>Votre interface cognitive avancée pour interagir avec l'intelligence artificielle de manière naturelle et intuitive.</p>
      </section>

      <section class="features-grid">
        <div class="feature-card" onclick="window.location.href='/chat'">
          <div class="feature-icon"><i class="bi bi-chat-dots"></i></div>
          <h3 class="feature-title">Chat Intelligent</h3>
          <p class="feature-description">Discutez avec Louna comme avec un humain. Elle comprend le contexte et apprend de vos interactions.</p>
        </div>

        <div class="feature-card" onclick="window.location.href='/futuristic-interface.html'">
          <div class="feature-icon"><i class="bi bi-brain"></i></div>
          <h3 class="feature-title">Mémoire Thermique</h3>
          <p class="feature-description">Louna se souvient de vos conversations et utilise sa mémoire thermique pour des réponses contextuelles.</p>
        </div>

        <div class="feature-card" onclick="window.location.href='/code-editor.html'">
          <div class="feature-icon"><i class="bi bi-code-square"></i></div>
          <h3 class="feature-title">Assistance Code</h3>
          <p class="feature-description">Obtenez de l'aide pour vos projets de programmation avec des suggestions intelligentes.</p>
        </div>

        <div class="feature-card" onclick="window.location.href='/kyber-dashboard.html'">
          <div class="feature-icon"><i class="bi bi-lightning"></i></div>
          <h3 class="feature-title">Accélérateurs Kyber</h3>
          <p class="feature-description">Utilisez les accélérateurs pour des tâches spécifiques et augmentez votre productivité.</p>
        </div>

        <div class="feature-card" onclick="window.location.href='/security-dashboard.html'">
          <div class="feature-icon"><i class="bi bi-shield-lock"></i></div>
          <h3 class="feature-title">Sécurité Avancée</h3>
          <p class="feature-description">Vos données sont protégées avec des protocoles de sécurité de pointe.</p>
        </div>

        <div class="feature-card" onclick="window.location.href='/generation-studio.html'">
          <div class="feature-icon"><i class="bi bi-palette"></i></div>
          <h3 class="feature-title">Studio Génération</h3>
          <p class="feature-description">Créez du contenu multimédia illimité : vidéos, photos, musique, modèles 3D.</p>
        </div>
      </section>

      <section class="stats-section">
        <h3 class="stats-title">Statistiques du Système</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value" id="qi-value">150</div>
            <div class="stat-label">QI Actuel</div>
          </div>

          <div class="stat-card">
            <div class="stat-value" id="memory-count">1,247</div>
            <div class="stat-label">Mémoires Stockées</div>
          </div>

          <div class="stat-card">
            <div class="stat-value" id="accelerators">8</div>
            <div class="stat-label">Accélérateurs Kyber</div>
          </div>

          <div class="stat-card">
            <div class="stat-value" id="response-time">2 ms</div>
            <div class="stat-label">Temps de Réponse</div>
          </div>
        </div>
      </section>
    </div>
  </main>

  <footer class="footer">
    <p>Louna - Interface Cognitive Avancée &copy; 2025 - Créé par Jean-Luc Passave</p>
    <div class="system-status">
      <span class="status-indicator active"></span>
      <span class="status-text">Système actif</span>
      <span class="version">v2.0.0</span>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const menuToggle = document.querySelector('.menu-toggle');
      const sidebar = document.querySelector('.sidebar');

      // Gérer le menu mobile
      if (menuToggle) {
        menuToggle.addEventListener('click', function() {
          sidebar.classList.toggle('show');
        });
      }

      // Animation des statistiques
      function animateStats() {
        const qiValue = document.getElementById('qi-value');
        const memoryCount = document.getElementById('memory-count');
        const accelerators = document.getElementById('accelerators');
        const responseTime = document.getElementById('response-time');

        // Simulation de données en temps réel
        setInterval(() => {
          const qi = 145 + Math.floor(Math.random() * 10);
          const memory = 1200 + Math.floor(Math.random() * 100);
          const accel = 6 + Math.floor(Math.random() * 4);
          const time = 1 + Math.floor(Math.random() * 3);

          qiValue.textContent = qi;
          memoryCount.textContent = memory.toLocaleString();
          accelerators.textContent = accel;
          responseTime.textContent = time + ' ms';
        }, 3000);
      }

      animateStats();
    });
  </script>
</body>
</html>
