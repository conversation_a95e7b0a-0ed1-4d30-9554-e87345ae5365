<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Hub Central Intelligent</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .hub-container {
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            height: 100vh;
            gap: 0;
        }

        /* Sidebar gauche */
        .sidebar {
            background: rgba(26, 26, 46, 0.95);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            overflow-y: auto;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo-section h1 {
            font-size: 24px;
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 5px;
        }

        .logo-section .subtitle {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-section {
            margin-bottom: 25px;
        }

        .nav-section h3 {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 5px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-item:hover {
            background: rgba(78, 205, 196, 0.1);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: rgba(78, 205, 196, 0.2);
            border-left: 3px solid #4ecdc4;
        }

        .nav-item i {
            width: 20px;
            margin-right: 12px;
            color: #4ecdc4;
        }

        .nav-item span {
            font-size: 14px;
            color: #ffffff;
        }

        .nav-item .badge {
            margin-left: auto;
            width: 8px;
            height: 8px;
            background: #ff6b6b;
            border-radius: 50%;
        }

        /* Zone de chat centrale */
        .chat-area {
            display: flex;
            flex-direction: column;
            background: rgba(22, 33, 62, 0.8);
        }

        .chat-header {
            background: rgba(15, 52, 96, 0.9);
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            display: flex;
            align-items: center;
        }

        .chat-title i {
            color: #4ecdc4;
            margin-right: 10px;
            font-size: 18px;
        }

        .chat-title h2 {
            font-size: 18px;
            color: #ffffff;
        }

        .chat-title .subtitle {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-left: 10px;
        }

        .header-buttons {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            padding: 8px 15px;
            background: rgba(78, 205, 196, 0.2);
            border: 1px solid #4ecdc4;
            border-radius: 6px;
            color: #4ecdc4;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .header-btn:hover {
            background: rgba(78, 205, 196, 0.3);
        }

        .home-btn {
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            border: none;
            color: white;
            font-weight: bold;
        }

        .home-btn:hover {
            background: linear-gradient(135deg, #ff1493, #20b2aa);
            transform: scale(1.05);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .message {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
            background: rgba(26, 26, 46, 0.6);
            padding: 15px;
            border-radius: 12px;
            border-left: 3px solid #4ecdc4;
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .message-name {
            font-weight: bold;
            color: #4ecdc4;
            margin-right: 10px;
        }

        .message-time {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.5);
        }

        .message-text {
            color: #ffffff;
            line-height: 1.5;
        }

        .chat-input-area {
            padding: 20px;
            background: rgba(15, 52, 96, 0.9);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-actions {
            display: flex;
            gap: 8px;
        }

        .input-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .input-btn:hover {
            background: rgba(78, 205, 196, 0.3);
        }

        .chat-input {
            flex: 1;
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }

        .chat-input:focus {
            border-color: #4ecdc4;
            box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .send-btn {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        /* Panneau droit */
        .right-panel {
            background: rgba(26, 26, 46, 0.95);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            overflow-y: auto;
        }

        .panel-section {
            margin-bottom: 25px;
            background: rgba(15, 52, 96, 0.6);
            border-radius: 12px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-title {
            font-size: 14px;
            color: #4ecdc4;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
        }

        .action-btn {
            width: 100%;
            padding: 10px;
            background: rgba(78, 205, 196, 0.2);
            border: 1px solid #4ecdc4;
            border-radius: 8px;
            color: #4ecdc4;
            font-size: 12px;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(78, 205, 196, 0.3);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .quick-action {
            padding: 12px;
            background: rgba(15, 52, 96, 0.8);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .quick-action:hover {
            background: rgba(78, 205, 196, 0.2);
            transform: translateY(-2px);
        }

        .quick-action i {
            display: block;
            font-size: 16px;
            margin-bottom: 5px;
            color: #4ecdc4;
        }

        .quick-action span {
            font-size: 11px;
            color: #ffffff;
        }

        .activity-item {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .activity-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="hub-container">
        <!-- Sidebar gauche -->
        <div class="sidebar">
            <div class="logo-section">
                <h1>LOUNA</h1>
                <div class="subtitle">Intelligence Évolutive</div>
            </div>

            <div class="nav-section">
                <h3>Navigation</h3>
                <div class="nav-item" onclick="window.location.href='/'">
                    <i class="fas fa-home"></i>
                    <span>🏠 Accueil</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-comments"></i>
                    <span>Chat Intelligent</span>
                </div>
                <div class="nav-item" onclick="window.location.href='/generation-studio.html'">
                    <i class="fas fa-magic"></i>
                    <span>Génération</span>
                </div>
                <div class="nav-item" onclick="window.location.href='/code-editor.html'">
                    <i class="fas fa-code"></i>
                    <span>Développement</span>
                </div>
                <div class="nav-item" onclick="window.location.href='/performance.html'">
                    <i class="fas fa-chart-line"></i>
                    <span>Analyse</span>
                </div>
            </div>

            <div class="nav-section">
                <h3>Système</h3>
                <div class="nav-item" onclick="window.location.href='/futuristic-interface.html'">
                    <i class="fas fa-brain"></i>
                    <span>Mémoire Thermique</span>
                </div>
                <div class="nav-item" onclick="window.location.href='/qi-neuron-monitor.html'">
                    <i class="fas fa-eye"></i>
                    <span>Monitoring</span>
                </div>
                <div class="nav-item" onclick="window.location.href='/agents.html'">
                    <i class="fas fa-users"></i>
                    <span>Agents</span>
                </div>
                <div class="nav-item" onclick="window.location.href='/settings.html'">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </div>
            </div>
        </div>

        <!-- Zone de chat centrale -->
        <div class="chat-area">
            <div class="chat-header">
                <div class="chat-title">
                    <i class="fas fa-comments"></i>
                    <h2>Chat Intelligent</h2>
                    <span class="subtitle">Vision Ultra - QI 120</span>
                </div>
                <div class="header-buttons">
                    <button class="header-btn home-btn" onclick="window.location.href='/'">
                        <i class="fas fa-home"></i> Accueil
                    </button>
                    <button class="header-btn">
                        <i class="fas fa-cog"></i> Options
                    </button>
                </div>
            </div>

            <div class="chat-messages">
                <div class="message">
                    <div class="message-avatar">L</div>
                    <div class="message-content">
                        <div class="message-header">
                            <span class="message-name">Louna</span>
                            <span class="message-time">Maintenant</span>
                        </div>
                        <div class="message-text">
                            Bonjour ! Je suis Vision Ultra, votre assistant cognitif créé par Jean Passave à Sainte-Anne, Guadeloupe.
                            Je suis équipé d'une mémoire thermique, d'accélérateurs Kyber, et de nombreuses fonctionnalités avancées.
                            Comment puis-je vous aider aujourd'hui ?
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-input-area">
                <div class="input-container">
                    <div class="input-actions">
                        <button class="input-btn">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button class="input-btn">
                            <i class="fas fa-camera"></i>
                        </button>
                        <button class="input-btn">
                            <i class="fas fa-paperclip"></i>
                        </button>
                    </div>
                    <input type="text" class="chat-input" placeholder="Tapez votre message ici...">
                    <button class="send-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Panneau droit -->
        <div class="right-panel">
            <div class="panel-section">
                <div class="panel-title">Statut Système</div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">120</div>
                        <div class="stat-label">QI</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">71</div>
                        <div class="stat-label">Neurones</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">42°C</div>
                        <div class="stat-label">Mémoire</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">84%</div>
                        <div class="stat-label">Efficacité</div>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">Actions Rapides</div>
                <div class="quick-actions">
                    <div class="quick-action" onclick="window.location.href='/futuristic-interface.html'">
                        <i class="fas fa-brain"></i>
                        <span>Mémoire</span>
                    </div>
                    <div class="quick-action" onclick="window.location.href='/brain-visualization.html'">
                        <i class="fas fa-eye"></i>
                        <span>3D Brain</span>
                    </div>
                    <div class="quick-action" onclick="window.location.href='/generation-studio.html'">
                        <i class="fas fa-magic"></i>
                        <span>Génération</span>
                    </div>
                    <div class="quick-action" onclick="window.location.href='/code-editor.html'">
                        <i class="fas fa-code"></i>
                        <span>Code</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">Activité Récente</div>
                <div class="activity-item">Optimisation mémoire thermique</div>
                <div class="activity-item">Accélérateur Kyber activé</div>
                <div class="activity-item">Cours ultra-avancé en cours</div>
                <div class="activity-item">Système AGI opérationnel</div>
            </div>
        </div>
    </div>

    <script>
        // Fonction pour actualiser les statistiques
        function updateStats() {
            // Simulation de données en temps réel
            const qiElement = document.querySelector('.stat-value');
            if (qiElement) {
                const currentQI = parseInt(qiElement.textContent);
                qiElement.textContent = Math.max(100, currentQI + Math.floor(Math.random() * 5 - 2));
            }
        }

        // Actualiser les stats toutes les 5 secondes
        setInterval(updateStats, 5000);

        // Ajouter des effets visuels aux éléments de navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(10px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });

        console.log('🚀 Interface Chat Hub Central chargée avec navigation vers l\'accueil');
    </script>
</body>
</html>
