<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Unification - Louna</title>
    <style>
        /* Styles de base pour tester l'unification */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-button {
            background: #e91e63;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #ad1457;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-success { background: #4caf50; }
        .status-warning { background: #ff9800; }
        .status-error { background: #f44336; }
        
        .test-results {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test d'Unification des Interfaces Louna</h1>
        <p>Cette page teste l'application automatique du thème unifié et de la navigation.</p>
        
        <div class="test-section">
            <h2>📊 État de l'Unification</h2>
            <div id="unification-status">
                <p><span class="status-indicator status-warning"></span>Vérification en cours...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎨 Éléments de Test</h2>
            <p>Ces éléments devraient être automatiquement stylisés par le thème unifié :</p>
            
            <div class="test-grid">
                <div class="test-card">
                    <h3>Carte de Test 1</h3>
                    <p>Cette carte devrait être transformée en louna-card</p>
                    <button class="test-button">Bouton Test</button>
                </div>
                
                <div class="test-card">
                    <h3>Carte de Test 2</h3>
                    <p>Navigation et styles unifiés appliqués automatiquement</p>
                    <button class="test-button">Autre Bouton</button>
                </div>
                
                <div class="test-card">
                    <h3>Carte de Test 3</h3>
                    <p>Vérification de la cohérence visuelle</p>
                    <button class="test-button">Troisième Bouton</button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Tests Fonctionnels</h2>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="test-button" onclick="testNavigation()">Test Navigation</button>
                <button class="test-button" onclick="testTheme()">Test Thème</button>
                <button class="test-button" onclick="testResponsive()">Test Responsive</button>
                <button class="test-button" onclick="testAnimations()">Test Animations</button>
                <button class="test-button" onclick="runAllTests()">Tous les Tests</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 Résultats des Tests</h2>
            <div class="test-results" id="test-results">
                <p>Aucun test exécuté pour le moment.</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔗 Navigation de Test</h2>
            <p>Testez la navigation vers d'autres interfaces :</p>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="/futuristic-interface.html" class="test-button" style="text-decoration: none;">Mémoire Thermique</a>
                <a href="/brain-visualization.html" class="test-button" style="text-decoration: none;">Cerveau 3D</a>
                <a href="/qi-neuron-monitor.html" class="test-button" style="text-decoration: none;">QI Monitor</a>
                <a href="/generation-studio.html" class="test-button" style="text-decoration: none;">Studio</a>
                <a href="/unified-hub.html" class="test-button" style="text-decoration: none;">Hub Central</a>
                <a href="/navigation" class="test-button" style="text-decoration: none;">Navigation Complète</a>
            </div>
        </div>
    </div>

    <script>
        // Variables globales pour les tests
        let testResults = [];
        
        // Fonction pour logger les résultats
        function logResult(test, status, message) {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({
                test,
                status,
                message,
                timestamp
            });
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = testResults.map(result => {
                const statusIcon = result.status === 'success' ? '✅' : 
                                 result.status === 'warning' ? '⚠️' : '❌';
                return `<p>[${result.timestamp}] ${statusIcon} ${result.test}: ${result.message}</p>`;
            }).join('');
        }
        
        // Test de la navigation unifiée
        function testNavigation() {
            logResult('Navigation', 'info', 'Vérification de la navigation unifiée...');
            
            setTimeout(() => {
                const nav = document.querySelector('.louna-nav');
                const navItems = document.querySelectorAll('.nav-item');
                const homeBtn = document.querySelector('.nav-home-btn');
                
                if (nav) {
                    logResult('Navigation', 'success', `Navigation unifiée trouvée avec ${navItems.length} éléments`);
                } else {
                    logResult('Navigation', 'error', 'Navigation unifiée non trouvée');
                }
                
                if (homeBtn) {
                    logResult('Navigation', 'success', 'Bouton d\'accueil présent');
                } else {
                    logResult('Navigation', 'warning', 'Bouton d\'accueil manquant');
                }
            }, 1000);
        }
        
        // Test du thème unifié
        function testTheme() {
            logResult('Thème', 'info', 'Vérification du thème unifié...');
            
            setTimeout(() => {
                const unifiedCSS = document.querySelector('link[href*="louna-unified-theme"]');
                const bodyClasses = document.body.classList;
                
                if (unifiedCSS) {
                    logResult('Thème', 'success', 'CSS unifié chargé');
                } else {
                    logResult('Thème', 'error', 'CSS unifié non trouvé');
                }
                
                if (bodyClasses.contains('louna-unified')) {
                    logResult('Thème', 'success', 'Classes unifiées appliquées');
                } else {
                    logResult('Thème', 'warning', 'Classes unifiées non appliquées');
                }
                
                // Vérifier les cartes transformées
                const lounaCards = document.querySelectorAll('.louna-card');
                if (lounaCards.length > 0) {
                    logResult('Thème', 'success', `${lounaCards.length} cartes transformées en louna-card`);
                } else {
                    logResult('Thème', 'warning', 'Aucune carte transformée trouvée');
                }
            }, 1500);
        }
        
        // Test responsive
        function testResponsive() {
            logResult('Responsive', 'info', 'Test de la responsivité...');
            
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            logResult('Responsive', 'info', `Résolution: ${width}x${height}`);
            
            if (width < 768) {
                logResult('Responsive', 'success', 'Mode mobile détecté');
            } else if (width < 1200) {
                logResult('Responsive', 'success', 'Mode tablette détecté');
            } else {
                logResult('Responsive', 'success', 'Mode desktop détecté');
            }
        }
        
        // Test des animations
        function testAnimations() {
            logResult('Animations', 'info', 'Test des animations...');
            
            setTimeout(() => {
                const animatedElements = document.querySelectorAll('.fade-in-up, [style*="transition"]');
                
                if (animatedElements.length > 0) {
                    logResult('Animations', 'success', `${animatedElements.length} éléments animés trouvés`);
                } else {
                    logResult('Animations', 'warning', 'Aucune animation détectée');
                }
            }, 1000);
        }
        
        // Exécuter tous les tests
        function runAllTests() {
            testResults = [];
            logResult('Tests', 'info', 'Démarrage de tous les tests...');
            
            testNavigation();
            setTimeout(() => testTheme(), 500);
            setTimeout(() => testResponsive(), 1000);
            setTimeout(() => testAnimations(), 1500);
            
            setTimeout(() => {
                const successCount = testResults.filter(r => r.status === 'success').length;
                const totalTests = testResults.length;
                logResult('Tests', 'info', `Tests terminés: ${successCount}/${totalTests} réussis`);
            }, 3000);
        }
        
        // Vérification automatique au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Page de test d\'unification chargée');
            
            // Vérifier l'état de l'unification après un délai
            setTimeout(() => {
                const statusDiv = document.getElementById('unification-status');
                const isUnified = document.body.classList.contains('louna-unified');
                const hasNav = document.querySelector('.louna-nav');
                
                if (isUnified && hasNav) {
                    statusDiv.innerHTML = '<p><span class="status-indicator status-success"></span>Unification réussie !</p>';
                } else if (hasNav) {
                    statusDiv.innerHTML = '<p><span class="status-indicator status-warning"></span>Unification partielle</p>';
                } else {
                    statusDiv.innerHTML = '<p><span class="status-indicator status-error"></span>Unification échouée</p>';
                }
                
                // Lancer automatiquement tous les tests
                runAllTests();
            }, 2000);
        });
    </script>
</body>
</html>
