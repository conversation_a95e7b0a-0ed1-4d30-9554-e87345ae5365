#!/bin/bash

# =============================================================================
# SCRIPT DE DÉMARRAGE OPTIMISÉ LOUNA - GRAND MÉNAGE TERMINÉ
# =============================================================================
# Créé après le grand ménage complet de l'application
# Toutes les pages problématiques et doublons ont été supprimés
# =============================================================================

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Fonction d'affichage stylé
print_header() {
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${WHITE}                    🚀 LOUNA - DÉMARRAGE OPTIMISÉ 🚀                        ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${CYAN}                     Application nettoyée et optimisée                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_step() {
    echo -e "${BLUE}▶${NC} ${WHITE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅${NC} ${WHITE}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} ${WHITE}$1${NC}"
}

print_error() {
    echo -e "${RED}❌${NC} ${WHITE}$1${NC}"
}

# Nettoyer les processus existants
cleanup_processes() {
    print_step "Nettoyage des processus existants..."

    # Tuer les processus Node.js sur les ports utilisés
    PORTS=(3000 3005 8080)

    for PORT in "${PORTS[@]}"; do
        PID=$(lsof -ti:$PORT 2>/dev/null)
        if [ ! -z "$PID" ]; then
            print_warning "Arrêt du processus sur le port $PORT (PID: $PID)"
            kill -9 $PID 2>/dev/null
            sleep 1
        fi
    done

    print_success "Nettoyage terminé"
}

# Optimiser la mémoire
optimize_memory() {
    print_step "Optimisation de la mémoire..."

    # Variables d'environnement pour optimiser Node.js
    export NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"
    export UV_THREADPOOL_SIZE=16

    print_success "Mémoire optimisée"
}

# Démarrer le serveur principal
start_server() {
    print_step "Démarrage du serveur Louna optimisé..."

    # Créer le dossier de logs s'il n'existe pas
    mkdir -p logs

    echo -e "${CYAN}🚀 Lancement de Louna sur http://localhost:3000${NC}"
    echo -e "${CYAN}📱 Interface principale: http://localhost:3000/louna${NC}"
    echo -e "${CYAN}💬 Chat intelligent: http://localhost:3000/chat${NC}"
    echo -e "${CYAN}🧠 Mémoire thermique: http://localhost:3000/futuristic-interface.html${NC}"
    echo ""

    # Démarrer en arrière-plan
    nohup node server.js > logs/louna-$(date +%Y%m%d_%H%M%S).log 2>&1 &
    SERVER_PID=$!

    # Attendre que le serveur démarre
    sleep 3

    # Vérifier si le serveur fonctionne
    if kill -0 $SERVER_PID 2>/dev/null; then
        print_success "Serveur démarré avec succès (PID: $SERVER_PID)"
        return 0
    else
        print_error "Échec du démarrage du serveur"
        return 1
    fi
}

# Ouvrir l'application
open_application() {
    print_step "Ouverture de l'application..."

    echo "Quelle interface souhaitez-vous ouvrir ?"
    echo "1) 🏠 Page d'accueil (Mémoire Thermique)"
    echo "2) 💬 Chat intelligent"
    echo "3) 🧠 Visualisation cerveau 3D"
    echo "4) ⚡ Accélérateurs Kyber"
    echo "5) 🎨 Studio de génération"
    echo "6) 📊 Monitoring QI"
    echo "7) 🤖 Gestion des agents"
    echo ""
    read -p "Votre choix (1-7, ou Entrée pour accueil) : " -n 1 -r
    echo ""

    case $REPLY in
        1|"")
            open "http://localhost:3000/"
            ;;
        2)
            open "http://localhost:3000/chat"
            ;;
        3)
            open "http://localhost:3000/brain-visualization.html"
            ;;
        4)
            open "http://localhost:3000/kyber-dashboard.html"
            ;;
        5)
            open "http://localhost:3000/generation-studio.html"
            ;;
        6)
            open "http://localhost:3000/qi-neuron-monitor.html"
            ;;
        7)
            open "http://localhost:3000/agents.html"
            ;;
        *)
            open "http://localhost:3000/"
            ;;
    esac

    print_success "Application ouverte dans le navigateur"
}

# Afficher les informations de démarrage
show_startup_info() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${WHITE}                           🎯 GRAND MÉNAGE TERMINÉ 🎯                        ${PURPLE}║${NC}"
    echo -e "${PURPLE}╠══════════════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Page problématique avec sidebar supprimée                               ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Toutes les routes en double nettoyées                                   ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Fichiers de chat doublons supprimés                                     ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Fichiers de backup et anciens supprimés                                 ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Routes unifiées et optimisées                                           ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Navigation simplifiée et fonctionnelle                                  ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Fonction principale
main() {
    clear
    print_header
    show_startup_info

    # Optimisations
    cleanup_processes
    optimize_memory

    # Démarrage
    if start_server; then
        open_application

        echo ""
        print_success "🎉 Louna est maintenant opérationnel !"
        echo -e "${CYAN}📝 Logs disponibles dans le dossier 'logs/'${NC}"
        echo -e "${CYAN}🔄 Pour redémarrer: ./start-louna.sh${NC}"
        echo -e "${CYAN}🛑 Pour arrêter: Ctrl+C ou kill les processus Node.js${NC}"
    else
        print_error "Échec du démarrage de Louna"
        exit 1
    fi
}

# Gestion des signaux pour arrêt propre
trap 'echo -e "\n${YELLOW}Arrêt de Louna...${NC}"; cleanup_processes; exit 0' INT TERM

# Exécution
main "$@"
