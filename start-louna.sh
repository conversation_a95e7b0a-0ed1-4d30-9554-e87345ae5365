#!/bin/bash

# Script pour démarrer l'application Louna
# Ce script démarre le serveur et ouvre l'application dans le navigateur

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██╗      ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██║     ██╔═══██╗██║   ██║████╗  ██║██╔══██╗"
echo "██║     ██║   ██║██║   ██║██╔██╗ ██║███████║"
echo "██║     ██║   ██║██║   ██║██║╚██╗██║██╔══██║"
echo "███████╗╚██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo "╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Démarrage de l'application Louna${NC}"
echo ""
print_message "Début du démarrage de l'application Louna..."
sleep 1

# Étape 1 : Vérifier si le serveur est déjà en cours d'exécution
print_message "Étape 1 : Vérification si le serveur est déjà en cours d'exécution..."

if lsof -i:3000 -t &> /dev/null; then
  print_message "Le serveur est déjà en cours d'exécution."

  # Demander à l'utilisateur s'il souhaite redémarrer le serveur
  read -p "Souhaitez-vous redémarrer le serveur ? (o/n) " -n 1 -r
  echo ""

  if [[ $REPLY =~ ^[Oo]$ ]]; then
    print_message "Arrêt du serveur en cours..."
    kill -9 $(lsof -i:3000 -t) 2>/dev/null
    sleep 2
    print_success "Serveur arrêté."
  else
    print_message "Le serveur continue de fonctionner."
  fi
fi

# Étape 2 : Démarrer les serveurs
print_message "Étape 2 : Démarrage des serveurs..."

cd "$APP_DIR" || exit

# Lancer le serveur principal en arrière-plan
nohup node server.js > /dev/null 2>&1 &

# Lancer le serveur Louna en arrière-plan
nohup node server-louna.js > /dev/null 2>&1 &

# Attendre que les serveurs démarrent
sleep 5

print_success "Serveurs démarrés."
print_message "Serveur principal: http://localhost:3000"
print_message "Serveur Louna: http://localhost:3005/louna"

# Étape 3 : Ouvrir l'application dans le navigateur
print_message "Étape 3 : Ouverture de l'application dans le navigateur..."

# Demander à l'utilisateur quelle page il souhaite ouvrir
echo "Quelle page souhaitez-vous ouvrir ?"
echo "1) Accueil"
echo "2) Chat"
echo "3) Présentation"
echo "4) Prompts"
echo "5) Mémoire"
echo "6) Thermique"
read -p "Votre choix (1-6) : " -n 1 -r
echo ""

case $REPLY in
  1)
    open "http://localhost:3005/louna"
    ;;
  2)
    open "http://localhost:3005/louna/chat"
    ;;
  3)
    open "http://localhost:3005/louna/presentation"
    ;;
  4)
    open "http://localhost:3005/louna/prompts"
    ;;
  5)
    open "http://localhost:3005/louna/memory"
    ;;
  6)
    open "http://localhost:3000/thermal-memory.html"
    ;;
  *)
    open "http://localhost:3005/louna"
    ;;
esac

print_success "Application Louna démarrée !"
print_message "Toutes les interfaces ont le même aspect visuel et sont bien connectées les unes aux autres."
