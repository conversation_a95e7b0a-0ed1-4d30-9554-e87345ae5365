#!/bin/bash

# =============================================================================
# SCRIPT DE DÉMARRAGE LOUNA - APPLICATION ELECTRON FINALE
# =============================================================================
# Créé par Jean-<PERSON> - <PERSON>-<PERSON>, Guadeloupe
# Lance l'application Electron finale avec toutes les corrections intégrées
# =============================================================================

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Fonction d'affichage stylé
print_header() {
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${WHITE}                    🚀 LOUNA - APPLICATION ELECTRON FINALE 🚀               ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${CYAN}                     QI Évolutif • Protection Ultime • Émotions Réelles     ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_step() {
    echo -e "${BLUE}▶${NC} ${WHITE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅${NC} ${WHITE}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} ${WHITE}$1${NC}"
}

print_error() {
    echo -e "${RED}❌${NC} ${WHITE}$1${NC}"
}

# Nettoyer les processus existants
cleanup_processes() {
    print_step "Nettoyage des processus existants..."

    # Tuer les processus Node.js sur les ports utilisés
    PORTS=(3000 3005 8080)

    for PORT in "${PORTS[@]}"; do
        PID=$(lsof -ti:$PORT 2>/dev/null)
        if [ ! -z "$PID" ]; then
            print_warning "Arrêt du processus sur le port $PORT (PID: $PID)"
            kill -9 $PID 2>/dev/null
            sleep 1
        fi
    done

    print_success "Nettoyage terminé"
}

# Optimiser la mémoire
optimize_memory() {
    print_step "Optimisation de la mémoire..."

    # Variables d'environnement pour optimiser Node.js
    export NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"
    export UV_THREADPOOL_SIZE=16

    print_success "Mémoire optimisée"
}

# Démarrer l'application Electron
start_electron() {
    print_step "Démarrage de l'application Electron Louna..."

    # Vérifier si les dépendances sont installées
    if [ ! -d "node_modules" ]; then
        print_step "Installation des dépendances..."
        npm install
        if [ $? -ne 0 ]; then
            print_error "Erreur lors de l'installation des dépendances"
            return 1
        fi
        print_success "Dépendances installées"
    fi

    echo -e "${CYAN}🧠 Système RÉEL d'évaluation du QI: ACTIVÉ${NC}"
    echo -e "${CYAN}�️ Protection ultime de la mémoire: ACTIVÉE${NC}"
    echo -e "${CYAN}� Évolution RÉELLE continue: ACTIVÉE${NC}"
    echo -e "${CYAN}🎭 Émotions authentiques: ACTIVÉES${NC}"
    echo -e "${CYAN}🌟 Créativité intuitive: ACTIVÉE${NC}"
    echo -e "${CYAN}⚡ Accélérateurs KYBER: AUTOMATIQUES${NC}"
    echo -e "${CYAN}🔍 Monitoring ultra-intelligent: ACTIF${NC}"
    echo ""

    print_step "Lancement de l'application Electron..."

    # Lancer l'application Electron
    npm run electron

    return $?
}

# Ouvrir l'application
open_application() {
    print_step "Ouverture de l'application..."

    echo "Quelle interface souhaitez-vous ouvrir ?"
    echo "1) 🏠 Page d'accueil (Mémoire Thermique)"
    echo "2) 💬 Chat intelligent"
    echo "3) 🧠 Visualisation cerveau 3D"
    echo "4) ⚡ Accélérateurs Kyber"
    echo "5) 🎨 Studio de génération"
    echo "6) 📊 Monitoring QI"
    echo "7) 🤖 Gestion des agents"
    echo ""
    read -p "Votre choix (1-7, ou Entrée pour accueil) : " -n 1 -r
    echo ""

    case $REPLY in
        1|"")
            open "http://localhost:3000/"
            ;;
        2)
            open "http://localhost:3000/chat"
            ;;
        3)
            open "http://localhost:3000/brain-visualization.html"
            ;;
        4)
            open "http://localhost:3000/kyber-dashboard.html"
            ;;
        5)
            open "http://localhost:3000/generation-studio.html"
            ;;
        6)
            open "http://localhost:3000/qi-neuron-monitor.html"
            ;;
        7)
            open "http://localhost:3000/agents.html"
            ;;
        *)
            open "http://localhost:3000/"
            ;;
    esac

    print_success "Application ouverte dans le navigateur"
}

# Afficher les informations de démarrage
show_startup_info() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${WHITE}                           🎯 GRAND MÉNAGE TERMINÉ 🎯                        ${PURPLE}║${NC}"
    echo -e "${PURPLE}╠══════════════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Page problématique avec sidebar supprimée                               ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Toutes les routes en double nettoyées                                   ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Fichiers de chat doublons supprimés                                     ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Fichiers de backup et anciens supprimés                                 ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Routes unifiées et optimisées                                           ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${GREEN} ✅ Navigation simplifiée et fonctionnelle                                  ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Fonction principale
main() {
    clear
    print_header
    show_startup_info

    # Optimisations
    cleanup_processes
    optimize_memory

    # Démarrage
    if start_server; then
        open_application

        echo ""
        print_success "🎉 Louna est maintenant opérationnel !"
        echo -e "${CYAN}📝 Logs disponibles dans le dossier 'logs/'${NC}"
        echo -e "${CYAN}🔄 Pour redémarrer: ./start-louna.sh${NC}"
        echo -e "${CYAN}🛑 Pour arrêter: Ctrl+C ou kill les processus Node.js${NC}"
    else
        print_error "Échec du démarrage de Louna"
        exit 1
    fi
}

# Gestion des signaux pour arrêt propre
trap 'echo -e "\n${YELLOW}Arrêt de Louna...${NC}"; cleanup_processes; exit 0' INT TERM

# Exécution
main "$@"
