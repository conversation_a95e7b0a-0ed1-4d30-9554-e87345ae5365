#!/bin/bash

# =============================================================================
# RACCOURCI BUREAU POUR LOUNA - APPLICATION ELECTRON FINALE
# =============================================================================
# Créé par Jean-<PERSON>ave - Sainte-Anne, Guadeloupe
# Double-cliquez sur ce fichier pour lancer Louna
# =============================================================================

# Couleurs pour l'affichage
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Affichage de démarrage
echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║${WHITE}                    🚀 LOUNA - APPLICATION ELECTRON FINALE 🚀               ${PURPLE}║${NC}"
echo -e "${PURPLE}║${CYAN}                     Démarrage depuis le bureau de Jean-Luc                  ${PURPLE}║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Aller dans le répertoire de l'application
APP_DIR="/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933"

echo -e "${BLUE}📂 Navigation vers le répertoire de l'application...${NC}"
cd "$APP_DIR"

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Erreur: Impossible d'accéder au répertoire de l'application${NC}"
    echo -e "${RED}   Répertoire: $APP_DIR${NC}"
    echo ""
    echo "Appuyez sur Entrée pour fermer..."
    read
    exit 1
fi

echo -e "${GREEN}✅ Répertoire trouvé: $APP_DIR${NC}"
echo ""

# Vérifier si le script de démarrage existe
if [ ! -f "start-louna.sh" ]; then
    echo -e "${RED}❌ Erreur: Script de démarrage non trouvé${NC}"
    echo ""
    echo "Appuyez sur Entrée pour fermer..."
    read
    exit 1
fi

echo -e "${GREEN}✅ Script de démarrage trouvé${NC}"
echo ""

# Lancer le script de démarrage
echo -e "${CYAN}🚀 Lancement de Louna...${NC}"
echo ""

# Exécuter le script de démarrage
./start-louna.sh

# Attendre avant de fermer
echo ""
echo "Appuyez sur Entrée pour fermer cette fenêtre..."
read
