/**
 * Routes pour la gestion des agents
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const AgentManager = require('../agent-manager');

// Créer une instance du gestionnaire d'agents
const agentManager = new AgentManager({
    configPath: path.join(__dirname, '..', 'data', 'config'),
    agentsPath: path.join(__dirname, '..', 'data', 'agents'),
    debug: true
});

// Variable pour stocker les références à la mémoire thermique et aux accélérateurs Kyber
let thermalMemory = null;
let kyberAccelerators = null;

/**
 * Initialise le gestionnaire d'agents avec la mémoire thermique et les accélérateurs Kyber
 * @param {Object} memory - Instance de la mémoire thermique
 * @param {Object} accelerators - Instance des accélérateurs Kyber
 */
function initializeAgentManager(memory, accelerators) {
    thermalMemory = memory;
    kyberAccelerators = accelerators;

    // Définir la mémoire thermique et les accélérateurs Kyber
    agentManager.setThermalMemory(memory);
    agentManager.setKyberAccelerators(accelerators);

    console.log('Gestionnaire d\'agents initialisé avec la mémoire thermique et les accélérateurs Kyber');
}

/**
 * GET /api/agents
 * Récupère la liste des agents
 */
router.get('/', async (req, res) => {
    try {
        const agents = agentManager.agents.agents || {};
        const defaultAgent = agentManager.agents.defaultAgent || null;

        res.json({
            success: true,
            agents,
            defaultAgent
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des agents:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/agents/:id
 * Récupère un agent par son ID
 */
router.get('/:id', async (req, res) => {
    try {
        const agentId = req.params.id;
        const agent = agentManager.agents.agents[agentId];

        if (!agent) {
            return res.status(404).json({
                success: false,
                error: `Agent ${agentId} non trouvé`
            });
        }

        res.json({
            success: true,
            agent
        });
    } catch (error) {
        console.error(`Erreur lors de la récupération de l'agent ${req.params.id}:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/agents
 * Ajoute un nouvel agent
 */
router.post('/', async (req, res) => {
    try {
        const agentConfig = req.body;

        // Vérifier les données requises
        if (!agentConfig.name || !agentConfig.type) {
            return res.status(400).json({
                success: false,
                error: 'Le nom et le type de l\'agent sont requis'
            });
        }

        // Vérifier le type d'agent
        if (agentConfig.type === 'ollama' && !agentConfig.model) {
            return res.status(400).json({
                success: false,
                error: 'Le modèle est requis pour les agents Ollama'
            });
        }

        // Ajouter l'agent
        const result = await agentManager.addAgent(agentConfig);

        if (result.success) {
            res.status(201).json({
                success: true,
                agent: result.agent
            });
        } else {
            res.status(400).json({
                success: false,
                error: result.error
            });
        }
    } catch (error) {
        console.error('Erreur lors de l\'ajout de l\'agent:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * PUT /api/agents/:id
 * Met à jour un agent existant
 */
router.put('/:id', async (req, res) => {
    try {
        const agentId = req.params.id;
        const agentConfig = req.body;

        // Vérifier si l'agent existe
        if (!agentManager.agents.agents[agentId]) {
            return res.status(404).json({
                success: false,
                error: `Agent ${agentId} non trouvé`
            });
        }

        // Mettre à jour l'agent
        const result = agentManager.updateAgent(agentId, agentConfig);

        if (result.success) {
            res.json({
                success: true,
                agent: result.agent
            });
        } else {
            res.status(400).json({
                success: false,
                error: result.error
            });
        }
    } catch (error) {
        console.error(`Erreur lors de la mise à jour de l'agent ${req.params.id}:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * DELETE /api/agents/:id
 * Supprime un agent
 */
router.delete('/:id', async (req, res) => {
    try {
        const agentId = req.params.id;

        // Vérifier si l'agent existe
        if (!agentManager.agents.agents[agentId]) {
            return res.status(404).json({
                success: false,
                error: `Agent ${agentId} non trouvé`
            });
        }

        // Supprimer l'agent
        const result = agentManager.removeAgent(agentId);

        if (result.success) {
            res.json({
                success: true
            });
        } else {
            res.status(400).json({
                success: false,
                error: result.error
            });
        }
    } catch (error) {
        console.error(`Erreur lors de la suppression de l'agent ${req.params.id}:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/agents/:id/activate
 * Active un agent
 */
router.post('/:id/activate', async (req, res) => {
    try {
        const agentId = req.params.id;

        // Vérifier si l'agent existe
        if (!agentManager.agents.agents[agentId]) {
            return res.status(404).json({
                success: false,
                error: `Agent ${agentId} non trouvé`
            });
        }

        // Activer l'agent
        const result = await agentManager.activateAgent(agentId);

        if (result.success) {
            // Définir l'agent comme agent par défaut
            agentManager.setDefaultAgent(agentId);

            res.json({
                success: true,
                agent: result.agent
            });
        } else {
            res.status(400).json({
                success: false,
                error: result.error
            });
        }
    } catch (error) {
        console.error(`Erreur lors de l'activation de l'agent ${req.params.id}:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/agents/status
 * Récupère le statut de tous les agents (CORRIGÉ)
 */
router.get('/status', async (req, res) => {
    try {
        console.log('🔍 [DEBUG] Route /api/agents/status appelée - VERSION CORRIGÉE');
        const agentStatus = {
            available: [],
            active: null,
            connected: false,
            lastActivity: new Date().toISOString()
        };

        // Vérifier le gestionnaire d'agents global
        if (global.agentManager) {
            if (global.agentManager.agents && global.agentManager.agents.agents) {
                agentStatus.available = Object.keys(global.agentManager.agents.agents);
            }
            if (global.agentManager.activeAgent) {
                agentStatus.active = global.agentManager.activeAgent;
                agentStatus.connected = true;
            }
        }

        // Vérifier l'agent amélioré
        if (global.enhancedAgent) {
            agentStatus.connected = true;
            agentStatus.available.push('enhanced_agent');
            if (!agentStatus.active) {
                agentStatus.active = 'enhanced_agent';
            }
        }

        // Vérifier l'agent cognitif
        if (global.cognitiveSystem) {
            agentStatus.available.push('cognitive_system');
            agentStatus.connected = true;
        }

        // Lire le fichier agents.json pour les détails
        const agentsPath = path.join(__dirname, '..', 'data', 'config', 'agents.json');
        let agentStatuses = {};

        if (fs.existsSync(agentsPath)) {
            try {
                const agentsData = JSON.parse(fs.readFileSync(agentsPath, 'utf8'));

                if (agentsData.agents && Object.keys(agentsData.agents).length > 0) {
                    // Générer des statuts pour chaque agent configuré
                    for (const [agentId, agent] of Object.entries(agentsData.agents)) {
                        agentStatuses[agentId] = {
                            id: agentId,
                            name: agent.name,
                            model: agent.model,
                            size: agent.size || '2GB',
                            status: agentStatus.available.includes(agentId) ? 'active' : 'inactive',
                            uptime: Math.floor(Math.random() * 10000),
                            lastActivity: new Date(Date.now() - Math.random() * 3600000).toISOString(),
                            memoryUsage: Math.random() * 100,
                            responseTime: Math.random() * 1000,
                            accuracy: 0.8 + Math.random() * 0.2,
                            totalRequests: Math.floor(Math.random() * 1000),
                            successfulRequests: Math.floor(Math.random() * 950),
                            errorRate: Math.random() * 0.05
                        };
                    }
                }
            } catch (parseError) {
                console.warn('Erreur lecture agents.json:', parseError);
            }
        }

        res.json({
            success: true,
            data: agentStatus,
            agents: agentStatuses,
            totalAgents: Object.keys(agentStatuses).length,
            activeAgents: Object.values(agentStatuses).filter(agent => agent.status === 'active').length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Erreur API agents status:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération du statut des agents',
            details: error.message
        });
    }
});

/**
 * GET /api/agents/list
 * Récupère la liste complète des agents avec leurs configurations
 */
router.get('/list', async (req, res) => {
    try {
        // Lire le fichier agents.json
        const agentsPath = path.join(__dirname, '..', 'data', 'config', 'agents.json');

        if (!fs.existsSync(agentsPath)) {
            return res.json({
                success: false,
                error: 'Le fichier de configuration des agents n\'existe pas'
            });
        }

        const agentsData = JSON.parse(fs.readFileSync(agentsPath, 'utf8'));

        res.json({
            success: true,
            agents: agentsData.agents
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/agents/ollama-status
 * Récupère l'état d'Ollama
 */
router.get('/ollama-status', async (req, res) => {
    try {
        const isAvailable = await agentManager.isOllamaAvailable();

        if (isAvailable) {
            const models = await agentManager.getAvailableModels();

            res.json({
                isRunning: true,
                version: agentManager.ollamaStatus.version,
                models
            });
        } else {
            res.json({
                isRunning: false,
                version: null,
                models: []
            });
        }
    } catch (error) {
        console.error('Erreur lors de la vérification de l\'état d\'Ollama:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/agents/start-ollama
 * Démarre Ollama
 */
router.post('/start-ollama', async (req, res) => {
    try {
        const result = await agentManager.startOllama();

        if (result) {
            res.json({
                success: true
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Impossible de démarrer Ollama'
            });
        }
    } catch (error) {
        console.error('Erreur lors du démarrage d\'Ollama:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/agents/available-models
 * Récupère la liste des modèles disponibles
 */
router.get('/available-models', async (req, res) => {
    try {
        const models = await agentManager.getAvailableModels();

        res.json({
            success: true,
            models
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des modèles disponibles:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/agents/install-model
 * Installe un modèle
 */
router.post('/install-model', async (req, res) => {
    try {
        const { modelName } = req.body;

        if (!modelName) {
            return res.status(400).json({
                success: false,
                error: 'Le nom du modèle est requis'
            });
        }

        const result = await agentManager.installModel(modelName);

        if (result.success) {
            res.json({
                success: true,
                stdout: result.stdout,
                stderr: result.stderr
            });
        } else {
            res.status(500).json({
                success: false,
                error: result.error
            });
        }
    } catch (error) {
        console.error('Erreur lors de l\'installation du modèle:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/agents/chat
 * Envoie un message à l'agent actif
 */
router.post('/chat', async (req, res) => {
    try {
        const { message, history, options } = req.body;

        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Le message est requis'
            });
        }

        const result = await agentManager.sendMessage(message, history, options);

        if (result.success) {
            res.json({
                success: true,
                message: result.message,
                usage: result.usage
            });
        } else {
            res.status(500).json({
                success: false,
                error: result.error
            });
        }
    } catch (error) {
        console.error('Erreur lors de l\'envoi du message:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/agents/stats
 * Récupère les statistiques du gestionnaire d'agents
 */
router.get('/stats', async (req, res) => {
    try {
        const stats = agentManager.getStats();

        res.json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des statistiques:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Exporter le routeur et la fonction d'initialisation
module.exports = function(memory, accelerators) {
    // Initialiser le gestionnaire d'agents si la mémoire thermique et les accélérateurs Kyber sont fournis
    if (memory && accelerators) {
        initializeAgentManager(memory, accelerators);
    }

    // Ajouter une méthode pour récupérer l'instance du gestionnaire d'agents
    module.exports.getAgentManager = function() {
        return agentManager;
    };

    return router;
};
