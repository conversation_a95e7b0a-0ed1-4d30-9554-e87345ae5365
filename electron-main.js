/**
 * Point d'entrée principal de l'application Electron pour Louna
 * Ce fichier gère la création de la fenêtre principale et l'intégration avec le système
 */

const { app, BrowserWindow, Tray, Menu, dialog, shell, ipcMain, nativeImage, Notification } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const os = require('os');
const { registerGlobalShortcuts, unregisterGlobalShortcuts } = require('./global-shortcuts');
const autoLaunch = require('./auto-launch');

// Référence globale à la fenêtre principale
let mainWindow;
// Référence globale au serveur Express
let serverProcess;
// Référence globale à l'icône de la barre des tâches
let tray;
// Port du serveur
const PORT = process.env.PORT || 3004; // Changé de 3003 à 3004 pour éviter les conflits
// Chemin vers l'icône de l'application
let iconPath;
try {
  // Essayer d'utiliser l'icône PNG
  iconPath = nativeImage.createFromPath(path.join(__dirname, 'public', 'img', 'louna-icon.png'));
  if (iconPath.isEmpty()) {
    throw new Error('Icône PNG vide');
  }
} catch (error) {
  console.warn('Erreur lors du chargement de l\'icône PNG, utilisation de l\'icône par défaut:', error);
  iconPath = nativeImage.createEmpty();
}
// Chemin vers le dossier de données
const dataDir = path.join(app.getPath('userData'), 'data');
// Chemin vers le dossier de sauvegarde
const backupDir = path.join(app.getPath('userData'), 'backups');

/**
 * Crée la fenêtre principale de l'application
 */
function createWindow() {
  // Créer la fenêtre du navigateur
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: iconPath,
    movable: true, // Permettre le déplacement de la fenêtre
    resizable: true, // Permettre le redimensionnement
    minimizable: true, // Permettre la minimisation
    maximizable: true, // Permettre la maximisation
    closable: true, // Permettre la fermeture
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false, // Nécessaire pour les permissions média
      allowRunningInsecureContent: true,
      experimentalFeatures: true
    },
    show: true, // Afficher la fenêtre immédiatement
    backgroundColor: '#1a1a1a', // Couleur de fond sombre
    titleBarStyle: 'default', // Style de barre de titre par défaut pour permettre le déplacement
    frame: true // Afficher le cadre de la fenêtre pour permettre le déplacement
  });

  // Charger l'URL de l'application - Page d'accueil Louna originale
  mainWindow.loadURL(`http://localhost:${PORT}/louna`);

  // Afficher la fenêtre une fois qu'elle est prête
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.focus();
    console.log('Fenêtre Louna affichée');
  });

  // Forcer l'affichage après un délai si nécessaire
  setTimeout(() => {
    if (mainWindow && !mainWindow.isVisible()) {
      console.log('Forçage de l\'affichage de la fenêtre');
      mainWindow.show();
      mainWindow.focus();
    }
  }, 3000);

  // Ouvrir les outils de développement en mode développement
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Gérer la fermeture de la fenêtre
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Intercepter les liens externes et les ouvrir dans le navigateur par défaut
  mainWindow.webContents.on('new-window', (event, url) => {
    event.preventDefault();
    shell.openExternal(url);
  });

  // Gérer les permissions pour caméra et micro
  mainWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
    const allowedPermissions = ['camera', 'microphone', 'display-capture', 'media'];

    if (allowedPermissions.includes(permission)) {
      console.log(`Permission accordée pour: ${permission}`);
      callback(true);
    } else {
      console.log(`Permission refusée pour: ${permission}`);
      callback(false);
    }
  });

  // Gérer les demandes de permissions média
  mainWindow.webContents.session.setPermissionCheckHandler((webContents, permission, requestingOrigin, details) => {
    const allowedPermissions = ['camera', 'microphone', 'display-capture', 'media'];
    return allowedPermissions.includes(permission);
  });

  // Créer le menu de l'application
  createMenu();
}

/**
 * Crée le menu de l'application
 */
function createMenu() {
  const template = [
    {
      label: 'Fichier',
      submenu: [
        {
          label: 'Nouvelle fenêtre',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            createWindow();
          }
        },
        { type: 'separator' },
        {
          label: 'Sauvegarder la mémoire',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            backupMemory();
          }
        },
        {
          label: 'Restaurer la mémoire',
          click: () => {
            restoreMemory();
          }
        },
        { type: 'separator' },
        {
          label: 'Quitter',
          accelerator: 'CmdOrCtrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Édition',
      submenu: [
        { role: 'undo', label: 'Annuler' },
        { role: 'redo', label: 'Rétablir' },
        { type: 'separator' },
        { role: 'cut', label: 'Couper' },
        { role: 'copy', label: 'Copier' },
        { role: 'paste', label: 'Coller' },
        { role: 'delete', label: 'Supprimer' },
        { type: 'separator' },
        { role: 'selectAll', label: 'Tout sélectionner' }
      ]
    },
    {
      label: 'Affichage',
      submenu: [
        { role: 'reload', label: 'Actualiser' },
        { role: 'forceReload', label: 'Forcer l\'actualisation' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'Taille réelle' },
        { role: 'zoomIn', label: 'Zoom avant' },
        { role: 'zoomOut', label: 'Zoom arrière' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'Plein écran' }
      ]
    },
    {
      label: 'Outils',
      submenu: [
        {
          label: 'Mémoire thermique',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/thermal-memory.html`);
            }
          }
        },
        {
          label: 'Visualisation 3D du cerveau',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/brain-visualization.html`);
            }
          }
        },
        {
          label: 'Monitoring Qi & Neurones',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/qi-neuron-monitor.html`);
            }
          }
        },
        {
          label: 'LTX Video',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/ltx-video.html`);
            }
          }
        },
        {
          label: 'Éditeur de Code',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/code-editor.html`);
            }
          }
        },
        {
          label: 'Accélérateurs Kyber',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/kyber-dashboard.html`);
            }
          }
        },
        {
          label: 'Fusion de mémoire',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/memory-fusion.html`);
            }
          }
        },
        {
          label: 'Performances',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/performance.html`);
            }
          }
        },
        {
          label: 'Chat',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/chat`);
            }
          }
        },
        {
          label: 'Paramètres',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/settings`);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Outils de développement',
          accelerator: 'CmdOrCtrl+Shift+I',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.toggleDevTools();
            }
          }
        }
      ]
    },
    {
      label: 'Aide',
      submenu: [
        {
          label: 'À propos de Louna',
          click: () => {
            showAboutDialog();
          }
        },
        {
          label: 'Documentation',
          click: () => {
            shell.openExternal('https://github.com/votre-repo/louna-thermal-memory/wiki');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

/**
 * Crée l'icône dans la barre des tâches
 */
function createTray() {
  try {
    // Créer l'icône de la barre des tâches
    tray = new Tray(iconPath);
  } catch (error) {
    console.error('Erreur lors de la création de l\'icône de la barre des tâches:', error);
    // Utiliser une icône par défaut d'Electron
    try {
      tray = new Tray(nativeImage.createEmpty());
    } catch (err) {
      console.error('Impossible de créer une icône vide, abandon de la création de la barre des tâches:', err);
      return; // Sortir de la fonction si on ne peut pas créer d'icône
    }
  }

  // Définir le menu contextuel
  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Ouvrir Louna',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
        } else {
          createWindow();
        }
      }
    },
    { type: 'separator' },
    {
      label: 'Chat',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/chat`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/chat`);
        }
      }
    },
    {
      label: 'Mémoire thermique',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/thermal-memory.html`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/thermal-memory.html`);
        }
      }
    },
    {
      label: 'Visualisation 3D',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/brain-visualization.html`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/brain-visualization.html`);
        }
      }
    },
    {
      label: 'Monitoring Qi',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/qi-neuron-monitor.html`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/qi-neuron-monitor.html`);
        }
      }
    },
    { type: 'separator' },
    {
      label: 'Quitter',
      click: () => {
        app.quit();
      }
    }
  ]);

  // Définir le titre de l'infobulle
  tray.setToolTip('Louna - Agent avec mémoire thermique');

  // Définir le menu contextuel
  tray.setContextMenu(contextMenu);

  // Afficher la fenêtre au clic sur l'icône
  tray.on('click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
      }
    } else {
      createWindow();
    }
  });
}

/**
 * Démarre le serveur Express
 */
function startServer() {
  return new Promise((resolve, reject) => {
    // Démarrer le serveur Express
    serverProcess = spawn('node', ['server.js'], {
      stdio: 'pipe',
      env: { ...process.env, PORT }
    });

    let output = '';

    // Capturer la sortie standard
    serverProcess.stdout.on('data', (data) => {
      const chunk = data.toString();
      output += chunk;
      console.log(chunk);

      // Vérifier si le serveur est démarré
      if (chunk.includes(`Serveur démarré sur le port ${PORT}`) || chunk.includes(`Serveur Louna démarré sur le port ${PORT}`)) {
        console.log('Serveur détecté comme démarré');
        resolve();
      }
    });

    // Capturer la sortie d'erreur
    serverProcess.stderr.on('data', (data) => {
      const chunk = data.toString();
      console.error(chunk);
    });

    // Gérer la fermeture du processus
    serverProcess.on('close', (code) => {
      if (code !== 0) {
        console.error(`Le serveur s'est arrêté avec le code ${code}`);
        reject(new Error(`Le serveur s'est arrêté avec le code ${code}`));
      }
    });

    // Définir un délai d'attente
    setTimeout(() => {
      if (!output.includes(`Serveur Louna démarré sur le port ${PORT}`) && !output.includes(`Serveur démarré sur le port ${PORT}`)) {
        reject(new Error('Le serveur n\'a pas démarré dans le délai imparti'));
      }
    }, 15000); // Augmenté à 15 secondes
  });
}

/**
 * Affiche la boîte de dialogue "À propos"
 */
function showAboutDialog() {
  dialog.showMessageBox(mainWindow, {
    title: 'À propos de Louna',
    message: 'Louna - Agent avec mémoire thermique',
    detail: `Version: 1.0.0\nElectron: ${process.versions.electron}\nNode: ${process.versions.node}\nChrome: ${process.versions.chrome}\n\nCopyright © 2025`,
    icon: nativeImage.createFromPath(iconPath),
    buttons: ['OK']
  });
}

/**
 * Configure les fonctionnalités spécifiques à macOS
 */
function setupMacOSFeatures() {
  // Configurer le menu Dock
  const dockMenu = Menu.buildFromTemplate([
    {
      label: 'Nouvelle fenêtre',
      click: () => {
        createWindow();
      }
    },
    { type: 'separator' },
    {
      label: 'Chat',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/chat`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/chat`);
        }
      }
    },
    {
      label: 'Mémoire thermique',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/thermal-memory.html`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/thermal-memory.html`);
        }
      }
    },
    {
      label: 'Visualisation 3D',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/brain-visualization.html`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/brain-visualization.html`);
        }
      }
    },
    {
      label: 'Monitoring Qi',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/qi-neuron-monitor.html`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/qi-neuron-monitor.html`);
        }
      }
    },
    {
      label: 'Accélérateurs Kyber',
      click: () => {
        if (mainWindow) {
          mainWindow.loadURL(`http://localhost:${PORT}/kyber-dashboard.html`);
          mainWindow.show();
        } else {
          createWindow();
          mainWindow.loadURL(`http://localhost:${PORT}/kyber-dashboard.html`);
        }
      }
    }
  ]);

  app.dock.setMenu(dockMenu);

  // Configurer le menu de l'application
  const template = [
    {
      label: 'Louna',
      submenu: [
        { role: 'about', label: 'À propos de Louna' },
        { type: 'separator' },
        { role: 'services', label: 'Services' },
        { type: 'separator' },
        { role: 'hide', label: 'Masquer Louna' },
        { role: 'hideOthers', label: 'Masquer les autres' },
        { role: 'unhide', label: 'Tout afficher' },
        { type: 'separator' },
        { role: 'quit', label: 'Quitter Louna' }
      ]
    },
    {
      label: 'Fichier',
      submenu: [
        {
          label: 'Importer des données',
          accelerator: 'Cmd+I',
          click: () => {
            importData();
          }
        },
        {
          label: 'Exporter des données',
          accelerator: 'Cmd+E',
          click: () => {
            exportData();
          }
        },
        { type: 'separator' },
        {
          label: 'Fermer',
          accelerator: 'Cmd+W',
          role: 'close'
        }
      ]
    },
    {
      label: 'Édition',
      submenu: [
        { role: 'undo', label: 'Annuler' },
        { role: 'redo', label: 'Rétablir' },
        { type: 'separator' },
        { role: 'cut', label: 'Couper' },
        { role: 'copy', label: 'Copier' },
        { role: 'paste', label: 'Coller' },
        { role: 'selectAll', label: 'Tout sélectionner' }
      ]
    },
    {
      label: 'Affichage',
      submenu: [
        { role: 'reload', label: 'Actualiser' },
        { role: 'forceReload', label: 'Forcer l\'actualisation' },
        { role: 'toggleDevTools', label: 'Outils de développement' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'Taille réelle' },
        { role: 'zoomIn', label: 'Zoom avant' },
        { role: 'zoomOut', label: 'Zoom arrière' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'Plein écran' }
      ]
    },
    {
      label: 'Navigation',
      submenu: [
        {
          label: 'Accueil',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/`);
            }
          }
        },
        {
          label: 'Chat',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/chat.html`);
            }
          }
        },
        {
          label: 'Mémoire Thermique',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/futuristic-interface.html`);
            }
          }
        },
        {
          label: 'Accélérateurs Kyber',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/kyber-dashboard.html`);
            }
          }
        },
        {
          label: 'Agents',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/agents.html`);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Présentation',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/presentation.html`);
            }
          }
        },
        {
          label: 'Navigation Agent',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/agent-navigation.html`);
            }
          }
        },
        {
          label: 'Sécurité',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/security-dashboard.html`);
            }
          }
        },
        {
          label: 'Studio de Génération',
          click: () => {
            if (mainWindow) {
              mainWindow.loadURL(`http://localhost:${PORT}/generation-studio.html`);
            }
          }
        }
      ]
    },
    {
      label: 'Fenêtre',
      submenu: [
        { role: 'minimize', label: 'Réduire' },
        { role: 'zoom', label: 'Zoom' },
        { type: 'separator' },
        { role: 'front', label: 'Tout ramener au premier plan' }
      ]
    },
    {
      role: 'help',
      label: 'Aide',
      submenu: [
        {
          label: 'Documentation',
          click: async () => {
            await shell.openExternal('https://github.com/votre-repo/louna/wiki');
          }
        },
        {
          label: 'Signaler un problème',
          click: async () => {
            await shell.openExternal('https://github.com/votre-repo/louna/issues');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  // Configurer les événements Touch Bar
  if (BrowserWindow.supportsTouchBar) {
    setupTouchBar();
  }

  // Configurer les événements de l'application
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
}

/**
 * Configure la Touch Bar pour macOS
 */
function setupTouchBar() {
  const { TouchBar } = require('electron');
  const { TouchBarButton, TouchBarSpacer, TouchBarSegmentedControl } = TouchBar;

  // Bouton pour ouvrir le chat
  const chatButton = new TouchBarButton({
    label: '💬 Chat',
    backgroundColor: '#ff69b4',
    click: () => {
      if (mainWindow) {
        mainWindow.loadURL(`http://localhost:${PORT}/chat.html`);
      }
    }
  });

  // Bouton pour ouvrir la mémoire thermique
  const memoryButton = new TouchBarButton({
    label: '🧠 Mémoire',
    backgroundColor: '#1a1a2e',
    click: () => {
      if (mainWindow) {
        mainWindow.loadURL(`http://localhost:${PORT}/futuristic-interface.html`);
      }
    }
  });

  // Bouton pour ouvrir les accélérateurs Kyber
  const kyberButton = new TouchBarButton({
    label: '⚡ Kyber',
    backgroundColor: '#0f3460',
    click: () => {
      if (mainWindow) {
        mainWindow.loadURL(`http://localhost:${PORT}/kyber-dashboard.html`);
      }
    }
  });

  // Contrôle segmenté pour les actions rapides
  const quickActions = new TouchBarSegmentedControl({
    segments: [
      { label: '🏠', mode: 'home' },
      { label: '⚙️', mode: 'settings' },
      { label: '📊', mode: 'performance' }
    ],
    selectedIndex: 0,
    change: (selectedIndex, isSelected) => {
      if (!mainWindow) return;

      switch (selectedIndex) {
        case 0: // Home
          mainWindow.loadURL(`http://localhost:${PORT}/`);
          break;
        case 1: // Settings
          mainWindow.loadURL(`http://localhost:${PORT}/settings-new.html`);
          break;
        case 2: // Performance
          mainWindow.loadURL(`http://localhost:${PORT}/performance.html`);
          break;
      }
    }
  });

  // Configurer la Touch Bar
  const touchBar = new TouchBar({
    items: [
      chatButton,
      new TouchBarSpacer({ size: 'small' }),
      memoryButton,
      new TouchBarSpacer({ size: 'small' }),
      kyberButton,
      new TouchBarSpacer({ size: 'large' }),
      quickActions
    ]
  });

  // Appliquer la Touch Bar à la fenêtre principale
  if (mainWindow) {
    mainWindow.setTouchBar(touchBar);
  }
}

/**
 * Importe des données depuis un fichier
 */
function importData() {
  dialog.showOpenDialog(mainWindow, {
    title: 'Importer des données',
    filters: [
      { name: 'Fichiers JSON', extensions: ['json'] },
      { name: 'Tous les fichiers', extensions: ['*'] }
    ],
    properties: ['openFile']
  }).then(result => {
    if (!result.canceled && result.filePaths.length > 0) {
      const filePath = result.filePaths[0];

      try {
        const data = fs.readFileSync(filePath, 'utf8');
        const jsonData = JSON.parse(data);

        // Importer les données dans la mémoire thermique
        // Cette partie dépend de l'implémentation de votre mémoire thermique

        new Notification({
          title: 'Importation réussie',
          body: 'Les données ont été importées avec succès.',
          icon: iconPath
        }).show();
      } catch (error) {
        dialog.showErrorBox(
          'Erreur d\'importation',
          `Une erreur s'est produite lors de l'importation des données : ${error.message}`
        );
      }
    }
  });
}

/**
 * Exporte des données vers un fichier
 */
function exportData() {
  dialog.showSaveDialog(mainWindow, {
    title: 'Exporter des données',
    filters: [
      { name: 'Fichiers JSON', extensions: ['json'] },
      { name: 'Tous les fichiers', extensions: ['*'] }
    ]
  }).then(result => {
    if (!result.canceled && result.filePath) {
      const filePath = result.filePath;

      try {
        // Exporter les données de la mémoire thermique
        // Cette partie dépend de l'implémentation de votre mémoire thermique
        const data = JSON.stringify({
          memory: {
            // Données de la mémoire thermique
          },
          accelerators: {
            // Données des accélérateurs Kyber
          }
        }, null, 2);

        fs.writeFileSync(filePath, data, 'utf8');

        new Notification({
          title: 'Exportation réussie',
          body: 'Les données ont été exportées avec succès.',
          icon: iconPath
        }).show();
      } catch (error) {
        dialog.showErrorBox(
          'Erreur d\'exportation',
          `Une erreur s'est produite lors de l'exportation des données : ${error.message}`
        );
      }
    }
  });
}

/**
 * Sauvegarde la mémoire thermique
 */
function backupMemory() {
  // Créer le dossier de sauvegarde s'il n'existe pas
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  // Nom du fichier de sauvegarde
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const backupPath = path.join(backupDir, `louna-backup-${timestamp}`);

  // Créer le dossier de sauvegarde
  fs.mkdirSync(backupPath, { recursive: true });

  try {
    // Copier les fichiers de données
    fs.cpSync(dataDir, path.join(backupPath, 'data'), { recursive: true });

    // Afficher une notification
    new Notification({
      title: 'Sauvegarde terminée',
      body: 'La mémoire thermique a été sauvegardée avec succès.',
      icon: iconPath
    }).show();

    // Afficher une boîte de dialogue
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Sauvegarde terminée',
      message: 'La mémoire thermique a été sauvegardée avec succès.',
      detail: `Emplacement de la sauvegarde : ${backupPath}`,
      buttons: ['OK']
    });
  } catch (error) {
    console.error('Erreur lors de la sauvegarde :', error);

    // Afficher une boîte de dialogue d'erreur
    dialog.showErrorBox(
      'Erreur de sauvegarde',
      `Une erreur s'est produite lors de la sauvegarde de la mémoire thermique : ${error.message}`
    );
  }
}

/**
 * Restaure la mémoire thermique
 */
function restoreMemory() {
  // Vérifier si le dossier de sauvegarde existe
  if (!fs.existsSync(backupDir)) {
    dialog.showErrorBox(
      'Aucune sauvegarde',
      'Aucune sauvegarde n\'a été trouvée.'
    );
    return;
  }

  // Lister les sauvegardes
  const backups = fs.readdirSync(backupDir)
    .filter(file => fs.statSync(path.join(backupDir, file)).isDirectory())
    .filter(file => file.startsWith('louna-backup-'))
    .sort((a, b) => {
      const timeA = fs.statSync(path.join(backupDir, a)).mtime.getTime();
      const timeB = fs.statSync(path.join(backupDir, b)).mtime.getTime();
      return timeB - timeA; // Tri par date de modification (la plus récente en premier)
    });

  if (backups.length === 0) {
    dialog.showErrorBox(
      'Aucune sauvegarde',
      'Aucune sauvegarde n\'a été trouvée.'
    );
    return;
  }

  // Afficher une boîte de dialogue pour sélectionner une sauvegarde
  dialog.showMessageBox(mainWindow, {
    type: 'question',
    title: 'Restaurer la mémoire',
    message: 'Sélectionnez une sauvegarde à restaurer',
    detail: 'Attention : Cette opération remplacera toutes les données actuelles.',
    buttons: [...backups.map(b => {
      const date = b.replace('louna-backup-', '');
      return new Date(date).toLocaleString();
    }), 'Annuler'],
    cancelId: backups.length
  }).then(({ response }) => {
    // Si l'utilisateur a annulé
    if (response === backups.length) {
      return;
    }

    // Sauvegarde sélectionnée
    const selectedBackup = backups[response];
    const backupPath = path.join(backupDir, selectedBackup, 'data');

    // Vérifier si le dossier de sauvegarde existe
    if (!fs.existsSync(backupPath)) {
      dialog.showErrorBox(
        'Sauvegarde invalide',
        'La sauvegarde sélectionnée est invalide ou corrompue.'
      );
      return;
    }

    try {
      // Copier les fichiers de sauvegarde
      fs.cpSync(backupPath, dataDir, { recursive: true });

      // Afficher une notification
      new Notification({
        title: 'Restauration terminée',
        body: 'La mémoire thermique a été restaurée avec succès.',
        icon: iconPath
      }).show();

      // Afficher une boîte de dialogue
      dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'Restauration terminée',
        message: 'La mémoire thermique a été restaurée avec succès.',
        detail: 'L\'application va redémarrer pour appliquer les changements.',
        buttons: ['OK']
      }).then(() => {
        // Redémarrer l'application
        app.relaunch();
        app.exit();
      });
    } catch (error) {
      console.error('Erreur lors de la restauration :', error);

      // Afficher une boîte de dialogue d'erreur
      dialog.showErrorBox(
        'Erreur de restauration',
        `Une erreur s'est produite lors de la restauration de la mémoire thermique : ${error.message}`
      );
    }
  });
}

// Quand Electron a fini de s'initialiser
app.whenReady().then(async () => {
  try {
    // Démarrer le serveur Express
    await startServer();

    // Créer la fenêtre principale
    createWindow();

    // Créer l'icône dans la barre des tâches (ignorer les erreurs)
    try {
      createTray();
    } catch (error) {
      console.error('Erreur lors de la création de la barre des tâches, mais l\'application continue:', error);
    }

    // Configurer les fonctionnalités spécifiques à macOS
    if (process.platform === 'darwin') {
      setupMacOSFeatures();
    }

    // Enregistrer les raccourcis clavier globaux
    registerGlobalShortcuts(mainWindow, PORT);

    // Initialiser le démarrage automatique
    autoLaunch.initialize();

    // Créer les dossiers nécessaires
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Gérer l'activation de l'application (macOS)
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
      }
    });
  } catch (error) {
    console.error('Erreur lors du démarrage de l\'application :', error);
    dialog.showErrorBox(
      'Erreur de démarrage',
      `Une erreur s'est produite lors du démarrage de l'application : ${error.message}`
    );
    app.quit();
  }
});

// Quitter l'application quand toutes les fenêtres sont fermées (Windows/Linux)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Nettoyer avant de quitter
app.on('before-quit', () => {
  // Arrêter le serveur Express
  if (serverProcess) {
    serverProcess.kill();
  }

  // Désenregistrer les raccourcis clavier globaux
  unregisterGlobalShortcuts();
});

// Gérer les messages IPC depuis le processus de rendu
ipcMain.on('show-notification', (event, { title, body }) => {
  new Notification({ title, body, icon: iconPath }).show();
});

ipcMain.on('open-external-link', (event, url) => {
  shell.openExternal(url);
});

ipcMain.on('backup-memory', () => {
  backupMemory();
});

ipcMain.on('restore-memory', () => {
  restoreMemory();
});

ipcMain.on('set-auto-launch', (event, { enabled, minimized }) => {
  autoLaunch.setAutoLaunch(enabled, minimized);
});

ipcMain.handle('get-auto-launch', async () => {
  return {
    enabled: autoLaunch.isAutoLaunchEnabled(),
    minimized: autoLaunch.isMinimizedOnLaunch()
  };
});

// Nouveaux gestionnaires d'événements IPC pour l'application native
ipcMain.on('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.on('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.on('show-context-menu', (event) => {
  const template = [
    {
      label: 'Couper',
      role: 'cut'
    },
    {
      label: 'Copier',
      role: 'copy'
    },
    {
      label: 'Coller',
      role: 'paste'
    },
    { type: 'separator' },
    {
      label: 'Sélectionner tout',
      role: 'selectAll'
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  menu.popup({ window: BrowserWindow.fromWebContents(event.sender) });
});

ipcMain.handle('get-system-info', async () => {
  return {
    platform: process.platform,
    arch: process.arch,
    version: app.getVersion(),
    electronVersion: process.versions.electron,
    nodeVersion: process.versions.node,
    chromeVersion: process.versions.chrome,
    cpuCount: os.cpus().length,
    totalMemory: os.totalmem(),
    freeMemory: os.freemem(),
    hostname: os.hostname(),
    username: os.userInfo().username,
    homedir: os.homedir(),
    tempdir: os.tmpdir()
  };
});

ipcMain.handle('dialog:openFile', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('dialog:openDirectory', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, {
    ...options,
    properties: ['openDirectory']
  });
  return result;
});

ipcMain.handle('dialog:saveFile', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('file:read', async (event, filePath) => {
  try {
    const data = await fs.promises.readFile(filePath, 'utf8');
    return data;
  } catch (error) {
    throw new Error(`Erreur lors de la lecture du fichier : ${error.message}`);
  }
});

ipcMain.handle('file:write', async (event, filePath, content) => {
  try {
    await fs.promises.writeFile(filePath, content, 'utf8');
    return true;
  } catch (error) {
    throw new Error(`Erreur lors de l'écriture du fichier : ${error.message}`);
  }
});

ipcMain.handle('file:exists', async (event, filePath) => {
  try {
    await fs.promises.access(filePath);
    return true;
  } catch (error) {
    return false;
  }
});
