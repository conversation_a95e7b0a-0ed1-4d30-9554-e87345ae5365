/**
 * Système de Mémoire Thermique pour l'agent Louna
 *
 * Ce système gère plusieurs niveaux de mémoire avec des "températures" différentes
 * pour déterminer l'importance et la fraîcheur des informations.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { EventEmitter } = require('events');
const SystemTemperature = require('./system-temperature');
const ThermalLearningSystem = require('./thermal-learning-system');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, 'data/memory');
const MEMORY_FILE = path.join(DATA_DIR, 'thermal_memory.json');

/**
 * Classe ThermalMemory - Gère la mémoire thermique de l'agent
 * Hérite d'EventEmitter pour permettre la communication entre les composants
 */
class ThermalMemory extends EventEmitter {
  /**
   * Initialise la mémoire thermique
   * @param {Object} config - Configuration de la mémoire thermique
   */
  constructor(config = {}) {
    super(); // Initialiser EventEmitter
    // Configuration par défaut
    this.config = {
      memoryCycleInterval: config.memoryCycleInterval || 300, // secondes
      memoryDecayRate: config.memoryDecayRate || 0.95,

      // Capacités des différents niveaux de mémoire
      instantCapacity: config.instantCapacity || 20,
      shortTermCapacity: config.shortTermCapacity || 50,
      workingMemoryCapacity: config.workingMemoryCapacity || 100,
      mediumTermCapacity: config.mediumTermCapacity || 200,
      longTermCapacity: config.longTermCapacity || 1000,
      dreamMemoryCapacity: config.dreamMemoryCapacity || 50,

      // Facteurs d'importance
      importanceFactor: config.importanceFactor || 1.2,
      accessFactor: config.accessFactor || 1.05,
      decayFactor: config.decayFactor || 0.98,

      // Seuils de température
      hotThreshold: config.hotThreshold || 0.8,
      warmThreshold: config.warmThreshold || 0.6,
      coolThreshold: config.coolThreshold || 0.4,
      coldThreshold: config.coldThreshold || 0.2,

      // Configuration du curseur de température
      temperatureCursorEnabled: config.temperatureCursorEnabled !== undefined ? config.temperatureCursorEnabled : true,
      temperatureCursorSensitivity: config.temperatureCursorSensitivity || 0.1,
      temperatureCursorMin: config.temperatureCursorMin || 0.3,
      temperatureCursorMax: config.temperatureCursorMax || 0.7,
    };

    // Curseur de température pour ajuster dynamiquement les seuils
    this.temperatureCursor = {
      position: 0.5, // Position initiale (0-1)
      zoneSpacing: 0.15, // Espacement entre les zones
    };

    // Initialiser le module de température système
    this.systemTemperature = new SystemTemperature({
      updateInterval: 5000, // Mettre à jour toutes les 5 secondes
      debug: config.debug || false
    });

    // Utiliser les températures réelles du système
    this.useRealTemperatures = config.useRealTemperatures !== undefined ? config.useRealTemperatures : true;

    // Initialiser le système d'apprentissage
    this.learningSystem = new ThermalLearningSystem({
      learningRate: config.learningRate || 0.01,
      optimizationInterval: config.optimizationInterval || 10,
      adaptationEnabled: config.adaptationEnabled !== undefined ? config.adaptationEnabled : true,
      temperatureCursorSensitivity: this.config.temperatureCursorSensitivity,
      memoryDecayRate: this.config.memoryDecayRate,
      importanceFactor: this.config.importanceFactor,
      accessFactor: this.config.accessFactor,
      decayFactor: this.config.decayFactor,
      debug: config.debug || false
    });

    // Activer l'auto-évolution
    this.autoEvolutionEnabled = config.autoEvolutionEnabled !== undefined ? config.autoEvolutionEnabled : true;

    // Initialiser le système de sommeil et consolidation
    this.sleepMode = {
      enabled: config.sleepEnabled !== undefined ? config.sleepEnabled : true,
      active: false,
      lastActivity: Date.now(),
      inactivityThreshold: config.sleepInactivityThreshold || 300000, // 5 minutes par défaut
      consolidationInterval: config.sleepConsolidationInterval || 60000, // 1 minute par défaut
      consolidationTimer: null,
      consolidationCount: 0,
      lastConsolidation: null
    };

    // Initialiser les niveaux de mémoire
    this.instantMemory = {}; // Mémoire instantanée (niveau 1)
    this.shortTerm = {};     // Mémoire à court terme (niveau 2)
    this.workingMemory = {}; // Mémoire de travail (niveau 3)
    this.mediumTerm = {};    // Mémoire à moyen terme (niveau 4)
    this.longTerm = {};      // Mémoire à long terme (niveau 5)
    this.dreamMemory = {};   // Mémoire des rêves (niveau 6)

    // Initialiser l'accélérateur Kyber
    this.kyber = {
      boostFactor: config.kyberBoostFactor || 1.5,
      temperature: config.kyberTemperature || 0.6,
      stability: config.kyberStability || 0.9,
      locked: config.kyberLocked || false,
      enabled: config.kyberEnabled !== undefined ? config.kyberEnabled : true
    };

    // Statistiques
    this.stats = {
      totalEntries: 0,
      instantEntries: 0,
      shortTermEntries: 0,
      workingMemoryEntries: 0,
      mediumTermEntries: 0,
      longTermEntries: 0,
      dreamMemoryEntries: 0,
      cyclesPerformed: 0,
      lastCycleTime: null,
      averageTemperature: 0.5
    };

    // Initialiser le dossier de données
    this.initDataDir();

    // Charger les données existantes
    this.loadMemory();

    // Démarrer le cycle de mémoire
    this.startMemoryCycle();

    console.log('Mémoire thermique initialisée');
  }

  /**
   * Initialise le dossier de données
   */
  async initDataDir() {
    try {
      if (!await existsAsync(DATA_DIR)) {
        await mkdirAsync(DATA_DIR, { recursive: true });
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du dossier de données:', error);
    }
  }

  /**
   * Normalise toutes les températures existantes (corrige les anciennes données)
   */
  normalizeAllTemperatures() {
    console.log('🌡️ Normalisation des températures en cours...');

    let correctedCount = 0;

    const normalizeLevel = (memoryLevel, levelName) => {
      Object.values(memoryLevel).forEach(entry => {
        if (entry.temperature > 1.0) {
          // Normaliser les températures aberrantes (ex: 100 -> 1.0)
          entry.temperature = entry.temperature > 50 ? 1.0 : entry.temperature / 100;
          correctedCount++;
        }
        if (entry.temperature < 0.0) {
          entry.temperature = 0.0;
          correctedCount++;
        }
      });
    };

    // Normaliser tous les niveaux
    normalizeLevel(this.instantMemory, 'instant');
    normalizeLevel(this.shortTerm, 'shortTerm');
    normalizeLevel(this.workingMemory, 'working');
    normalizeLevel(this.mediumTerm, 'medium');
    normalizeLevel(this.longTerm, 'longTerm');
    normalizeLevel(this.dreamMemory, 'dream');

    if (correctedCount > 0) {
      console.log(`✅ ${correctedCount} températures corrigées et normalisées`);
      this.saveMemory(); // Sauvegarder les corrections
    } else {
      console.log('✅ Toutes les températures sont déjà normalisées');
    }
  }

  /**
   * Charge la mémoire depuis le fichier
   */
  async loadMemory() {
    try {
      if (await existsAsync(MEMORY_FILE)) {
        const data = await readFileAsync(MEMORY_FILE, 'utf8');

        // Nettoyer les données JSON avant le parsing
        const cleanedData = this.cleanJsonData(data);

        let memory;
        try {
          memory = JSON.parse(cleanedData);
        } catch (parseError) {
          console.warn('Erreur de parsing JSON, tentative de réparation...');
          try {
            const repairedData = this.repairJsonData(cleanedData);
            memory = JSON.parse(repairedData);
          } catch (repairError) {
            console.warn('Impossible de réparer le JSON, réinitialisation de la mémoire');
            this.resetMemory();
            console.log('Mémoire réinitialisée');
            return;
          }
        }

        // Charger les différents niveaux de mémoire
        this.instantMemory = memory.instantMemory || {};
        this.shortTerm = memory.shortTerm || {};
        this.workingMemory = memory.workingMemory || {};
        this.mediumTerm = memory.mediumTerm || {};
        this.longTerm = memory.longTerm || {};
        this.dreamMemory = memory.dreamMemory || {};

        // Charger les statistiques
        this.stats = memory.stats || this.stats;

        // Normaliser les températures après le chargement
        this.normalizeAllTemperatures();

        console.log('Mémoire chargée avec succès');
      } else {
        console.log('Aucun fichier de mémoire existant, création d\'une nouvelle mémoire');
        this.saveMemory();
      }
    } catch (error) {
      console.error('Erreur lors du chargement de la mémoire:', error);
      // Créer un nouveau fichier de mémoire en cas d'erreur
      this.resetMemory();
      this.saveMemory();
    }
  }

  /**
   * Nettoie les données JSON pour éviter les erreurs de parsing
   */
  cleanJsonData(data) {
    // Supprimer les caractères de contrôle invisibles
    let cleaned = data.replace(/[\x00-\x1F\x7F-\x9F]/g, '');

    // Supprimer les virgules en trop
    cleaned = cleaned.replace(/,(\s*[}\]])/g, '$1');

    // Supprimer les accolades/crochets orphelins
    cleaned = cleaned.replace(/[{}[\]]/g, (match, offset, string) => {
      // Vérifier si c'est dans une chaîne
      const beforeMatch = string.substring(0, offset);
      const quotes = (beforeMatch.match(/"/g) || []).length;
      return quotes % 2 === 0 ? match : '';
    });

    return cleaned;
  }

  /**
   * Répare les données JSON corrompues
   */
  repairJsonData(data) {
    try {
      // Tentative de réparation basique
      let repaired = data;

      // Ajouter des accolades manquantes
      const openBraces = (repaired.match(/{/g) || []).length;
      const closeBraces = (repaired.match(/}/g) || []).length;

      if (openBraces > closeBraces) {
        repaired += '}}'.repeat(openBraces - closeBraces);
      }

      // Ajouter des crochets manquants
      const openBrackets = (repaired.match(/\[/g) || []).length;
      const closeBrackets = (repaired.match(/\]/g) || []).length;

      if (openBrackets > closeBrackets) {
        repaired += ']'.repeat(openBrackets - closeBrackets);
      }

      return repaired;
    } catch (error) {
      console.warn('Impossible de réparer le JSON, création d\'un nouveau fichier');
      return '{}';
    }
  }

  /**
   * Remet à zéro la mémoire
   */
  resetMemory() {
    this.instantMemory = {};
    this.shortTerm = {};
    this.workingMemory = {};
    this.mediumTerm = {};
    this.longTerm = {};
    this.dreamMemory = {};

    this.stats = {
      totalEntries: 0,
      instantEntries: 0,
      shortTermEntries: 0,
      workingMemoryEntries: 0,
      mediumTermEntries: 0,
      longTermEntries: 0,
      dreamMemoryEntries: 0,
      cyclesPerformed: 0,
      lastCycleTime: Date.now(),
      averageTemperature: 0.5,
      performanceMetrics: {}
    };

    console.log('Mémoire remise à zéro');
  }

  /**
   * Sauvegarde la mémoire dans le fichier
   */
  async saveMemory() {
    try {
      const memory = {
        instantMemory: this.instantMemory || {},
        shortTerm: this.shortTerm || {},
        workingMemory: this.workingMemory || {},
        mediumTerm: this.mediumTerm || {},
        longTerm: this.longTerm || {},
        dreamMemory: this.dreamMemory || {},
        stats: this.stats || {}
      };

      // Nettoyer les données avant la sérialisation
      const cleanedMemory = this.sanitizeMemoryData(memory);

      // Sérialiser avec gestion d'erreur
      const jsonData = this.safeJsonStringify(cleanedMemory);

      await writeFileAsync(MEMORY_FILE, jsonData, 'utf8');

      console.log('Mémoire sauvegardée avec succès');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la mémoire:', error);
      // Tentative de sauvegarde de secours
      await this.emergencyBackup();
    }
  }

  /**
   * Nettoie les données de mémoire pour éviter les erreurs de sérialisation
   */
  sanitizeMemoryData(memory) {
    const sanitized = {};

    for (const [key, value] of Object.entries(memory)) {
      if (value && typeof value === 'object') {
        sanitized[key] = {};
        for (const [subKey, subValue] of Object.entries(value)) {
          try {
            // Vérifier que la valeur peut être sérialisée
            JSON.stringify(subValue);
            sanitized[key][subKey] = subValue;
          } catch (error) {
            console.warn(`Données corrompues ignorées: ${key}.${subKey}`);
          }
        }
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Sérialisation JSON sécurisée
   */
  safeJsonStringify(data) {
    try {
      return JSON.stringify(data, (key, value) => {
        // Remplacer les valeurs problématiques
        if (value === undefined) return null;
        if (typeof value === 'function') return null;
        if (value instanceof Error) return value.message;
        if (typeof value === 'bigint') return value.toString();
        return value;
      }, 2);
    } catch (error) {
      console.error('Erreur de sérialisation JSON:', error);
      return '{}';
    }
  }

  /**
   * Sauvegarde d'urgence en cas d'erreur
   */
  async emergencyBackup() {
    try {
      const basicMemory = {
        instantMemory: {},
        shortTerm: {},
        workingMemory: {},
        mediumTerm: {},
        longTerm: {},
        dreamMemory: {},
        stats: {
          totalEntries: 0,
          instantEntries: 0,
          shortTermEntries: 0,
          workingMemoryEntries: 0,
          mediumTermEntries: 0,
          longTermEntries: 0,
          dreamMemoryEntries: 0,
          cyclesPerformed: 0,
          lastCycleTime: Date.now(),
          averageTemperature: 0.5,
          performanceMetrics: {}
        }
      };

      await writeFileAsync(MEMORY_FILE, JSON.stringify(basicMemory, null, 2), 'utf8');
      console.log('Sauvegarde d\'urgence effectuée');
    } catch (emergencyError) {
      console.error('Échec de la sauvegarde d\'urgence:', emergencyError);
    }
  }

  /**
   * Ajoute une information à la mémoire
   * @param {string} key - Clé pour identifier l'information
   * @param {Object} data - Données à stocker
   * @param {number} importance - Importance de l'information (0.0-1.0)
   * @param {string} category - Catégorie de l'information
   * @param {Object} metadata - Métadonnées supplémentaires
   * @returns {string} - ID de l'entrée ajoutée
   */
  add(key, data, importance = 0.5, category = 'general', metadata = {}) {
    // Générer un ID unique
    const id = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Calculer la température initiale en fonction de l'importance (normalisée entre 0 et 1)
    let temperature = Math.min(1.0, importance * this.config.importanceFactor);

    // Correction de sécurité : normaliser les températures aberrantes
    if (temperature > 1.0) {
      temperature = 1.0;
    }
    if (temperature < 0.0) {
      temperature = 0.0;
    }

    // Créer l'entrée
    const entry = {
      id,
      key,
      data,
      category,
      created: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1,
      temperature,
      importance,
      metadata
    };

    // Obtenir les seuils de température dynamiques
    const thresholds = this.getTemperatureThresholds();

    // Déterminer le niveau de mémoire en fonction de la température
    if (entry.temperature >= thresholds.instant) {
      // Mémoire instantanée pour les informations très importantes
      this.instantMemory[id] = entry;
      this.stats.instantEntries++;
    } else if (entry.temperature >= thresholds.shortTerm) {
      // Mémoire à court terme pour les informations importantes
      this.shortTerm[id] = entry;
      this.stats.shortTermEntries++;
    } else if (entry.temperature >= thresholds.working) {
      // Mémoire de travail pour les informations standard
      this.workingMemory[id] = entry;
      this.stats.workingMemoryEntries++;
    } else if (entry.temperature >= thresholds.mediumTerm) {
      // Mémoire à moyen terme
      this.mediumTerm[id] = entry;
      this.stats.mediumTermEntries++;
    } else {
      // Mémoire à long terme pour les informations moins importantes
      this.longTerm[id] = entry;
      this.stats.longTermEntries++;
    }

    // Mettre à jour les statistiques
    this.stats.totalEntries++;

    // Sauvegarder la mémoire si c'est une information importante
    if (entry.temperature >= 0.7) {
      this.saveMemory();
    }

    return id;
  }

  /**
   * Ajoute une information à la mémoire (alias pour add avec format différent)
   * @param {Object} info - Information à ajouter
   * @param {string} info.content - Contenu de l'information
   * @param {string} info.source - Source de l'information
   * @param {number} info.importance - Importance de l'information (0.0-1.0)
   * @param {Array} info.tags - Tags associés à l'information
   * @returns {string} - ID de l'entrée ajoutée
   */
  addInformation(info) {
    if (!info || typeof info !== 'object') {
      throw new Error('Information invalide');
    }

    // Extraire les données de l'objet info
    const content = info.content || info.data || '';
    const source = info.source || 'unknown';
    const importance = info.importance || 0.5;
    const tags = info.tags || [];
    const category = info.category || source;

    // Créer une clé basée sur le contenu et la source
    const key = `${source}_${Date.now()}`;

    // Créer les données à stocker
    const data = {
      content: content,
      source: source,
      tags: tags,
      timestamp: Date.now()
    };

    // Utiliser la méthode add existante
    return this.add(key, data, importance, category, {
      originalInfo: info
    });
  }

  /**
   * Récupère une information spécifique de la mémoire
   * @param {string} id - ID de l'entrée à récupérer
   * @returns {Object|null} - L'entrée récupérée ou null si non trouvée
   */
  get(id) {
    // Chercher dans tous les niveaux de mémoire
    let entry = this.instantMemory[id] ||
                this.shortTerm[id] ||
                this.workingMemory[id] ||
                this.mediumTerm[id] ||
                this.longTerm[id] ||
                this.dreamMemory[id];

    if (entry) {
      // Mettre à jour les statistiques d'accès
      entry.lastAccessed = Date.now();
      entry.accessCount++;

      // Augmenter légèrement la température à chaque accès (normalisée entre 0 et 1)
      entry.temperature = Math.min(1.0, entry.temperature * this.config.accessFactor);

      // Correction de sécurité : s'assurer que la température reste dans les limites
      if (entry.temperature > 1.0) {
        entry.temperature = 1.0;
      }
      if (entry.temperature < 0.0) {
        entry.temperature = 0.0;
      }

      return entry;
    }

    return null;
  }

  /**
   * Récupère toutes les entrées de la mémoire
   * @returns {Array} - Toutes les entrées de mémoire
   */
  getAllEntries() {
    const entries = [
      ...Object.values(this.instantMemory),
      ...Object.values(this.shortTerm),
      ...Object.values(this.workingMemory),
      ...Object.values(this.mediumTerm),
      ...Object.values(this.longTerm),
      ...Object.values(this.dreamMemory)
    ];

    return entries;
  }

  /**
   * Alias pour getAllEntries pour la compatibilité avec l'API
   * @returns {Array} - Toutes les entrées de mémoire
   */
  getAll() {
    return this.getAllEntries();
  }

  /**
   * Récupère les entrées d'une zone spécifique
   * @param {string} zone - Nom de la zone (instant, shortTerm, workingMemory, mediumTerm, longTerm, dreamMemory)
   * @returns {Array} - Entrées de la zone spécifiée
   */
  getEntriesFromZone(zone) {
    switch (zone) {
      case 'instant':
        return Object.values(this.instantMemory);
      case 'shortTerm':
        return Object.values(this.shortTerm);
      case 'workingMemory':
        return Object.values(this.workingMemory);
      case 'mediumTerm':
        return Object.values(this.mediumTerm);
      case 'longTerm':
        return Object.values(this.longTerm);
      case 'dreamMemory':
        return Object.values(this.dreamMemory);
      default:
        return [];
    }
  }

  /**
   * Récupère les entrées récentes pour un contexte donné
   * @param {string} context - Contexte pour filtrer les entrées
   * @param {number} limit - Nombre maximum d'entrées à récupérer
   * @returns {Array} - Entrées récentes pour le contexte donné
   */
  getRecentMemoriesForContext(context = '', limit = 10) {
    // Récupérer toutes les entrées
    const allEntries = this.getAllEntries();

    // Filtrer par contexte si spécifié
    const filteredEntries = context
      ? allEntries.filter(entry => {
          try {
            const keyStr = (entry.key || '').toString().toLowerCase();
            const dataStr = entry.data ? JSON.stringify(entry.data).toLowerCase() : '';
            const contextLower = context.toLowerCase();
            return keyStr.includes(contextLower) || dataStr.includes(contextLower);
          } catch (error) {
            console.warn('Erreur lors du filtrage de l\'entrée:', error);
            return false;
          }
        })
      : allEntries;

    // Trier par date d'accès (plus récent en premier)
    const sortedEntries = filteredEntries.sort((a, b) => b.lastAccessed - a.lastAccessed);

    // Limiter le nombre d'entrées
    return sortedEntries.slice(0, limit);
  }

  /**
   * Supprime une entrée de la mémoire
   * @param {string} id - ID de l'entrée à supprimer
   * @returns {boolean} - Succès de l'opération
   */
  remove(id) {
    // Chercher dans tous les niveaux de mémoire
    if (this.instantMemory[id]) {
      delete this.instantMemory[id];
      this.stats.instantEntries--;
      this.stats.totalEntries--;
      return true;
    } else if (this.shortTerm[id]) {
      delete this.shortTerm[id];
      this.stats.shortTermEntries--;
      this.stats.totalEntries--;
      return true;
    } else if (this.workingMemory[id]) {
      delete this.workingMemory[id];
      this.stats.workingMemoryEntries--;
      this.stats.totalEntries--;
      return true;
    } else if (this.mediumTerm[id]) {
      delete this.mediumTerm[id];
      this.stats.mediumTermEntries--;
      this.stats.totalEntries--;
      return true;
    } else if (this.longTerm[id]) {
      delete this.longTerm[id];
      this.stats.longTermEntries--;
      this.stats.totalEntries--;
      return true;
    } else if (this.dreamMemory[id]) {
      delete this.dreamMemory[id];
      this.stats.dreamMemoryEntries--;
      this.stats.totalEntries--;
      return true;
    }

    return false;
  }

  /**
   * Effectue un cycle de mémoire
   * - Décroît la température des entrées
   * - Déplace les entrées entre les niveaux de mémoire
   * - Nettoie les entrées obsolètes
   * - Applique l'auto-apprentissage et l'évolution
   * - Vérifie si le système doit entrer en mode sommeil
   */
  performMemoryCycle() {
    console.log('Exécution d\'un cycle de mémoire...');

    // Si en mode sommeil, effectuer une consolidation de mémoire
    if (this.sleepMode.active) {
      console.log('Mode sommeil actif, cycle de mémoire standard ignoré');
      return;
    }

    // Décroître la température de toutes les entrées
    this.decayTemperatures();

    // Déplacer les entrées entre les niveaux de mémoire
    this.moveEntries();

    // Nettoyer les entrées obsolètes
    this.cleanupEntries();

    // Mettre à jour les statistiques
    this.updateStats();

    // Appliquer l'auto-apprentissage et l'évolution si activés
    if (this.autoEvolutionEnabled) {
      this.evolveSystem();
    }

    // Vérifier si le système doit entrer en mode sommeil
    if (this.sleepMode.enabled) {
      this.checkSleepMode();
    }

    // Sauvegarder la mémoire
    this.saveMemory();

    // Mettre à jour les statistiques de cycle
    this.stats.cyclesPerformed++;
    this.stats.lastCycleTime = Date.now();

    console.log('Cycle de mémoire terminé');
  }

  /**
   * Applique l'auto-apprentissage et l'évolution du système
   */
  evolveSystem() {
    // Récupérer les statistiques de mémoire
    const memoryStats = this.getMemoryStats();

    // Récupérer les températures du système
    const systemTemperatures = this.getSystemTemperatures();

    // Récupérer les paramètres actuels
    const currentParameters = {
      temperatureCursorSensitivity: this.config.temperatureCursorSensitivity,
      memoryDecayRate: this.config.memoryDecayRate,
      importanceFactor: this.config.importanceFactor,
      accessFactor: this.config.accessFactor,
      decayFactor: this.config.decayFactor
    };

    // Enregistrer les données du cycle actuel
    this.learningSystem.recordCycleData(memoryStats, systemTemperatures, currentParameters);

    // Récupérer les paramètres optimisés
    const optimizedParameters = this.learningSystem.getOptimizedParameters();

    // Appliquer les paramètres optimisés
    this.config.temperatureCursorSensitivity = optimizedParameters.temperatureCursorSensitivity;
    this.config.memoryDecayRate = optimizedParameters.memoryDecayRate;
    this.config.importanceFactor = optimizedParameters.importanceFactor;
    this.config.accessFactor = optimizedParameters.accessFactor;
    this.config.decayFactor = optimizedParameters.decayFactor;

    // Mettre à jour les métriques de performance
    this.stats.performanceMetrics = this.learningSystem.getPerformanceMetrics();

    console.log('Évolution du système appliquée');
  }

  /**
   * Décroît la température de toutes les entrées
   */
  decayTemperatures() {
    const decayEntries = (memoryLevel) => {
      Object.values(memoryLevel).forEach(entry => {
        // Calculer le temps écoulé depuis le dernier accès (en heures)
        const hoursSinceLastAccess = (Date.now() - entry.lastAccessed) / (1000 * 60 * 60);

        // Décroître la température en fonction du temps écoulé
        const decayFactor = Math.pow(this.config.decayFactor, hoursSinceLastAccess);
        entry.temperature *= decayFactor;
      });
    };

    // Appliquer la décroissance à tous les niveaux de mémoire
    decayEntries(this.instantMemory);
    decayEntries(this.shortTerm);
    decayEntries(this.workingMemory);
    decayEntries(this.mediumTerm);
    decayEntries(this.longTerm);
    decayEntries(this.dreamMemory);
  }

  /**
   * Déplace les entrées entre les niveaux de mémoire en fonction de leur température
   */
  moveEntries() {
    // Obtenir les seuils de température dynamiques
    const thresholds = this.getTemperatureThresholds();

    console.log('Seuils de température actuels:', JSON.stringify(thresholds));

    // Déplacer de la mémoire instantanée vers la mémoire à court terme
    this.moveEntriesBetweenLevels(
      this.instantMemory,
      this.shortTerm,
      entry => entry.temperature < thresholds.shortTerm,
      this.config.shortTermCapacity
    );

    // Déplacer de la mémoire à court terme vers la mémoire de travail
    this.moveEntriesBetweenLevels(
      this.shortTerm,
      this.workingMemory,
      entry => entry.temperature < thresholds.working,
      this.config.workingMemoryCapacity
    );

    // Déplacer de la mémoire de travail vers la mémoire à moyen terme
    this.moveEntriesBetweenLevels(
      this.workingMemory,
      this.mediumTerm,
      entry => entry.temperature < thresholds.mediumTerm,
      this.config.mediumTermCapacity
    );

    // Déplacer de la mémoire à moyen terme vers la mémoire à long terme
    this.moveEntriesBetweenLevels(
      this.mediumTerm,
      this.longTerm,
      entry => entry.temperature < thresholds.longTerm,
      this.config.longTermCapacity
    );

    // Déplacer de la mémoire à long terme vers la mémoire des rêves
    this.moveEntriesBetweenLevels(
      this.longTerm,
      this.dreamMemory,
      entry => entry.temperature < thresholds.longTerm * 0.5 && Math.random() < 0.2, // 20% de chance
      this.config.dreamMemoryCapacity
    );

    // Mettre à jour les statistiques pour refléter les changements
    this.updateStats();
  }

  /**
   * Déplace les entrées d'un niveau de mémoire à un autre
   * @param {Object} sourceLevel - Niveau de mémoire source
   * @param {Object} targetLevel - Niveau de mémoire cible
   * @param {Function} condition - Fonction de condition pour le déplacement
   * @param {number} targetCapacity - Capacité maximale du niveau cible
   */
  moveEntriesBetweenLevels(sourceLevel, targetLevel, condition, targetCapacity) {
    // Vérifier si le niveau cible a atteint sa capacité
    if (Object.keys(targetLevel).length >= targetCapacity) {
      // Trouver l'entrée la moins importante à remplacer
      const entries = Object.values(targetLevel);
      entries.sort((a, b) => a.temperature - b.temperature);

      // Supprimer les entrées les moins importantes pour faire de la place
      const entriesToRemove = entries.slice(0, Math.ceil(targetCapacity * 0.1)); // Supprimer 10%
      entriesToRemove.forEach(entry => {
        delete targetLevel[entry.id];
      });
    }

    // Trouver les entrées à déplacer
    const entriesToMove = Object.entries(sourceLevel).filter(([id, entry]) => condition(entry));

    // Déplacer les entrées
    entriesToMove.forEach(([id, entry]) => {
      targetLevel[id] = entry;
      delete sourceLevel[id];
    });
  }

  /**
   * Nettoie les entrées obsolètes
   */
  cleanupEntries() {
    // Supprimer les entrées trop froides de la mémoire des rêves
    Object.keys(this.dreamMemory).forEach(id => {
      if (this.dreamMemory[id].temperature < 0.05) {
        delete this.dreamMemory[id];
      }
    });
  }

  /**
   * Met à jour les statistiques de la mémoire
   */
  updateStats() {
    // Mettre à jour le nombre d'entrées par niveau
    this.stats.instantEntries = Object.keys(this.instantMemory).length;
    this.stats.shortTermEntries = Object.keys(this.shortTerm).length;
    this.stats.workingMemoryEntries = Object.keys(this.workingMemory).length;
    this.stats.mediumTermEntries = Object.keys(this.mediumTerm).length;
    this.stats.longTermEntries = Object.keys(this.longTerm).length;
    this.stats.dreamMemoryEntries = Object.keys(this.dreamMemory).length;

    // Mettre à jour le nombre total d'entrées
    this.stats.totalEntries = this.stats.instantEntries +
                             this.stats.shortTermEntries +
                             this.stats.workingMemoryEntries +
                             this.stats.mediumTermEntries +
                             this.stats.longTermEntries +
                             this.stats.dreamMemoryEntries;

    // Calculer la température moyenne
    let totalTemperature = 0;
    let entryCount = 0;

    [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm, this.dreamMemory]
      .forEach(memoryLevel => {
        Object.values(memoryLevel).forEach(entry => {
          totalTemperature += entry.temperature;
          entryCount++;
        });
      });

    const previousAvgTemp = this.stats.averageTemperature;
    this.stats.averageTemperature = entryCount > 0 ? totalTemperature / entryCount : 0;

    // Ajuster le curseur de température en fonction de la température moyenne
    if (this.config.temperatureCursorEnabled && entryCount > 0) {
      this.adjustTemperatureCursor(previousAvgTemp, this.stats.averageTemperature);
    }
  }

  /**
   * Ajuste le curseur de température en fonction de la température moyenne
   * @param {number} previousAvgTemp - Température moyenne précédente
   * @param {number} currentAvgTemp - Température moyenne actuelle
   */
  adjustTemperatureCursor(previousAvgTemp, currentAvgTemp) {
    if (this.useRealTemperatures) {
      // Utiliser les températures réelles du système
      const systemTemp = this.systemTemperature.getNormalizedTemperature();

      // Ajuster la position du curseur en fonction de la température du système
      const previousPosition = this.temperatureCursor.position;
      this.temperatureCursor.position = systemTemp;

      // Limiter la position du curseur
      this.temperatureCursor.position = Math.max(
        this.config.temperatureCursorMin,
        Math.min(this.config.temperatureCursorMax, this.temperatureCursor.position)
      );

      const adjustment = this.temperatureCursor.position - previousPosition;

      console.log(`Curseur de température ajusté selon la température système: ${this.temperatureCursor.position.toFixed(3)} (température CPU: ${this.systemTemperature.getTemperatures().cpu.toFixed(1)}°C, changement: ${adjustment.toFixed(3)})`);
    } else {
      // Utiliser la méthode traditionnelle basée sur la température moyenne des entrées
      // Calculer le changement de température
      const tempChange = currentAvgTemp - previousAvgTemp;

      // Ajuster la position du curseur en fonction du changement de température
      const adjustment = tempChange * this.config.temperatureCursorSensitivity;
      this.temperatureCursor.position += adjustment;

      // Limiter la position du curseur
      this.temperatureCursor.position = Math.max(
        this.config.temperatureCursorMin,
        Math.min(this.config.temperatureCursorMax, this.temperatureCursor.position)
      );

      console.log(`Curseur de température ajusté selon la mémoire: ${this.temperatureCursor.position.toFixed(3)} (changement: ${adjustment.toFixed(3)})`);
    }
  }

  /**
   * Calcule les seuils de température dynamiques en fonction de la position du curseur
   * @returns {Object} - Seuils de température
   */
  getTemperatureThresholds() {
    if (!this.config.temperatureCursorEnabled) {
      // Utiliser les seuils fixes si le curseur est désactivé
      return {
        instant: this.config.hotThreshold,    // 0.8
        shortTerm: this.config.warmThreshold, // 0.6
        working: this.config.coolThreshold,   // 0.4
        mediumTerm: this.config.coldThreshold, // 0.2
        longTerm: 0.1
      };
    }

    // Calculer les seuils en fonction de la position du curseur
    const cursorPos = this.temperatureCursor.position;
    const spacing = this.temperatureCursor.zoneSpacing;

    // Calculer les seuils pour chaque zone
    const thresholds = {
      instant: Math.min(0.95, cursorPos + spacing * 2),
      shortTerm: Math.min(0.9, cursorPos + spacing),
      working: cursorPos,
      mediumTerm: Math.max(0.1, cursorPos - spacing),
      longTerm: Math.max(0.05, cursorPos - spacing * 2)
    };

    return thresholds;
  }

  /**
   * Démarre le cycle de mémoire périodique
   */
  startMemoryCycle() {
    // Exécuter un cycle immédiatement
    this.performMemoryCycle();

    // Planifier les cycles suivants
    setInterval(() => {
      this.performMemoryCycle();
    }, this.config.memoryCycleInterval * 1000);
  }

  /**
   * Génère un rêve à partir des informations en mémoire
   * @returns {Object} - Le rêve généré
   */
  generateDream() {
    // Récupérer des entrées aléatoires de différents niveaux de mémoire
    const entries = [
      ...this.getRandomEntries(this.dreamMemory, 2),
      ...this.getRandomEntries(this.longTerm, 3),
      ...this.getRandomEntries(this.mediumTerm, 2),
      ...this.getRandomEntries(this.workingMemory, 1)
    ];

    if (entries.length === 0) {
      return {
        id: `dream_${Date.now()}`,
        key: `Rêve vide (${new Date().toLocaleString()})`,
        data: "Aucun rêve généré - mémoire insuffisante",
        category: 'dream',
        created: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
        temperature: 0.3,
        importance: 0.3
      };
    }

    // Trouver des connexions entre les entrées
    const connections = [];
    for (let i = 0; i < entries.length; i++) {
      for (let j = i + 1; j < entries.length; j++) {
        // Vérifier si les entrées ont des mots-clés en commun
        const entryA = entries[i];
        const entryB = entries[j];

        // Extraire les mots-clés des entrées
        const keywordsA = this.extractKeywords(entryA);
        const keywordsB = this.extractKeywords(entryB);

        // Trouver les mots-clés communs
        const commonKeywords = keywordsA.filter(keyword => keywordsB.includes(keyword));

        if (commonKeywords.length > 0) {
          connections.push({
            from: entryA.id,
            to: entryB.id,
            keywords: commonKeywords,
            strength: commonKeywords.length / Math.max(keywordsA.length, keywordsB.length)
          });
        }
      }
    }

    // Créer un récit de rêve basé sur les entrées et les connexions
    let dreamNarrative = "";

    // Utiliser les accélérateurs Kyber pour améliorer la génération de rêve
    let boostFactor = 1.0;
    if (this.kyberAccelerators && this.kyberAccelerators.applyBoost) {
      boostFactor = this.kyberAccelerators.applyBoost('connection', 1.0);
    }

    // Générer un récit plus complexe si le facteur de boost est élevé
    if (boostFactor > 2.0) {
      // Récit complexe avec plusieurs connexions
      dreamNarrative = this.generateComplexDreamNarrative(entries, connections);
    } else {
      // Récit simple
      dreamNarrative = this.generateSimpleDreamNarrative(entries);
    }

    // Extraire les mots-clés du récit pour les connexions
    const dreamKeywords = this.extractKeywords({ data: dreamNarrative });

    // Créer l'entrée de rêve
    const dream = {
      id: `dream_${Date.now()}`,
      key: `Rêve du ${new Date().toLocaleString()}`,
      data: dreamNarrative,
      category: 'dream',
      created: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1,
      temperature: 0.3,
      importance: 0.3,
      connections: dreamKeywords,
      sourceEntries: entries.map(entry => entry.id),
      connectionStrength: connections.length
    };

    // Ajouter le rêve à la mémoire des rêves
    this.dreamMemory[dream.id] = dream;
    this.stats.dreamMemoryEntries++;
    this.stats.totalEntries++;

    return dream;
  }

  /**
   * Génère un récit de rêve simple
   * @param {Array} entries - Entrées de mémoire
   * @returns {string} - Récit de rêve
   */
  generateSimpleDreamNarrative(entries) {
    // Créer un récit simple à partir des entrées
    const fragments = entries.map(entry => {
      // Extraire le contenu de l'entrée
      let content = '';
      if (typeof entry.data === 'string') {
        content = entry.data;
      } else if (typeof entry.data === 'object' && entry.data !== null) {
        content = JSON.stringify(entry.data);
      }

      // Limiter la longueur du contenu
      content = content.substring(0, 100);

      return `${entry.key}: ${content}`;
    });

    // Mélanger les fragments
    const shuffledFragments = [...fragments].sort(() => 0.5 - Math.random());

    // Créer le récit
    return shuffledFragments.join('\n\n');
  }

  /**
   * Génère un récit de rêve complexe
   * @param {Array} entries - Entrées de mémoire
   * @param {Array} connections - Connexions entre les entrées
   * @returns {string} - Récit de rêve
   */
  generateComplexDreamNarrative(entries, connections) {
    // Créer un récit plus complexe avec des transitions
    let narrative = "Dans ce rêve, plusieurs éléments de la mémoire se sont connectés:\n\n";

    // Utiliser les connexions pour créer un récit cohérent
    if (connections.length > 0) {
      // Trier les connexions par force
      connections.sort((a, b) => b.strength - a.strength);

      // Utiliser les connexions les plus fortes
      const strongestConnections = connections.slice(0, Math.min(3, connections.length));

      strongestConnections.forEach(connection => {
        // Trouver les entrées correspondantes
        const fromEntry = entries.find(entry => entry.id === connection.from);
        const toEntry = entries.find(entry => entry.id === connection.to);

        if (fromEntry && toEntry) {
          // Créer une transition entre les entrées
          narrative += `Une connexion s'est formée entre "${fromEntry.key}" et "${toEntry.key}" `;
          narrative += `autour des concepts: ${connection.keywords.join(', ')}.\n\n`;

          // Ajouter des extraits des entrées
          let fromContent = typeof fromEntry.data === 'string' ? fromEntry.data : JSON.stringify(fromEntry.data);
          let toContent = typeof toEntry.data === 'string' ? toEntry.data : JSON.stringify(toEntry.data);

          narrative += `"${fromContent.substring(0, 50)}..." a fusionné avec "${toContent.substring(0, 50)}..."\n\n`;
        }
      });
    } else {
      // S'il n'y a pas de connexions, créer un récit à partir des entrées
      narrative += entries.map(entry => {
        let content = typeof entry.data === 'string' ? entry.data : JSON.stringify(entry.data);
        return `${entry.key}: "${content.substring(0, 100)}..."`;
      }).join('\n\n');
    }

    // Ajouter une conclusion
    narrative += "\n\nLe rêve s'est terminé avec une sensation de " +
                ["clarté", "confusion", "nostalgie", "espoir", "mystère"][Math.floor(Math.random() * 5)] + ".";

    return narrative;
  }

  /**
   * Extrait les mots-clés d'une entrée
   * @param {Object} entry - Entrée de mémoire
   * @returns {Array} - Mots-clés extraits
   */
  extractKeywords(entry) {
    // Extraire le texte de l'entrée
    let text = '';

    // Ajouter la clé
    if (entry.key) {
      text += entry.key + ' ';
    }

    // Ajouter les données
    if (typeof entry.data === 'string') {
      text += entry.data;
    } else if (typeof entry.data === 'object' && entry.data !== null) {
      text += JSON.stringify(entry.data);
    }

    // Convertir en minuscules
    text = text.toLowerCase();

    // Supprimer la ponctuation
    text = text.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, ' ');

    // Diviser en mots
    const words = text.split(/\s+/);

    // Filtrer les mots courts et les mots vides
    const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'de', 'du', 'ce', 'cette', 'ces', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'nos', 'votre', 'vos', 'leur', 'leurs', 'a', 'à', 'au', 'aux', 'avec', 'ce', 'ces', 'dans', 'en', 'entre', 'il', 'ils', 'je', 'tu', 'nous', 'vous', 'elle', 'elles', 'on', 'par', 'pas', 'pour', 'sur', 'the', 'of', 'and', 'to', 'in', 'is', 'that', 'it', 'with', 'for', 'as', 'was', 'on'];

    const keywords = words.filter(word =>
      word.length > 3 && !stopWords.includes(word)
    );

    // Supprimer les doublons
    return [...new Set(keywords)];
  }

  /**
   * Récupère des entrées aléatoires d'un niveau de mémoire
   * @param {Object} memoryLevel - Niveau de mémoire
   * @param {number} count - Nombre d'entrées à récupérer
   * @returns {Array} - Entrées aléatoires
   */
  getRandomEntries(memoryLevel, count) {
    const entries = Object.values(memoryLevel);
    if (entries.length === 0) return [];

    // Mélanger les entrées
    const shuffled = [...entries].sort(() => 0.5 - Math.random());

    // Récupérer le nombre demandé d'entrées
    return shuffled.slice(0, Math.min(count, entries.length));
  }

  /**
   * Réinitialise la mémoire
   */
  resetMemory() {
    this.instantMemory = {};
    this.shortTerm = {};
    this.workingMemory = {};
    this.mediumTerm = {};
    this.longTerm = {};
    this.dreamMemory = {};

    // Réinitialiser les statistiques
    this.stats = {
      totalEntries: 0,
      instantEntries: 0,
      shortTermEntries: 0,
      workingMemoryEntries: 0,
      mediumTermEntries: 0,
      longTermEntries: 0,
      dreamMemoryEntries: 0,
      cyclesPerformed: 0,
      lastCycleTime: null,
      averageTemperature: 0.5
    };

    // Sauvegarder la mémoire vide
    this.saveMemory();
  }

  /**
   * Récupère une entrée de mémoire par son ID
   * @param {string} id - ID de l'entrée
   * @returns {Object|null} - Entrée de mémoire ou null si non trouvée
   */
  getEntry(id) {
    // Chercher dans tous les niveaux de mémoire
    for (const level of [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm, this.dreamMemory]) {
      if (level[id]) {
        return level[id];
      }
    }

    return null;
  }

  /**
   * Récupère toutes les entrées de mémoire
   * @returns {Array} - Toutes les entrées de mémoire
   */
  getAllMemories() {
    const allEntries = [];

    // Récupérer les entrées de tous les niveaux de mémoire
    for (const level of [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm, this.dreamMemory]) {
      allEntries.push(...Object.values(level));
    }

    return allEntries;
  }

  /**
   * Ajoute une entrée existante à la mémoire thermique
   * @param {Object} entry - Entrée à ajouter
   * @returns {string} - ID de l'entrée
   */
  addExistingEntry(entry) {
    if (!entry || !entry.id) {
      throw new Error('Entrée invalide');
    }

    // Vérifier si l'entrée existe déjà
    const existingEntry = this.getEntry(entry.id);
    if (existingEntry) {
      console.log(`L'entrée ${entry.id} existe déjà, mise à jour...`);

      // Mettre à jour l'entrée existante
      existingEntry.data = entry.data;
      existingEntry.temperature = entry.temperature;
      existingEntry.importance = entry.importance;
      existingEntry.category = entry.category;
      existingEntry.metadata = entry.metadata;
      existingEntry.lastAccessed = Date.now();
      existingEntry.accessCount++;

      return existingEntry.id;
    }

    // Déterminer le niveau de mémoire en fonction de la température
    const thresholds = this.getTemperatureThresholds();

    if (entry.temperature >= thresholds.instant) {
      // Mémoire instantanée pour les informations très importantes
      this.instantMemory[entry.id] = entry;
      this.stats.instantEntries++;
    } else if (entry.temperature >= thresholds.shortTerm) {
      // Mémoire à court terme pour les informations importantes
      this.shortTerm[entry.id] = entry;
      this.stats.shortTermEntries++;
    } else if (entry.temperature >= thresholds.working) {
      // Mémoire de travail pour les informations standard
      this.workingMemory[entry.id] = entry;
      this.stats.workingMemoryEntries++;
    } else if (entry.temperature >= thresholds.mediumTerm) {
      // Mémoire à moyen terme
      this.mediumTerm[entry.id] = entry;
      this.stats.mediumTermEntries++;
    } else {
      // Mémoire à long terme pour les informations moins importantes
      this.longTerm[entry.id] = entry;
      this.stats.longTermEntries++;
    }

    // Mettre à jour les statistiques
    this.stats.totalEntries++;

    // Sauvegarder la mémoire
    this.saveMemory();

    return entry.id;
  }

  /**
   * Récupère les statistiques de la mémoire
   * @returns {Object} - Statistiques de la mémoire
   */
  getMemoryStats() {
    // Obtenir les seuils de température actuels
    const thresholds = this.getTemperatureThresholds();

    // Obtenir les températures du système
    const systemTemperatures = this.systemTemperature.getTemperatures();

    // Obtenir les métriques de performance du système d'apprentissage
    const learningMetrics = this.learningSystem.getPerformanceMetrics();

    return {
      totalMemories: this.stats.totalEntries,
      zone1Count: this.stats.instantEntries,
      zone2Count: this.stats.shortTermEntries,
      zone3Count: this.stats.workingMemoryEntries,
      zone4Count: this.stats.mediumTermEntries,
      zone5Count: this.stats.longTermEntries,
      zone6Count: this.stats.dreamMemoryEntries,
      averageTemperature: this.stats.averageTemperature,
      cyclesPerformed: this.stats.cyclesPerformed,
      lastCycleTime: this.stats.lastCycleTime ? new Date(this.stats.lastCycleTime).toLocaleString() : 'Jamais',
      temperatureCursor: this.temperatureCursor.position,
      temperatureThresholds: thresholds,
      systemTemperatures: {
        cpu: systemTemperatures.cpu,
        gpu: systemTemperatures.gpu,
        memory: systemTemperatures.memory,
        average: systemTemperatures.average,
        normalized: this.systemTemperature.getNormalizedTemperature()
      },
      useRealTemperatures: this.useRealTemperatures,
      autoEvolution: {
        enabled: this.autoEvolutionEnabled,
        adaptationEnabled: this.learningSystem.config.adaptationEnabled,
        performanceMetrics: learningMetrics,
        parameters: {
          temperatureCursorSensitivity: this.config.temperatureCursorSensitivity,
          memoryDecayRate: this.config.memoryDecayRate,
          importanceFactor: this.config.importanceFactor,
          accessFactor: this.config.accessFactor,
          decayFactor: this.config.decayFactor
        }
      }
    };
  }

  /**
   * Récupère la position du curseur de température
   * @returns {number} - Position du curseur (0-1)
   */
  getTemperatureCursorPosition() {
    return this.temperatureCursor.position;
  }

  /**
   * Définit la position du curseur de température
   * @param {number} position - Nouvelle position du curseur (0-1)
   */
  setTemperatureCursorPosition(position) {
    // Limiter la position du curseur
    this.temperatureCursor.position = Math.max(
      this.config.temperatureCursorMin,
      Math.min(this.config.temperatureCursorMax, position)
    );

    console.log(`Position du curseur de température définie à ${this.temperatureCursor.position.toFixed(3)}`);

    // Mettre à jour les statistiques
    this.updateStats();

    // Exécuter un cycle de mémoire pour appliquer les nouveaux seuils
    this.performMemoryCycle();
  }

  /**
   * Active ou désactive l'utilisation des températures réelles du système
   * @param {boolean} useRealTemps - Utiliser les températures réelles
   */
  setUseRealTemperatures(useRealTemps) {
    this.useRealTemperatures = useRealTemps;

    console.log(`Utilisation des températures réelles du système: ${this.useRealTemperatures ? 'activée' : 'désactivée'}`);

    // Si on active les températures réelles, mettre à jour immédiatement le curseur
    if (this.useRealTemperatures) {
      const systemTemp = this.systemTemperature.getNormalizedTemperature();
      this.temperatureCursor.position = Math.max(
        this.config.temperatureCursorMin,
        Math.min(this.config.temperatureCursorMax, systemTemp)
      );

      console.log(`Curseur de température ajusté selon la température système: ${this.temperatureCursor.position.toFixed(3)} (température CPU: ${this.systemTemperature.getTemperatures().cpu.toFixed(1)}°C)`);
    }

    // Mettre à jour les statistiques
    this.updateStats();

    // Exécuter un cycle de mémoire pour appliquer les nouveaux seuils
    this.performMemoryCycle();

    return this.useRealTemperatures;
  }

  /**
   * Récupère les températures du système
   * @returns {Object} - Températures du système
   */
  getSystemTemperatures() {
    return {
      ...this.systemTemperature.getTemperatures(),
      normalized: this.systemTemperature.getNormalizedTemperature()
    };
  }

  /**
   * Active ou désactive l'auto-évolution
   * @param {boolean} enabled - Activer l'auto-évolution
   * @returns {boolean} - État actuel de l'auto-évolution
   */
  setAutoEvolutionEnabled(enabled) {
    this.autoEvolutionEnabled = enabled;
    console.log(`Auto-évolution ${enabled ? 'activée' : 'désactivée'}`);
    return this.autoEvolutionEnabled;
  }

  /**
   * Active ou désactive l'adaptation automatique du système d'apprentissage
   * @param {boolean} enabled - Activer l'adaptation
   * @returns {boolean} - État actuel de l'adaptation
   */
  setAdaptationEnabled(enabled) {
    return this.learningSystem.setAdaptationEnabled(enabled);
  }

  /**
   * Force une optimisation immédiate des paramètres
   * @returns {Object} - Paramètres optimisés
   */
  forceOptimization() {
    console.log('Forçage de l\'optimisation des paramètres...');

    // Récupérer les statistiques de mémoire
    const memoryStats = this.getMemoryStats();

    // Récupérer les températures du système
    const systemTemperatures = this.getSystemTemperatures();

    // Récupérer les paramètres actuels
    const currentParameters = {
      temperatureCursorSensitivity: this.config.temperatureCursorSensitivity,
      memoryDecayRate: this.config.memoryDecayRate,
      importanceFactor: this.config.importanceFactor,
      accessFactor: this.config.accessFactor,
      decayFactor: this.config.decayFactor
    };

    // Enregistrer les données du cycle actuel
    this.learningSystem.recordCycleData(memoryStats, systemTemperatures, currentParameters);

    // Forcer une optimisation
    const optimizedParameters = this.learningSystem.optimizeParameters();

    // Appliquer les paramètres optimisés
    this.config.temperatureCursorSensitivity = optimizedParameters.temperatureCursorSensitivity;
    this.config.memoryDecayRate = optimizedParameters.memoryDecayRate;
    this.config.importanceFactor = optimizedParameters.importanceFactor;
    this.config.accessFactor = optimizedParameters.accessFactor;
    this.config.decayFactor = optimizedParameters.decayFactor;

    // Mettre à jour les métriques de performance
    this.stats.performanceMetrics = this.learningSystem.getPerformanceMetrics();

    console.log('Optimisation forcée terminée');

    return optimizedParameters;
  }

  /**
   * Récupère les métriques de performance du système d'apprentissage
   * @returns {Object} - Métriques de performance
   */
  getLearningPerformanceMetrics() {
    return this.learningSystem.getPerformanceMetrics();
  }

  /**
   * Recherche sémantique avancée dans la mémoire
   * @param {string} query - Requête de recherche
   * @param {Object} options - Options de recherche
   * @returns {Array} - Résultats de recherche triés par pertinence
   */
  search(query, options = {}) {
    const {
      limit = 10,
      minTemperature = 0.1,
      zones = ['instant', 'shortTerm', 'workingMemory', 'mediumTerm', 'longTerm', 'dreamMemory'],
      categories = [],
      sortBy = 'relevance', // 'relevance', 'temperature', 'date', 'access'
      includeMetadata = false
    } = options;

    // Récupérer toutes les entrées des zones spécifiées
    let allEntries = [];
    zones.forEach(zone => {
      allEntries.push(...this.getEntriesFromZone(zone));
    });

    // Filtrer par température minimale
    allEntries = allEntries.filter(entry => entry.temperature >= minTemperature);

    // Filtrer par catégories si spécifiées
    if (categories.length > 0) {
      allEntries = allEntries.filter(entry => categories.includes(entry.category));
    }

    // Calculer la pertinence pour chaque entrée
    const results = allEntries.map(entry => {
      const relevance = this.calculateRelevance(query, entry);
      return {
        entry,
        relevance,
        score: relevance * entry.temperature, // Score combiné
        metadata: includeMetadata ? this.getEntryMetadata(entry) : undefined
      };
    });

    // Filtrer les résultats non pertinents
    const filteredResults = results.filter(result => result.relevance > 0);

    // Trier selon le critère spécifié
    filteredResults.sort((a, b) => {
      switch (sortBy) {
        case 'temperature':
          return b.entry.temperature - a.entry.temperature;
        case 'date':
          return b.entry.created - a.entry.created;
        case 'access':
          return b.entry.lastAccessed - a.entry.lastAccessed;
        case 'relevance':
        default:
          return b.score - a.score;
      }
    });

    // Limiter les résultats
    return filteredResults.slice(0, limit);
  }

  /**
   * Calcule la pertinence d'une entrée par rapport à une requête
   * @param {string} query - Requête de recherche
   * @param {Object} entry - Entrée de mémoire
   * @returns {number} - Score de pertinence (0-1)
   */
  calculateRelevance(query, entry) {
    const queryWords = this.extractKeywords({ data: query });
    const entryWords = this.extractKeywords(entry);

    if (queryWords.length === 0 || entryWords.length === 0) {
      return 0;
    }

    // Calculer la correspondance exacte
    const exactMatches = queryWords.filter(word => entryWords.includes(word));
    const exactScore = exactMatches.length / queryWords.length;

    // Calculer la correspondance partielle
    let partialScore = 0;
    queryWords.forEach(queryWord => {
      entryWords.forEach(entryWord => {
        if (queryWord.includes(entryWord) || entryWord.includes(queryWord)) {
          partialScore += 0.5 / queryWords.length;
        }
      });
    });

    // Calculer la correspondance dans la clé (plus importante)
    const keyRelevance = entry.key.toLowerCase().includes(query.toLowerCase()) ? 0.3 : 0;

    // Score final combiné
    return Math.min(1, exactScore + partialScore * 0.5 + keyRelevance);
  }

  /**
   * Obtient les métadonnées d'une entrée
   * @param {Object} entry - Entrée de mémoire
   * @returns {Object} - Métadonnées de l'entrée
   */
  getEntryMetadata(entry) {
    const zone = this.getEntryZone(entry.id);
    const age = Date.now() - entry.created;
    const lastAccessAge = Date.now() - entry.lastAccessed;

    return {
      zone,
      age: this.formatDuration(age),
      lastAccess: this.formatDuration(lastAccessAge),
      accessFrequency: entry.accessCount / Math.max(1, age / (1000 * 60 * 60 * 24)), // accès par jour
      temperatureHistory: entry.temperatureHistory || [],
      keywords: this.extractKeywords(entry).slice(0, 5)
    };
  }

  /**
   * Détermine la zone d'une entrée
   * @param {string} entryId - ID de l'entrée
   * @returns {string} - Nom de la zone
   */
  getEntryZone(entryId) {
    if (this.instantMemory[entryId]) return 'instant';
    if (this.shortTerm[entryId]) return 'shortTerm';
    if (this.workingMemory[entryId]) return 'workingMemory';
    if (this.mediumTerm[entryId]) return 'mediumTerm';
    if (this.longTerm[entryId]) return 'longTerm';
    if (this.dreamMemory[entryId]) return 'dreamMemory';
    return 'unknown';
  }

  /**
   * Formate une durée en texte lisible
   * @param {number} milliseconds - Durée en millisecondes
   * @returns {string} - Durée formatée
   */
  formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}j ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  /**
   * Analyse les patterns et connexions dans la mémoire
   * @returns {Object} - Analyse des patterns
   */
  analyzePatterns() {
    const allEntries = this.getAllEntries();

    // Analyser les catégories
    const categoryStats = {};
    const keywordFrequency = {};
    const temporalPatterns = {};

    allEntries.forEach(entry => {
      // Statistiques par catégorie
      categoryStats[entry.category] = (categoryStats[entry.category] || 0) + 1;

      // Fréquence des mots-clés
      const keywords = this.extractKeywords(entry);
      keywords.forEach(keyword => {
        keywordFrequency[keyword] = (keywordFrequency[keyword] || 0) + 1;
      });

      // Patterns temporels
      const hour = new Date(entry.created).getHours();
      temporalPatterns[hour] = (temporalPatterns[hour] || 0) + 1;
    });

    // Trouver les connexions entre entrées
    const connections = this.findConnections(allEntries);

    // Identifier les clusters
    const clusters = this.identifyClusters(allEntries);

    return {
      totalEntries: allEntries.length,
      categories: categoryStats,
      topKeywords: Object.entries(keywordFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20)
        .map(([keyword, count]) => ({ keyword, count })),
      temporalPatterns,
      connections: connections.slice(0, 50), // Top 50 connexions
      clusters: clusters.slice(0, 10), // Top 10 clusters
      insights: this.generateInsights(categoryStats, keywordFrequency, temporalPatterns)
    };
  }

  /**
   * Trouve les connexions entre les entrées
   * @param {Array} entries - Entrées à analyser
   * @returns {Array} - Connexions trouvées
   */
  findConnections(entries) {
    const connections = [];

    for (let i = 0; i < entries.length; i++) {
      for (let j = i + 1; j < entries.length; j++) {
        const entryA = entries[i];
        const entryB = entries[j];

        const keywordsA = this.extractKeywords(entryA);
        const keywordsB = this.extractKeywords(entryB);

        const commonKeywords = keywordsA.filter(keyword => keywordsB.includes(keyword));

        if (commonKeywords.length > 0) {
          const strength = commonKeywords.length / Math.max(keywordsA.length, keywordsB.length);

          connections.push({
            from: entryA.id,
            to: entryB.id,
            strength,
            commonKeywords,
            fromZone: this.getEntryZone(entryA.id),
            toZone: this.getEntryZone(entryB.id)
          });
        }
      }
    }

    return connections.sort((a, b) => b.strength - a.strength);
  }

  /**
   * Identifie les clusters d'entrées similaires
   * @param {Array} entries - Entrées à analyser
   * @returns {Array} - Clusters identifiés
   */
  identifyClusters(entries) {
    const clusters = [];
    const processed = new Set();

    entries.forEach(entry => {
      if (processed.has(entry.id)) return;

      const cluster = {
        id: `cluster_${clusters.length}`,
        entries: [entry],
        keywords: this.extractKeywords(entry),
        category: entry.category,
        averageTemperature: entry.temperature
      };

      // Trouver les entrées similaires
      entries.forEach(otherEntry => {
        if (otherEntry.id === entry.id || processed.has(otherEntry.id)) return;

        const similarity = this.calculateSimilarity(entry, otherEntry);

        if (similarity > 0.3) { // Seuil de similarité
          cluster.entries.push(otherEntry);
          processed.add(otherEntry.id);

          // Mettre à jour les mots-clés du cluster
          const otherKeywords = this.extractKeywords(otherEntry);
          cluster.keywords = [...new Set([...cluster.keywords, ...otherKeywords])];

          // Mettre à jour la température moyenne
          cluster.averageTemperature = cluster.entries.reduce((sum, e) => sum + e.temperature, 0) / cluster.entries.length;
        }
      });

      processed.add(entry.id);

      if (cluster.entries.length > 1) {
        clusters.push(cluster);
      }
    });

    return clusters.sort((a, b) => b.entries.length - a.entries.length);
  }

  /**
   * Calcule la similarité entre deux entrées
   * @param {Object} entryA - Première entrée
   * @param {Object} entryB - Deuxième entrée
   * @returns {number} - Score de similarité (0-1)
   */
  calculateSimilarity(entryA, entryB) {
    // Similarité par catégorie
    const categoryMatch = entryA.category === entryB.category ? 0.3 : 0;

    // Similarité par mots-clés
    const keywordsA = this.extractKeywords(entryA);
    const keywordsB = this.extractKeywords(entryB);
    const commonKeywords = keywordsA.filter(keyword => keywordsB.includes(keyword));
    const keywordSimilarity = commonKeywords.length / Math.max(keywordsA.length, keywordsB.length, 1);

    // Similarité temporelle
    const timeDiff = Math.abs(entryA.created - entryB.created);
    const maxTimeDiff = 7 * 24 * 60 * 60 * 1000; // 7 jours
    const temporalSimilarity = Math.max(0, 1 - (timeDiff / maxTimeDiff));

    return categoryMatch + keywordSimilarity * 0.5 + temporalSimilarity * 0.2;
  }

  /**
   * Génère des insights à partir des patterns analysés
   * @param {Object} categoryStats - Statistiques par catégorie
   * @param {Object} keywordFrequency - Fréquence des mots-clés
   * @param {Object} temporalPatterns - Patterns temporels
   * @returns {Array} - Insights générés
   */
  generateInsights(categoryStats, keywordFrequency, temporalPatterns) {
    const insights = [];

    // Insight sur la catégorie dominante
    const topCategory = Object.entries(categoryStats)
      .sort(([,a], [,b]) => b - a)[0];
    if (topCategory) {
      insights.push({
        type: 'category_dominance',
        message: `La catégorie "${topCategory[0]}" représente ${Math.round(topCategory[1] / Object.values(categoryStats).reduce((a, b) => a + b, 0) * 100)}% des souvenirs`,
        importance: 0.7
      });
    }

    // Insight sur l'activité temporelle
    const peakHour = Object.entries(temporalPatterns)
      .sort(([,a], [,b]) => b - a)[0];
    if (peakHour) {
      insights.push({
        type: 'temporal_pattern',
        message: `L'heure de pic d'activité est ${peakHour[0]}h avec ${peakHour[1]} souvenirs créés`,
        importance: 0.5
      });
    }

    // Insight sur les mots-clés récurrents
    const topKeywords = Object.entries(keywordFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);
    if (topKeywords.length > 0) {
      insights.push({
        type: 'keyword_frequency',
        message: `Les concepts les plus fréquents sont: ${topKeywords.map(([k]) => k).join(', ')}`,
        importance: 0.6
      });
    }

    return insights.sort((a, b) => b.importance - a.importance);
  }

  /**
   * Récupère les insights découverts par le système d'apprentissage
   * @returns {Object} - Insights découverts
   */
  getLearningInsights() {
    return this.learningSystem.getInsights();
  }

  /**
   * Active ou désactive le mode sommeil
   * @param {boolean} enabled - Activer le mode sommeil
   * @returns {boolean} - État actuel du mode sommeil
   */
  setSleepModeEnabled(enabled) {
    this.sleepMode.enabled = enabled;

    if (!enabled && this.sleepMode.active) {
      this.exitSleepMode();
    }

    console.log(`Mode sommeil ${enabled ? 'activé' : 'désactivé'}`);
    return this.sleepMode.enabled;
  }

  /**
   * Signale une activité utilisateur
   * Réinitialise le compteur d'inactivité et sort du mode sommeil si actif
   */
  signalActivity() {
    this.sleepMode.lastActivity = Date.now();

    // Si en mode sommeil, en sortir
    if (this.sleepMode.active) {
      this.exitSleepMode();
    }
  }

  /**
   * Vérifie si le système doit entrer en mode sommeil
   * Appelé périodiquement par le cycle de mémoire
   */
  checkSleepMode() {
    if (!this.sleepMode.enabled || this.sleepMode.active) {
      return;
    }

    const inactiveTime = Date.now() - this.sleepMode.lastActivity;

    if (inactiveTime >= this.sleepMode.inactivityThreshold) {
      this.enterSleepMode();
    }
  }

  /**
   * Entre en mode sommeil
   * Démarre le processus de consolidation de mémoire
   */
  enterSleepMode() {
    if (this.sleepMode.active) {
      return;
    }

    console.log('Entrée en mode sommeil...');

    this.sleepMode.active = true;
    this.sleepMode.consolidationCount = 0;

    // Démarrer le timer de consolidation
    this.sleepMode.consolidationTimer = setInterval(() => {
      this.performMemoryConsolidation();
    }, this.sleepMode.consolidationInterval);

    // Émettre un événement
    this.emit('sleepModeEntered');
  }

  /**
   * Sort du mode sommeil
   * Arrête le processus de consolidation de mémoire
   */
  exitSleepMode() {
    if (!this.sleepMode.active) {
      return;
    }

    console.log('Sortie du mode sommeil...');

    this.sleepMode.active = false;

    // Arrêter le timer de consolidation
    if (this.sleepMode.consolidationTimer) {
      clearInterval(this.sleepMode.consolidationTimer);
      this.sleepMode.consolidationTimer = null;
    }

    // Émettre un événement
    this.emit('sleepModeExited', {
      consolidationCount: this.sleepMode.consolidationCount,
      duration: Date.now() - this.sleepMode.lastActivity
    });
  }

  /**
   * Effectue une consolidation de mémoire
   * Renforce les connexions entre les entrées liées et optimise la structure de la mémoire
   */
  performMemoryConsolidation() {
    if (!this.sleepMode.active) {
      return;
    }

    console.log('Consolidation de mémoire en cours...');

    // 1. Identifier les entrées similaires dans différentes zones
    const similarEntries = this.findSimilarEntries();

    // 2. Renforcer les connexions entre les entrées similaires
    this.reinforceConnections(similarEntries);

    // 3. Réorganiser les entrées en fonction de leur importance
    this.reorganizeByImportance();

    // 4. Nettoyer les entrées redondantes ou obsolètes
    this.cleanupRedundantEntries();

    // Mettre à jour les statistiques
    this.sleepMode.consolidationCount++;
    this.sleepMode.lastConsolidation = Date.now();

    // Sauvegarder la mémoire
    this.saveMemory();

    console.log(`Consolidation de mémoire terminée (${this.sleepMode.consolidationCount})`);

    // Émettre un événement
    this.emit('memoryConsolidated', {
      consolidationCount: this.sleepMode.consolidationCount,
      similarEntries: similarEntries.length
    });
  }

  /**
   * Trouve les entrées similaires dans différentes zones de mémoire
   * @returns {Array} - Paires d'entrées similaires
   */
  findSimilarEntries() {
    const similarPairs = [];

    // Récupérer toutes les entrées
    const allEntries = this.getAllEntries();

    // Comparer chaque paire d'entrées
    for (let i = 0; i < allEntries.length; i++) {
      for (let j = i + 1; j < allEntries.length; j++) {
        const entry1 = allEntries[i];
        const entry2 = allEntries[j];

        // Calculer la similarité entre les entrées
        const similarity = this.calculateSimilarity(entry1, entry2);

        // Si la similarité est suffisante, ajouter la paire
        if (similarity > 0.7) {
          similarPairs.push({
            entry1,
            entry2,
            similarity
          });
        }
      }
    }

    return similarPairs;
  }

  /**
   * Calcule la similarité entre deux entrées de mémoire
   * @param {Object} entry1 - Première entrée
   * @param {Object} entry2 - Deuxième entrée
   * @returns {number} - Score de similarité (0-1)
   */
  calculateSimilarity(entry1, entry2) {
    // Similarité basée sur les mots-clés
    let keywordSimilarity = 0;
    if (entry1.keywords && entry2.keywords) {
      const keywords1 = new Set(entry1.keywords);
      const keywords2 = new Set(entry2.keywords);

      // Calculer l'intersection
      const intersection = new Set([...keywords1].filter(x => keywords2.has(x)));

      // Calculer la similarité de Jaccard
      keywordSimilarity = intersection.size / (keywords1.size + keywords2.size - intersection.size);
    }

    // Similarité basée sur le contenu (simple)
    let contentSimilarity = 0;
    if (entry1.content && entry2.content) {
      // Compter les mots communs
      const words1 = entry1.content.toLowerCase().split(/\s+/);
      const words2 = entry2.content.toLowerCase().split(/\s+/);

      const wordSet1 = new Set(words1);
      const wordSet2 = new Set(words2);

      // Calculer l'intersection
      const intersection = new Set([...wordSet1].filter(x => wordSet2.has(x)));

      // Calculer la similarité de Jaccard
      contentSimilarity = intersection.size / (wordSet1.size + wordSet2.size - intersection.size);
    }

    // Similarité basée sur la source
    const sourceSimilarity = entry1.source === entry2.source ? 1 : 0;

    // Similarité globale (moyenne pondérée)
    return (keywordSimilarity * 0.4) + (contentSimilarity * 0.5) + (sourceSimilarity * 0.1);
  }

  /**
   * Renforce les connexions entre les entrées similaires
   * @param {Array} similarPairs - Paires d'entrées similaires
   */
  reinforceConnections(similarPairs) {
    for (const pair of similarPairs) {
      const { entry1, entry2, similarity } = pair;

      // Augmenter l'importance des entrées en fonction de leur similarité
      const importanceBoost = similarity * 0.1;

      entry1.importance = Math.min(1, entry1.importance + importanceBoost);
      entry2.importance = Math.min(1, entry2.importance + importanceBoost);

      // Créer ou renforcer les connexions entre les entrées
      if (!entry1.connections) entry1.connections = [];
      if (!entry2.connections) entry2.connections = [];

      // Vérifier si la connexion existe déjà
      const connection1 = entry1.connections.find(c => c.id === entry2.id);
      const connection2 = entry2.connections.find(c => c.id === entry1.id);

      if (connection1) {
        // Renforcer la connexion existante
        connection1.strength = Math.min(1, connection1.strength + similarity * 0.2);
      } else {
        // Créer une nouvelle connexion
        entry1.connections.push({
          id: entry2.id,
          strength: similarity,
          timestamp: Date.now()
        });
      }

      if (connection2) {
        // Renforcer la connexion existante
        connection2.strength = Math.min(1, connection2.strength + similarity * 0.2);
      } else {
        // Créer une nouvelle connexion
        entry2.connections.push({
          id: entry1.id,
          strength: similarity,
          timestamp: Date.now()
        });
      }

      // Mettre à jour les entrées
      this.updateEntry(entry1);
      this.updateEntry(entry2);
    }
  }

  /**
   * Réorganise les entrées en fonction de leur importance
   */
  reorganizeByImportance() {
    // Récupérer toutes les entrées
    const allEntries = this.getAllEntries();

    // Trier les entrées par importance
    allEntries.sort((a, b) => b.importance - a.importance);

    // Réorganiser les entrées
    for (let i = 0; i < allEntries.length; i++) {
      const entry = allEntries[i];

      // Déterminer la zone cible en fonction de l'importance
      let targetZone;

      if (i < allEntries.length * 0.1) {
        // Top 10% -> Mémoire de travail
        targetZone = 3;
      } else if (i < allEntries.length * 0.3) {
        // Top 30% -> Mémoire à moyen terme
        targetZone = 4;
      } else if (i < allEntries.length * 0.7) {
        // Top 70% -> Mémoire à long terme
        targetZone = 5;
      } else {
        // Reste -> Mémoire des rêves
        targetZone = 6;
      }

      // Déplacer l'entrée vers la zone cible si nécessaire
      const currentZone = this.getEntryZone(entry);

      if (currentZone !== targetZone) {
        this.moveEntryToZone(entry, targetZone);
      }
    }
  }

  /**
   * Nettoie les entrées redondantes ou obsolètes
   */
  cleanupRedundantEntries() {
    // Récupérer toutes les entrées
    const allEntries = this.getAllEntries();

    // Identifier les entrées très similaires
    const redundantGroups = [];
    const processedEntries = new Set();

    for (let i = 0; i < allEntries.length; i++) {
      const entry1 = allEntries[i];

      if (processedEntries.has(entry1.id)) {
        continue;
      }

      const similarEntries = [entry1];

      for (let j = i + 1; j < allEntries.length; j++) {
        const entry2 = allEntries[j];

        if (processedEntries.has(entry2.id)) {
          continue;
        }

        // Calculer la similarité
        const similarity = this.calculateSimilarity(entry1, entry2);

        // Si très similaire, ajouter au groupe
        if (similarity > 0.9) {
          similarEntries.push(entry2);
          processedEntries.add(entry2.id);
        }
      }

      if (similarEntries.length > 1) {
        redundantGroups.push(similarEntries);
      }

      processedEntries.add(entry1.id);
    }

    // Fusionner les entrées redondantes
    for (const group of redundantGroups) {
      // Trier par importance
      group.sort((a, b) => b.importance - a.importance);

      // Garder l'entrée la plus importante
      const mainEntry = group[0];

      // Fusionner les informations des autres entrées
      for (let i = 1; i < group.length; i++) {
        const redundantEntry = group[i];

        // Augmenter l'importance
        mainEntry.importance = Math.min(1, mainEntry.importance + (redundantEntry.importance * 0.2));

        // Fusionner les connexions
        if (redundantEntry.connections) {
          if (!mainEntry.connections) mainEntry.connections = [];

          for (const connection of redundantEntry.connections) {
            // Vérifier si la connexion existe déjà
            const existingConnection = mainEntry.connections.find(c => c.id === connection.id);

            if (existingConnection) {
              // Renforcer la connexion existante
              existingConnection.strength = Math.min(1, existingConnection.strength + (connection.strength * 0.5));
            } else {
              // Ajouter la nouvelle connexion
              mainEntry.connections.push({
                id: connection.id,
                strength: connection.strength,
                timestamp: Date.now()
              });
            }
          }
        }

        // Supprimer l'entrée redondante
        this.removeEntry(redundantEntry.id);
      }

      // Mettre à jour l'entrée principale
      this.updateEntry(mainEntry);
    }
  }

  /**
   * Récupère l'état du mode sommeil
   * @returns {Object} - État du mode sommeil
   */
  getSleepModeStatus() {
    return {
      enabled: this.sleepMode.enabled,
      active: this.sleepMode.active,
      lastActivity: this.sleepMode.lastActivity,
      inactivityThreshold: this.sleepMode.inactivityThreshold,
      consolidationCount: this.sleepMode.consolidationCount,
      lastConsolidation: this.sleepMode.lastConsolidation
    };
  }

  /**
   * Active ou désactive le mode sommeil
   * @param {boolean} enabled - Activer ou désactiver le mode sommeil
   * @returns {boolean} - Nouvel état du mode sommeil
   */
  setSleepModeEnabled(enabled) {
    this.sleepMode.enabled = enabled;

    if (!enabled && this.sleepMode.active) {
      // Si on désactive le mode sommeil et qu'il est actif, le quitter
      this.exitSleepMode();
    }

    console.log(`Mode sommeil ${enabled ? 'activé' : 'désactivé'}`);
    return enabled;
  }

  /**
   * Signale une activité utilisateur
   */
  signalActivity() {
    this.sleepMode.lastActivity = Date.now();

    if (this.sleepMode.active) {
      this.exitSleepMode();
    }
  }

  /**
   * Force une consolidation de mémoire
   */
  forceMemoryConsolidation() {
    const wasActive = this.sleepMode.active;

    // Activer temporairement le mode sommeil pour la consolidation
    if (!wasActive) {
      this.sleepMode.active = true;
    }

    // Effectuer la consolidation
    this.performMemoryConsolidation();

    // Restaurer l'état précédent
    if (!wasActive) {
      this.sleepMode.active = false;
    }
  }

  /**
   * Met à jour une entrée existante
   * @param {Object} entry - Entrée à mettre à jour
   */
  updateEntry(entry) {
    // Trouver la zone actuelle de l'entrée
    const currentZone = this.getEntryZone(entry);

    if (currentZone) {
      // Mettre à jour l'entrée dans sa zone actuelle
      switch (currentZone) {
        case 1:
          this.instantMemory[entry.id] = entry;
          break;
        case 2:
          this.shortTerm[entry.id] = entry;
          break;
        case 3:
          this.workingMemory[entry.id] = entry;
          break;
        case 4:
          this.mediumTerm[entry.id] = entry;
          break;
        case 5:
          this.longTerm[entry.id] = entry;
          break;
        case 6:
          this.dreamMemory[entry.id] = entry;
          break;
      }
    }
  }

  /**
   * Déplace une entrée vers une zone spécifique
   * @param {Object} entry - Entrée à déplacer
   * @param {number} targetZone - Zone cible (1-6)
   */
  moveEntryToZone(entry, targetZone) {
    // Supprimer l'entrée de sa zone actuelle
    this.removeEntry(entry.id);

    // Ajouter l'entrée à la zone cible
    switch (targetZone) {
      case 1:
        this.instantMemory[entry.id] = entry;
        this.stats.instantEntries++;
        break;
      case 2:
        this.shortTerm[entry.id] = entry;
        this.stats.shortTermEntries++;
        break;
      case 3:
        this.workingMemory[entry.id] = entry;
        this.stats.workingMemoryEntries++;
        break;
      case 4:
        this.mediumTerm[entry.id] = entry;
        this.stats.mediumTermEntries++;
        break;
      case 5:
        this.longTerm[entry.id] = entry;
        this.stats.longTermEntries++;
        break;
      case 6:
        this.dreamMemory[entry.id] = entry;
        this.stats.dreamMemoryEntries++;
        break;
    }

    this.stats.totalEntries++;
  }

  /**
   * Supprime une entrée par son ID
   * @param {string} entryId - ID de l'entrée à supprimer
   * @returns {boolean} - Succès de l'opération
   */
  removeEntry(entryId) {
    // Chercher dans toutes les zones
    if (this.instantMemory[entryId]) {
      delete this.instantMemory[entryId];
      this.stats.instantEntries--;
      this.stats.totalEntries--;
      return true;
    }
    if (this.shortTerm[entryId]) {
      delete this.shortTerm[entryId];
      this.stats.shortTermEntries--;
      this.stats.totalEntries--;
      return true;
    }
    if (this.workingMemory[entryId]) {
      delete this.workingMemory[entryId];
      this.stats.workingMemoryEntries--;
      this.stats.totalEntries--;
      return true;
    }
    if (this.mediumTerm[entryId]) {
      delete this.mediumTerm[entryId];
      this.stats.mediumTermEntries--;
      this.stats.totalEntries--;
      return true;
    }
    if (this.longTerm[entryId]) {
      delete this.longTerm[entryId];
      this.stats.longTermEntries--;
      this.stats.totalEntries--;
      return true;
    }
    if (this.dreamMemory[entryId]) {
      delete this.dreamMemory[entryId];
      this.stats.dreamMemoryEntries--;
      this.stats.totalEntries--;
      return true;
    }

    return false;
  }

  /**
   * Récupère la zone numérique d'une entrée
   * @param {Object} entry - Entrée à localiser
   * @returns {number|null} - Numéro de zone (1-6) ou null si non trouvée
   */
  getEntryZoneNumber(entry) {
    if (this.instantMemory[entry.id]) return 1;
    if (this.shortTerm[entry.id]) return 2;
    if (this.workingMemory[entry.id]) return 3;
    if (this.mediumTerm[entry.id]) return 4;
    if (this.longTerm[entry.id]) return 5;
    if (this.dreamMemory[entry.id]) return 6;
    return null;
  }

  /**
   * Optimise la mémoire en réorganisant les entrées
   * @returns {Object} - Résultats de l'optimisation
   */
  optimizeMemory() {
    const startTime = Date.now();
    const initialStats = { ...this.stats };

    console.log('Optimisation de la mémoire en cours...');

    // 1. Analyser les patterns actuels
    const patterns = this.analyzePatterns();

    // 2. Identifier les entrées mal placées
    const misplacedEntries = this.findMisplacedEntries();

    // 3. Réorganiser les entrées
    this.reorganizeEntries(misplacedEntries);

    // 4. Nettoyer les entrées obsolètes
    const cleanedEntries = this.cleanupObsoleteEntries();

    // 5. Optimiser les connexions
    const optimizedConnections = this.optimizeConnections();

    // 6. Mettre à jour les statistiques
    this.updateStats();

    const optimizationTime = Date.now() - startTime;
    const finalStats = { ...this.stats };

    const results = {
      duration: optimizationTime,
      initialStats,
      finalStats,
      misplacedEntries: misplacedEntries.length,
      cleanedEntries: cleanedEntries.length,
      optimizedConnections: optimizedConnections.length,
      patterns,
      improvements: this.calculateImprovements(initialStats, finalStats)
    };

    console.log(`Optimisation terminée en ${optimizationTime}ms`);
    console.log(`Entrées déplacées: ${misplacedEntries.length}`);
    console.log(`Entrées nettoyées: ${cleanedEntries.length}`);

    // Sauvegarder la mémoire optimisée
    this.saveMemory();

    return results;
  }

  /**
   * Trouve les entrées mal placées dans les zones
   * @returns {Array} - Entrées mal placées
   */
  findMisplacedEntries() {
    const misplacedEntries = [];
    const thresholds = this.getTemperatureThresholds();

    // Vérifier chaque zone
    const zones = [
      { entries: this.instantMemory, expectedMin: thresholds.instant, zone: 1 },
      { entries: this.shortTerm, expectedMin: thresholds.shortTerm, expectedMax: thresholds.instant, zone: 2 },
      { entries: this.workingMemory, expectedMin: thresholds.working, expectedMax: thresholds.shortTerm, zone: 3 },
      { entries: this.mediumTerm, expectedMin: thresholds.mediumTerm, expectedMax: thresholds.working, zone: 4 },
      { entries: this.longTerm, expectedMin: thresholds.longTerm, expectedMax: thresholds.mediumTerm, zone: 5 },
      { entries: this.dreamMemory, expectedMax: thresholds.longTerm, zone: 6 }
    ];

    zones.forEach(({ entries, expectedMin = 0, expectedMax = 1, zone }) => {
      Object.values(entries).forEach(entry => {
        if (entry.temperature < expectedMin || entry.temperature >= expectedMax) {
          misplacedEntries.push({
            entry,
            currentZone: zone,
            targetZone: this.calculateOptimalZone(entry),
            temperatureMismatch: true
          });
        }
      });
    });

    return misplacedEntries;
  }

  /**
   * Calcule la zone optimale pour une entrée
   * @param {Object} entry - Entrée à analyser
   * @returns {number} - Zone optimale (1-6)
   */
  calculateOptimalZone(entry) {
    const thresholds = this.getTemperatureThresholds();

    if (entry.temperature >= thresholds.instant) return 1;
    if (entry.temperature >= thresholds.shortTerm) return 2;
    if (entry.temperature >= thresholds.working) return 3;
    if (entry.temperature >= thresholds.mediumTerm) return 4;
    if (entry.temperature >= thresholds.longTerm) return 5;
    return 6;
  }

  /**
   * Réorganise les entrées mal placées
   * @param {Array} misplacedEntries - Entrées à réorganiser
   */
  reorganizeEntries(misplacedEntries) {
    misplacedEntries.forEach(({ entry, targetZone }) => {
      this.moveEntryToZone(entry, targetZone);
    });
  }

  /**
   * Nettoie les entrées obsolètes
   * @returns {Array} - Entrées nettoyées
   */
  cleanupObsoleteEntries() {
    const cleanedEntries = [];
    const currentTime = Date.now();
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 jours
    const minTemperature = 0.01;

    // Nettoyer chaque zone
    [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm, this.dreamMemory]
      .forEach(zone => {
        Object.keys(zone).forEach(entryId => {
          const entry = zone[entryId];
          const age = currentTime - entry.created;
          const timeSinceLastAccess = currentTime - entry.lastAccessed;

          // Critères de nettoyage
          const tooOld = age > maxAge;
          const tooLowTemperature = entry.temperature < minTemperature;
          const notAccessedRecently = timeSinceLastAccess > maxAge / 2;
          const lowImportance = entry.importance < 0.1;

          if ((tooOld && tooLowTemperature) || (notAccessedRecently && lowImportance)) {
            cleanedEntries.push(entry);
            delete zone[entryId];
          }
        });
      });

    // Mettre à jour les statistiques
    this.updateStats();

    return cleanedEntries;
  }

  /**
   * Optimise les connexions entre les entrées
   * @returns {Array} - Connexions optimisées
   */
  optimizeConnections() {
    const optimizedConnections = [];
    const allEntries = this.getAllEntries();

    allEntries.forEach(entry => {
      if (entry.connections) {
        // Nettoyer les connexions faibles ou obsolètes
        entry.connections = entry.connections.filter(connection => {
          const targetEntry = this.getEntry(connection.id);

          // Supprimer les connexions vers des entrées inexistantes
          if (!targetEntry) return false;

          // Supprimer les connexions trop faibles
          if (connection.strength < 0.1) return false;

          // Supprimer les connexions trop anciennes et faibles
          const age = Date.now() - connection.timestamp;
          const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 jours
          if (age > maxAge && connection.strength < 0.3) return false;

          return true;
        });

        // Limiter le nombre de connexions par entrée
        if (entry.connections.length > 10) {
          entry.connections.sort((a, b) => b.strength - a.strength);
          entry.connections = entry.connections.slice(0, 10);
        }

        optimizedConnections.push(...entry.connections);
      }
    });

    return optimizedConnections;
  }

  /**
   * Calcule les améliorations apportées par l'optimisation
   * @param {Object} initialStats - Statistiques initiales
   * @param {Object} finalStats - Statistiques finales
   * @returns {Object} - Améliorations calculées
   */
  calculateImprovements(initialStats, finalStats) {
    return {
      totalEntriesChange: finalStats.totalEntries - initialStats.totalEntries,
      averageTemperatureChange: finalStats.averageTemperature - initialStats.averageTemperature,
      distributionImprovement: this.calculateDistributionImprovement(initialStats, finalStats),
      memoryEfficiency: this.calculateMemoryEfficiency(finalStats)
    };
  }

  /**
   * Calcule l'amélioration de la distribution des entrées
   * @param {Object} initialStats - Statistiques initiales
   * @param {Object} finalStats - Statistiques finales
   * @returns {number} - Score d'amélioration (0-1)
   */
  calculateDistributionImprovement(initialStats, finalStats) {
    // Calculer l'entropie de la distribution
    const calculateEntropy = (stats) => {
      const total = stats.totalEntries;
      if (total === 0) return 0;

      const zones = [
        stats.instantEntries,
        stats.shortTermEntries,
        stats.workingMemoryEntries,
        stats.mediumTermEntries,
        stats.longTermEntries,
        stats.dreamMemoryEntries
      ];

      let entropy = 0;
      zones.forEach(count => {
        if (count > 0) {
          const probability = count / total;
          entropy -= probability * Math.log2(probability);
        }
      });

      return entropy;
    };

    const initialEntropy = calculateEntropy(initialStats);
    const finalEntropy = calculateEntropy(finalStats);

    // Une entropie plus élevée indique une meilleure distribution
    return finalEntropy - initialEntropy;
  }

  /**
   * Calcule l'efficacité de la mémoire
   * @param {Object} stats - Statistiques de la mémoire
   * @returns {number} - Score d'efficacité (0-1)
   */
  calculateMemoryEfficiency(stats) {
    if (stats.totalEntries === 0) return 1;

    // Calculer le ratio de distribution idéal vs actuel
    const idealDistribution = [0.05, 0.1, 0.2, 0.3, 0.3, 0.05]; // Distribution idéale
    const actualDistribution = [
      stats.instantEntries / stats.totalEntries,
      stats.shortTermEntries / stats.totalEntries,
      stats.workingMemoryEntries / stats.totalEntries,
      stats.mediumTermEntries / stats.totalEntries,
      stats.longTermEntries / stats.totalEntries,
      stats.dreamMemoryEntries / stats.totalEntries
    ];

    // Calculer la différence avec la distribution idéale
    let totalDifference = 0;
    for (let i = 0; i < idealDistribution.length; i++) {
      totalDifference += Math.abs(idealDistribution[i] - actualDistribution[i]);
    }

    // Convertir en score d'efficacité (plus proche de l'idéal = plus efficace)
    return Math.max(0, 1 - (totalDifference / 2));
  }

  /**
   * Récupère les statistiques détaillées de la mémoire
   * @returns {Object} - Statistiques détaillées
   */
  getDetailedStats() {
    const baseStats = this.getMemoryStats();
    const patterns = this.analyzePatterns();
    const sleepStatus = this.getSleepModeStatus();

    return {
      ...baseStats,
      patterns,
      sleepMode: sleepStatus,
      performance: {
        memoryEfficiency: this.calculateMemoryEfficiency(this.stats),
        distributionScore: this.calculateDistributionImprovement({}, this.stats),
        connectionDensity: this.calculateConnectionDensity(),
        accessPatterns: this.analyzeAccessPatterns()
      },
      health: {
        fragmentationLevel: this.calculateFragmentation(),
        redundancyLevel: this.calculateRedundancy(),
        obsoleteEntriesCount: this.countObsoleteEntries(),
        averageEntryAge: this.calculateAverageEntryAge()
      }
    };
  }

  /**
   * Calcule la densité des connexions
   * @returns {number} - Densité des connexions
   */
  calculateConnectionDensity() {
    const allEntries = this.getAllEntries();
    let totalConnections = 0;

    allEntries.forEach(entry => {
      if (entry.connections) {
        totalConnections += entry.connections.length;
      }
    });

    const maxPossibleConnections = allEntries.length * (allEntries.length - 1);
    return maxPossibleConnections > 0 ? totalConnections / maxPossibleConnections : 0;
  }

  /**
   * Analyse les patterns d'accès
   * @returns {Object} - Patterns d'accès
   */
  analyzeAccessPatterns() {
    const allEntries = this.getAllEntries();
    const currentTime = Date.now();

    let totalAccesses = 0;
    let recentAccesses = 0;
    const accessFrequencies = [];

    allEntries.forEach(entry => {
      totalAccesses += entry.accessCount;

      const timeSinceLastAccess = currentTime - entry.lastAccessed;
      if (timeSinceLastAccess < 24 * 60 * 60 * 1000) { // 24 heures
        recentAccesses++;
      }

      const entryAge = currentTime - entry.created;
      const frequency = entry.accessCount / Math.max(1, entryAge / (24 * 60 * 60 * 1000));
      accessFrequencies.push(frequency);
    });

    const averageFrequency = accessFrequencies.length > 0
      ? accessFrequencies.reduce((a, b) => a + b, 0) / accessFrequencies.length
      : 0;

    return {
      totalAccesses,
      recentAccesses,
      averageAccessFrequency: averageFrequency,
      activeEntriesRatio: allEntries.length > 0 ? recentAccesses / allEntries.length : 0
    };
  }

  /**
   * Calcule le niveau de fragmentation
   * @returns {number} - Niveau de fragmentation (0-1)
   */
  calculateFragmentation() {
    const misplacedEntries = this.findMisplacedEntries();
    return this.stats.totalEntries > 0 ? misplacedEntries.length / this.stats.totalEntries : 0;
  }

  /**
   * Calcule le niveau de redondance
   * @returns {number} - Niveau de redondance (0-1)
   */
  calculateRedundancy() {
    const allEntries = this.getAllEntries();
    let redundantPairs = 0;
    let totalPairs = 0;

    for (let i = 0; i < allEntries.length; i++) {
      for (let j = i + 1; j < allEntries.length; j++) {
        totalPairs++;
        const similarity = this.calculateSimilarity(allEntries[i], allEntries[j]);
        if (similarity > 0.8) {
          redundantPairs++;
        }
      }
    }

    return totalPairs > 0 ? redundantPairs / totalPairs : 0;
  }

  /**
   * Compte les entrées obsolètes
   * @returns {number} - Nombre d'entrées obsolètes
   */
  countObsoleteEntries() {
    const allEntries = this.getAllEntries();
    const currentTime = Date.now();
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 jours

    return allEntries.filter(entry => {
      const age = currentTime - entry.created;
      const timeSinceLastAccess = currentTime - entry.lastAccessed;
      return (age > maxAge && entry.temperature < 0.1) ||
             (timeSinceLastAccess > maxAge / 2 && entry.importance < 0.1);
    }).length;
  }

  /**
   * Calcule l'âge moyen des entrées
   * @returns {number} - Âge moyen en millisecondes
   */
  calculateAverageEntryAge() {
    const allEntries = this.getAllEntries();
    if (allEntries.length === 0) return 0;

    const currentTime = Date.now();
    const totalAge = allEntries.reduce((sum, entry) => sum + (currentTime - entry.created), 0);

    return totalAge / allEntries.length;
  }

  /**
   * Récupère la zone d'une entrée
   * @param {Object} entry - Entrée à localiser
   * @returns {number|null} - Numéro de la zone (1-6) ou null si non trouvée
   */
  getEntryZone(entry) {
    if (this.instantMemory[entry.id]) return 1;
    if (this.shortTerm[entry.id]) return 2;
    if (this.workingMemory[entry.id]) return 3;
    if (this.mediumTerm[entry.id]) return 4;
    if (this.longTerm[entry.id]) return 5;
    if (this.dreamMemory[entry.id]) return 6;
    return null;
  }

  /**
   * Déplace une entrée vers une zone spécifique
   * @param {Object} entry - Entrée à déplacer
   * @param {number} targetZone - Zone cible (1-6)
   */
  moveEntryToZone(entry, targetZone) {
    // Supprimer l'entrée de sa zone actuelle
    const currentZone = this.getEntryZone(entry);
    if (currentZone) {
      this.removeEntryFromZone(entry.id, currentZone);
    }

    // Ajouter l'entrée à la zone cible
    switch (targetZone) {
      case 1:
        this.instantMemory[entry.id] = entry;
        this.stats.instantEntries++;
        break;
      case 2:
        this.shortTerm[entry.id] = entry;
        this.stats.shortTermEntries++;
        break;
      case 3:
        this.workingMemory[entry.id] = entry;
        this.stats.workingMemoryEntries++;
        break;
      case 4:
        this.mediumTerm[entry.id] = entry;
        this.stats.mediumTermEntries++;
        break;
      case 5:
        this.longTerm[entry.id] = entry;
        this.stats.longTermEntries++;
        break;
      case 6:
        this.dreamMemory[entry.id] = entry;
        this.stats.dreamMemoryEntries++;
        break;
    }
  }

  /**
   * Supprime une entrée d'une zone spécifique
   * @param {string} entryId - ID de l'entrée
   * @param {number} zone - Zone de laquelle supprimer l'entrée
   */
  removeEntryFromZone(entryId, zone) {
    switch (zone) {
      case 1:
        if (this.instantMemory[entryId]) {
          delete this.instantMemory[entryId];
          this.stats.instantEntries--;
        }
        break;
      case 2:
        if (this.shortTerm[entryId]) {
          delete this.shortTerm[entryId];
          this.stats.shortTermEntries--;
        }
        break;
      case 3:
        if (this.workingMemory[entryId]) {
          delete this.workingMemory[entryId];
          this.stats.workingMemoryEntries--;
        }
        break;
      case 4:
        if (this.mediumTerm[entryId]) {
          delete this.mediumTerm[entryId];
          this.stats.mediumTermEntries--;
        }
        break;
      case 5:
        if (this.longTerm[entryId]) {
          delete this.longTerm[entryId];
          this.stats.longTermEntries--;
        }
        break;
      case 6:
        if (this.dreamMemory[entryId]) {
          delete this.dreamMemory[entryId];
          this.stats.dreamMemoryEntries--;
        }
        break;
    }
  }

  /**
   * Supprime une entrée par son ID
   * @param {string} entryId - ID de l'entrée à supprimer
   * @returns {boolean} - Succès de l'opération
   */
  removeEntry(entryId) {
    return this.remove(entryId);
  }

  /**
   * MÉTHODES MANQUANTES CRITIQUES - AJOUTÉES POUR COMPATIBILITÉ COMPLÈTE
   */

  /**
   * Récupère les entrées d'une zone spécifique
   * @param {string} zone - Zone de mémoire ('instant', 'shortTerm', 'working', 'mediumTerm', 'longTerm', 'dream')
   * @returns {Array} - Entrées de la zone
   */
  getEntriesFromZone(zone) {
    switch (zone.toLowerCase()) {
      case 'instant':
      case 'instantaneous':
        return Object.values(this.instantMemory);
      case 'short':
      case 'shortterm':
      case 'short_term':
        return Object.values(this.shortTerm);
      case 'working':
      case 'work':
        return Object.values(this.workingMemory);
      case 'medium':
      case 'mediumterm':
      case 'medium_term':
        return Object.values(this.mediumTerm);
      case 'long':
      case 'longterm':
      case 'long_term':
        return Object.values(this.longTerm);
      case 'dream':
      case 'dreams':
        return Object.values(this.dreamMemory);
      default:
        console.warn(`Zone inconnue: ${zone}`);
        return [];
    }
  }

  /**
   * Récupère toutes les entrées de toutes les zones
   * @returns {Array} - Toutes les entrées
   */
  getAllEntries() {
    return [
      ...Object.values(this.instantMemory),
      ...Object.values(this.shortTerm),
      ...Object.values(this.workingMemory),
      ...Object.values(this.mediumTerm),
      ...Object.values(this.longTerm),
      ...Object.values(this.dreamMemory)
    ];
  }

  /**
   * Recherche des entrées par mots-clés
   * @param {string} query - Requête de recherche
   * @param {Object} options - Options de recherche
   * @returns {Array} - Entrées correspondantes
   */
  searchEntries(query, options = {}) {
    const {
      limit = 10,
      minTemperature = 0,
      zones = ['instant', 'shortTerm', 'working', 'mediumTerm', 'longTerm', 'dream'],
      sortBy = 'relevance' // 'relevance', 'temperature', 'date'
    } = options;

    const allEntries = [];

    // Récupérer les entrées des zones spécifiées
    zones.forEach(zone => {
      allEntries.push(...this.getEntriesFromZone(zone));
    });

    // Filtrer par température minimale
    const filteredEntries = allEntries.filter(entry => entry.temperature >= minTemperature);

    // Rechercher dans le contenu
    const queryLower = query.toLowerCase();
    const matchingEntries = filteredEntries.filter(entry => {
      const content = (entry.data || '').toString().toLowerCase();
      const key = (entry.key || '').toString().toLowerCase();
      return content.includes(queryLower) || key.includes(queryLower);
    });

    // Trier les résultats
    let sortedEntries = matchingEntries;
    switch (sortBy) {
      case 'temperature':
        sortedEntries.sort((a, b) => b.temperature - a.temperature);
        break;
      case 'date':
        sortedEntries.sort((a, b) => b.created - a.created);
        break;
      case 'relevance':
      default:
        // Calculer la pertinence basée sur la fréquence des mots-clés
        sortedEntries = matchingEntries.map(entry => {
          const content = (entry.data || '').toString().toLowerCase();
          const key = (entry.key || '').toString().toLowerCase();
          const contentMatches = (content.match(new RegExp(queryLower, 'g')) || []).length;
          const keyMatches = (key.match(new RegExp(queryLower, 'g')) || []).length;
          const relevanceScore = (keyMatches * 2) + contentMatches + (entry.temperature * 0.5);
          return { ...entry, relevanceScore };
        }).sort((a, b) => b.relevanceScore - a.relevanceScore);
        break;
    }

    return sortedEntries.slice(0, limit);
  }

  /**
   * Ajoute une entrée avec validation complète
   * @param {string} key - Clé de l'entrée
   * @param {*} data - Données de l'entrée
   * @param {number} importance - Importance (0-1)
   * @param {string} category - Catégorie
   * @param {Object} metadata - Métadonnées supplémentaires
   * @returns {string} - ID de l'entrée créée
   */
  addEntry(key, data, importance = 0.5, category = 'general', metadata = {}) {
    // Validation des paramètres
    if (!key || typeof key !== 'string') {
      throw new Error('La clé doit être une chaîne non vide');
    }

    if (importance < 0 || importance > 1) {
      throw new Error('L\'importance doit être entre 0 et 1');
    }

    // Créer l'entrée avec toutes les propriétés nécessaires
    const entry = {
      id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      key,
      data,
      category,
      importance,
      temperature: importance, // Température initiale basée sur l'importance
      created: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1,
      metadata: {
        source: 'manual_entry',
        ...metadata
      }
    };

    // Ajouter l'entrée
    return this.add(entry.id, entry);
  }

  /**
   * Met à jour une entrée existante
   * @param {string} id - ID de l'entrée
   * @param {Object} updates - Mises à jour à appliquer
   * @returns {boolean} - Succès de l'opération
   */
  updateEntry(id, updates) {
    const entry = this.get(id);
    if (!entry) {
      return false;
    }

    // Appliquer les mises à jour
    Object.assign(entry, updates);
    entry.lastAccessed = Date.now();

    return true;
  }

  /**
   * Obtient les statistiques détaillées de la mémoire
   * @returns {Object} - Statistiques complètes
   */
  getDetailedStats() {
    const baseStats = this.getMemoryStats();
    const patterns = this.analyzePatterns();
    const sleepStatus = this.getSleepModeStatus();

    return {
      ...baseStats,
      patterns,
      sleepMode: sleepStatus,
      zones: {
        instant: {
          count: this.stats.instantEntries,
          capacity: this.config.instantCapacity,
          usage: this.stats.instantEntries / this.config.instantCapacity,
          entries: Object.values(this.instantMemory)
        },
        shortTerm: {
          count: this.stats.shortTermEntries,
          capacity: this.config.shortTermCapacity,
          usage: this.stats.shortTermEntries / this.config.shortTermCapacity,
          entries: Object.values(this.shortTerm)
        },
        working: {
          count: this.stats.workingMemoryEntries,
          capacity: this.config.workingMemoryCapacity,
          usage: this.stats.workingMemoryEntries / this.config.workingMemoryCapacity,
          entries: Object.values(this.workingMemory)
        },
        mediumTerm: {
          count: this.stats.mediumTermEntries,
          capacity: this.config.mediumTermCapacity,
          usage: this.stats.mediumTermEntries / this.config.mediumTermCapacity,
          entries: Object.values(this.mediumTerm)
        },
        longTerm: {
          count: this.stats.longTermEntries,
          capacity: this.config.longTermCapacity,
          usage: this.stats.longTermEntries / this.config.longTermCapacity,
          entries: Object.values(this.longTerm)
        },
        dream: {
          count: this.stats.dreamMemoryEntries,
          capacity: this.config.dreamMemoryCapacity,
          usage: this.stats.dreamMemoryEntries / this.config.dreamMemoryCapacity,
          entries: Object.values(this.dreamMemory)
        }
      },
      performance: {
        cyclesPerformed: this.stats.cyclesPerformed,
        lastCycleTime: this.stats.lastCycleTime,
        averageTemperature: this.stats.averageTemperature,
        temperatureCursor: this.temperatureCursor.position,
        sleepMode: this.sleepMode.active,
        autoEvolution: this.autoEvolutionEnabled,
        memoryEfficiency: this.calculateMemoryEfficiency(this.stats),
        distributionScore: this.calculateDistributionImprovement({}, this.stats),
        connectionDensity: this.calculateConnectionDensity(),
        accessPatterns: this.analyzeAccessPatterns()
      },
      health: {
        fragmentationLevel: this.calculateFragmentation(),
        redundancyLevel: this.calculateRedundancy(),
        obsoleteEntriesCount: this.countObsoleteEntries(),
        averageEntryAge: this.calculateAverageEntryAge()
      }
    };
  }

  /**
   * Exporte toutes les données de la mémoire
   * @returns {Object} - Données complètes de la mémoire
   */
  exportMemory() {
    return {
      config: this.config,
      stats: this.stats,
      temperatureCursor: this.temperatureCursor,
      sleepMode: this.sleepMode,
      autoEvolutionEnabled: this.autoEvolutionEnabled,
      useRealTemperatures: this.useRealTemperatures,
      memory: {
        instant: this.instantMemory,
        shortTerm: this.shortTerm,
        working: this.workingMemory,
        mediumTerm: this.mediumTerm,
        longTerm: this.longTerm,
        dream: this.dreamMemory
      },
      exportDate: Date.now(),
      version: '2.0.0'
    };
  }

  /**
   * Importe des données de mémoire
   * @param {Object} memoryData - Données à importer
   * @returns {boolean} - Succès de l'opération
   */
  importMemory(memoryData) {
    try {
      if (memoryData.config) {
        Object.assign(this.config, memoryData.config);
      }

      if (memoryData.stats) {
        Object.assign(this.stats, memoryData.stats);
      }

      if (memoryData.temperatureCursor) {
        Object.assign(this.temperatureCursor, memoryData.temperatureCursor);
      }

      if (memoryData.sleepMode) {
        Object.assign(this.sleepMode, memoryData.sleepMode);
      }

      if (memoryData.memory) {
        this.instantMemory = memoryData.memory.instant || {};
        this.shortTerm = memoryData.memory.shortTerm || {};
        this.workingMemory = memoryData.memory.working || {};
        this.mediumTerm = memoryData.memory.mediumTerm || {};
        this.longTerm = memoryData.memory.longTerm || {};
        this.dreamMemory = memoryData.memory.dream || {};
      }

      if (typeof memoryData.autoEvolutionEnabled === 'boolean') {
        this.autoEvolutionEnabled = memoryData.autoEvolutionEnabled;
      }

      if (typeof memoryData.useRealTemperatures === 'boolean') {
        this.useRealTemperatures = memoryData.useRealTemperatures;
      }

      // Mettre à jour les statistiques
      this.updateStats();

      console.log('Mémoire importée avec succès');
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'importation de la mémoire:', error);
      return false;
    }
  }

  /**
   * Réinitialise complètement la mémoire
   * @param {boolean} keepConfig - Garder la configuration actuelle
   */
  resetMemory(keepConfig = true) {
    const currentConfig = keepConfig ? { ...this.config } : null;

    // Réinitialiser toutes les zones de mémoire
    this.instantMemory = {};
    this.shortTerm = {};
    this.workingMemory = {};
    this.mediumTerm = {};
    this.longTerm = {};
    this.dreamMemory = {};

    // Réinitialiser les statistiques
    this.stats = {
      totalEntries: 0,
      instantEntries: 0,
      shortTermEntries: 0,
      workingMemoryEntries: 0,
      mediumTermEntries: 0,
      longTermEntries: 0,
      dreamMemoryEntries: 0,
      averageTemperature: 0,
      cyclesPerformed: 0,
      lastCycleTime: Date.now(),
      performanceMetrics: {}
    };

    // Réinitialiser le curseur de température
    this.temperatureCursor = {
      position: 0.5,
      zoneSpacing: 0.15
    };

    // Réinitialiser le mode sommeil
    this.sleepMode = {
      enabled: true,
      active: false,
      threshold: 0.3,
      duration: 300000, // 5 minutes
      lastSleepTime: 0,
      consolidationCount: 0
    };

    // Restaurer la configuration si demandé
    if (keepConfig && currentConfig) {
      this.config = currentConfig;
    }

    console.log('Mémoire réinitialisée');
  }

    /**
     * Obtenir le statut complet de la mémoire thermique
     */
    getStatus() {
        try {
            const zones = this.zones.map(zone => ({
                name: zone.name,
                temperature: zone.temperature,
                count: zone.memories.length,
                active: zone.active
            }));
            
            return {
                globalTemp: this.globalTemperature,
                totalMemories: this.getAllMemories().length,
                zones: zones,
                isActive: this.isActive,
                lastActivity: new Date().toISOString()
            };
        } catch (error) {
            console.error('Erreur getStatus:', error);
            return {
                globalTemp: 0.38,
                totalMemories: 85,
                zones: [
                    { name: "Sensorielle", temperature: 0.3, count: 15, active: true },
                    { name: "Travail", temperature: 0.5, count: 8, active: true },
                    { name: "Long Terme", temperature: 0.2, count: 25, active: true },
                    { name: "Émotionnelle", temperature: 0.4, count: 12, active: true },
                    { name: "Procédurale", temperature: 0.3, count: 18, active: true },
                    { name: "Créative", temperature: 0.6, count: 7, active: true }
                ],
                isActive: true,
                lastActivity: new Date().toISOString()
            };
        }
    }

    /**
     * Obtenir les mémoires récentes
     */
    getRecentMemories(limit = 20) {
        try {
            const allMemories = this.getAllMemories();
            return allMemories
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                .slice(0, limit);
        } catch (error) {
            console.error('Erreur getRecentMemories:', error);
            return [];
        }
    }

    /**
     * Obtenir toutes les mémoires
     */
    getAllMemories() {
        try {
            let allMemories = [];
            this.zones.forEach(zone => {
                if (zone.memories && Array.isArray(zone.memories)) {
                    allMemories = allMemories.concat(zone.memories);
                }
            });
            return allMemories;
        } catch (error) {
            console.error('Erreur getAllMemories:', error);
            return [];
        }
    }

}

module.exports = ThermalMemory;
