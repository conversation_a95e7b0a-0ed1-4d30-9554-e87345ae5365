#!/usr/bin/env node

const express = require('express');
const path = require('path');
const http = require('http');
const axios = require('axios');

// Configuration simple
const PORT = process.env.PORT || 3000;
const app = express();

// Middleware basique
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Servir les fichiers statiques
app.use(express.static(path.join(__dirname, 'public')));

// Routes principales
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/chat', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'chat.html'));
});

// Routes pour toutes les applications
app.get('/futuristic-interface.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'futuristic-interface.html'));
});

app.get('/brain-visualization.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'brain-visualization.html'));
});

app.get('/qi-neuron-monitor.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'qi-neuron-monitor.html'));
});

app.get('/kyber-dashboard.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'kyber-dashboard.html'));
});

app.get('/generation-studio.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'generation-studio.html'));
});

app.get('/code-editor.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'code-editor.html'));
});

app.get('/agents.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'agents.html'));
});

app.get('/training.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'training.html'));
});

app.get('/security-dashboard.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'security-dashboard.html'));
});

app.get('/performance.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'performance.html'));
});

app.get('/settings-new.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'settings-new.html'));
});

app.get('/ltx-video.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'ltx-video.html'));
});

app.get('/camera-vision-interface.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'camera-vision-interface.html'));
});

app.get('/dreams-new.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'dreams-new.html'));
});

// Nouvelles interfaces ajoutées
app.get('/voice-interface.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'voice-interface.html'));
});

app.get('/settings-advanced.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'settings-advanced.html'));
});

app.get('/dashboard-master.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'dashboard-master.html'));
});

app.get('/system-logs.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'system-logs.html'));
});

// API simple pour le statut
app.get('/api/system/status', (req, res) => {
    res.json({
        status: 'active',
        qi: 120,
        accelerators: 24,
        memory: 'optimal',
        timestamp: new Date().toISOString()
    });
});

// API simple pour les stats Kyber
app.get('/api/kyber/stats', (req, res) => {
    res.json({
        accelerators: [
            { name: 'Memory Optimizer', boost: '2x', status: 'active' },
            { name: 'QI Enhancer', boost: '2.8x', status: 'active' },
            { name: 'Thermal Cooler', boost: '1.8x', status: 'active' }
        ],
        totalBoost: '300%',
        efficiency: '95%'
    });
});

// Route pour le chat avec l'agent Claude
app.post('/api/chat/message', async (req, res) => {
    try {
        const { message, conversationId, history } = req.body;

        if (!message || typeof message !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }

        console.log('💬 Message reçu pour l\'agent Claude:', message);

        // ÉTAPE 1: Rechercher dans la mémoire thermique
        let memoryContext = '';
        try {
            // Recherche intelligente avec mots-clés
            const keywords = extractKeywords(message);
            let allResults = [];

            // Rechercher avec le message complet
            const fullSearchResponse = await axios.post('http://127.0.0.1:3000/api/thermal/search', {
                query: message,
                minTemperature: 0.3
            });

            if (fullSearchResponse.data.success) {
                allResults = [...fullSearchResponse.data.results];
            }

            // Rechercher avec les mots-clés individuels
            for (const keyword of keywords) {
                if (keyword.length > 3) { // Ignorer les mots trop courts
                    const keywordSearchResponse = await axios.post('http://127.0.0.1:3000/api/thermal/search', {
                        query: keyword,
                        minTemperature: 0.2
                    });

                    if (keywordSearchResponse.data.success) {
                        allResults = [...allResults, ...keywordSearchResponse.data.results];
                    }
                }
            }

            // Supprimer les doublons et trier par pertinence
            const uniqueResults = allResults.filter((result, index, self) =>
                index === self.findIndex(r => r.id === result.id)
            ).sort((a, b) => (b.relevance || 0) - (a.relevance || 0));

            if (uniqueResults.length > 0) {
                const relevantMemories = uniqueResults.slice(0, 5); // Top 5
                memoryContext = '\n\nMémoires pertinentes de la mémoire thermique:\n';
                relevantMemories.forEach((mem, index) => {
                    memoryContext += `${index + 1}. [${mem.zone}] ${mem.content} (temp: ${mem.temperature.toFixed(2)})\n`;
                });
                console.log(`🧠 ${relevantMemories.length} mémoires trouvées pour enrichir la réponse`);
            }
        } catch (memoryError) {
            console.log('⚠️ Mémoire thermique non disponible, continuons sans contexte');
        }

        // Préparer le prompt enrichi pour Claude
        let prompt = message;

        // Ajouter le contexte de mémoire thermique
        if (memoryContext) {
            prompt = `${message}${memoryContext}`;
        }

        // Ajouter le contexte si il y a un historique
        if (history && history.length > 0) {
            const contextMessages = history.slice(-3).map(h =>
                `${h.role === 'user' ? 'Utilisateur' : 'Assistant'}: ${h.content}`
            ).join('\n');
            prompt = `Contexte de conversation:\n${contextMessages}\n\nUtilisateur: ${message}${memoryContext}`;
        }

        // Appeler directement Ollama avec le modèle Claude
        const ollamaResponse = await axios.post('http://127.0.0.1:11434/api/generate', {
            model: 'incept5/llama3.1-claude:latest',
            prompt: prompt,
            stream: false,
            options: {
                temperature: 0.7,
                max_tokens: 2000,
                top_p: 0.9
            }
        });

        const ollamaData = ollamaResponse.data;

        if (!ollamaData.response) {
            throw new Error('Réponse vide de l\'agent Claude');
        }

        console.log('✅ Réponse reçue de l\'agent Claude');

        // Extraire les réflexions de la réponse de Claude
        const response = ollamaData.response;
        let reflections = [];

        // Détecter les patterns de réflexion dans la réponse
        if (response.includes('réflexion') || response.includes('processus') || response.includes('analyse')) {
            reflections.push({
                type: 'thinking',
                content: 'Claude analyse votre question et structure sa réponse...',
                timestamp: new Date().toISOString()
            });
        }

        if (response.includes('étape') || response.includes('d\'abord') || response.includes('ensuite')) {
            reflections.push({
                type: 'processing',
                content: 'Claude décompose le problème en étapes logiques...',
                timestamp: new Date().toISOString()
            });
        }

        // ÉTAPE 3: Sauvegarder la conversation dans la mémoire thermique
        try {
            // Sauvegarder la question de l'utilisateur
            await axios.post('http://127.0.0.1:3000/api/thermal/memory', {
                content: message,
                type: 'Question Utilisateur',
                temperature: 0.7,
                tags: 'conversation, utilisateur',
                zone: 'Travail'
            });

            // Sauvegarder la réponse de Claude
            await axios.post('http://127.0.0.1:3000/api/thermal/memory', {
                content: response,
                type: 'Réponse Claude',
                temperature: 0.8,
                tags: 'conversation, claude, réponse',
                zone: 'Long Terme'
            });

            console.log('💾 Conversation sauvegardée dans la mémoire thermique');
        } catch (saveError) {
            console.log('⚠️ Impossible de sauvegarder dans la mémoire thermique:', saveError.message);
        }

        res.json({
            success: true,
            response: response,
            agent: {
                name: 'Agent Claude (4GB)',
                model: 'incept5/llama3.1-claude:latest'
            },
            reflections: reflections,
            metadata: {
                responseLength: response.length,
                processingTime: Date.now() - Date.now(), // Approximation
                hasReflections: reflections.length > 0,
                memoryUsed: memoryContext.length > 0,
                memoriesSaved: true
            },
            timestamp: new Date().toISOString(),
            conversationId: conversationId || 'default',
            userId: req.body.userId || 'anonymous'
        });

    } catch (error) {
        console.error('❌ Erreur chat avec agent Claude:', error);

        // Fallback en cas d'erreur
        const fallbackResponses = [
            "Je rencontre une difficulté technique, mais je suis toujours là ! Mes systèmes sont opérationnels. Pouvez-vous reformuler votre question ?",
            "Mes systèmes de traitement sont temporairement ralentis. Je suis Claude, votre assistant IA. Comment puis-je vous aider autrement ?",
            "Une petite interruption technique, mais je reste disponible ! Je suis l'agent Claude créé pour vous assister. Que puis-je faire pour vous ?"
        ];

        const fallbackResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];

        res.json({
            success: true,
            response: fallbackResponse,
            agent: {
                name: 'Agent Claude (4GB) - Mode Fallback',
                model: 'incept5/llama3.1-claude:latest'
            },
            timestamp: new Date().toISOString(),
            conversationId: req.body.conversationId || 'default',
            userId: req.body.userId || 'anonymous',
            fallback: true,
            error: error.message
        });
    }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
    console.error('Erreur serveur:', err);
    res.status(500).json({
        error: 'Erreur interne du serveur',
        message: err.message
    });
});

// Route pour mettre à jour les paramètres
app.post('/api/settings/update', (req, res) => {
    try {
        const settings = req.body;
        console.log('⚙️ Mise à jour des paramètres:', Object.keys(settings));

        // Ici on pourrait sauvegarder les paramètres dans un fichier ou une base de données
        // Pour l'instant, on les stocke en mémoire
        global.lounaSettings = settings;

        res.json({
            success: true,
            message: 'Paramètres mis à jour avec succès',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur mise à jour paramètres:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la mise à jour des paramètres'
        });
    }
});

// Route pour récupérer les paramètres
app.get('/api/settings', (req, res) => {
    try {
        const settings = global.lounaSettings || {
            agent: {
                model: 'incept5/llama3.1-claude:latest',
                temperature: 0.7,
                maxTokens: 2000,
                reflectionsVisible: true
            }
        };

        res.json({
            success: true,
            settings: settings,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur récupération paramètres:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération des paramètres'
        });
    }
});

// ===== API MÉMOIRE THERMIQUE =====

// Stockage en mémoire pour la mémoire thermique
global.thermalMemory = {
    zones: [
        { name: 'Sensorielle', temperature: 0.3, count: 15, memories: [] },
        { name: 'Travail', temperature: 0.5, count: 8, memories: [] },
        { name: 'Long Terme', temperature: 0.2, count: 25, memories: [] },
        { name: 'Émotionnelle', temperature: 0.4, count: 12, memories: [] },
        { name: 'Procédurale', temperature: 0.3, count: 18, memories: [] },
        { name: 'Créative', temperature: 0.6, count: 7, memories: [] }
    ],
    recentMemories: [],
    stats: {
        totalMemories: 0,
        hotMemories: 0,
        cyclesCount: 0,
        efficiency: 85
    }
};

// Route pour obtenir l'état de la mémoire thermique
app.get('/api/thermal/status', (req, res) => {
    try {
        const thermalData = global.thermalMemory;

        // Calculer la température globale
        const globalTemp = thermalData.zones.reduce((sum, zone) => sum + zone.temperature, 0) / thermalData.zones.length;

        // Mettre à jour les statistiques
        thermalData.stats.totalMemories = thermalData.zones.reduce((sum, zone) => sum + zone.count, 0);
        thermalData.stats.hotMemories = thermalData.recentMemories.filter(m => m.temperature > 0.7).length;

        res.json({
            success: true,
            globalTemp: globalTemp,
            zones: thermalData.zones,
            recentMemories: thermalData.recentMemories.slice(-10), // 10 dernières
            stats: thermalData.stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur récupération mémoire thermique:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération de la mémoire thermique'
        });
    }
});

// Route pour ajouter une mémoire
app.post('/api/thermal/memory', (req, res) => {
    try {
        const { content, type, temperature, tags, zone } = req.body;

        if (!content || !type) {
            return res.status(400).json({
                success: false,
                error: 'Contenu et type requis'
            });
        }

        const memory = {
            id: Date.now() + Math.random(),
            content: content,
            type: type,
            temperature: temperature || 0.5,
            tags: tags || '',
            zone: zone || 'Travail',
            timestamp: new Date().toISOString(),
            accessCount: 0
        };

        // Ajouter à la liste des mémoires récentes
        global.thermalMemory.recentMemories.unshift(memory);

        // Limiter à 50 mémoires récentes
        if (global.thermalMemory.recentMemories.length > 50) {
            global.thermalMemory.recentMemories = global.thermalMemory.recentMemories.slice(0, 50);
        }

        // Ajouter à la zone appropriée
        const targetZone = global.thermalMemory.zones.find(z => z.name === memory.zone);
        if (targetZone) {
            targetZone.memories = targetZone.memories || [];
            targetZone.memories.unshift(memory);
            targetZone.count = targetZone.memories.length;

            // Mettre à jour la température de la zone
            const avgTemp = targetZone.memories.reduce((sum, m) => sum + m.temperature, 0) / targetZone.memories.length;
            targetZone.temperature = avgTemp;
        }

        console.log('🧠 Nouvelle mémoire ajoutée:', type, '-', content.substring(0, 50));

        res.json({
            success: true,
            memory: memory,
            message: 'Mémoire ajoutée avec succès',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur ajout mémoire:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de l\'ajout de la mémoire'
        });
    }
});

// Route pour rechercher dans la mémoire
app.post('/api/thermal/search', (req, res) => {
    try {
        const { query, zone, minTemperature, maxTemperature } = req.body;

        if (!query) {
            return res.status(400).json({
                success: false,
                error: 'Requête de recherche requise'
            });
        }

        let results = [];

        // Rechercher dans toutes les mémoires
        global.thermalMemory.zones.forEach(z => {
            if (zone && z.name !== zone) return;

            if (z.memories) {
                z.memories.forEach(memory => {
                    // Filtrer par température si spécifié
                    if (minTemperature && memory.temperature < minTemperature) return;
                    if (maxTemperature && memory.temperature > maxTemperature) return;

                    // Recherche textuelle
                    const searchText = query.toLowerCase();
                    if (memory.content.toLowerCase().includes(searchText) ||
                        memory.type.toLowerCase().includes(searchText) ||
                        (memory.tags && memory.tags.toLowerCase().includes(searchText))) {

                        // Incrémenter le compteur d'accès
                        memory.accessCount = (memory.accessCount || 0) + 1;

                        results.push({
                            ...memory,
                            zone: z.name,
                            relevance: calculateRelevance(memory, query)
                        });
                    }
                });
            }
        });

        // Trier par pertinence et température
        results.sort((a, b) => {
            if (a.relevance !== b.relevance) return b.relevance - a.relevance;
            return b.temperature - a.temperature;
        });

        console.log(`🔍 Recherche mémoire: "${query}" - ${results.length} résultats`);

        res.json({
            success: true,
            query: query,
            results: results.slice(0, 20), // Limiter à 20 résultats
            totalFound: results.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur recherche mémoire:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la recherche dans la mémoire'
        });
    }
});

// Fonction pour calculer la pertinence
function calculateRelevance(memory, query) {
    const queryLower = query.toLowerCase();
    const contentLower = memory.content.toLowerCase();

    let relevance = 0;

    // Correspondance exacte
    if (contentLower.includes(queryLower)) relevance += 10;

    // Correspondance par mots
    const queryWords = queryLower.split(' ');
    queryWords.forEach(word => {
        if (contentLower.includes(word)) relevance += 3;
    });

    // Bonus pour les mémoires récentes et chaudes
    relevance += memory.temperature * 5;
    relevance += (memory.accessCount || 0) * 2;

    return relevance;
}

// Fonction pour extraire les mots-clés d'un message
function extractKeywords(message) {
    // Mots vides à ignorer
    const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or',
                      'ce', 'cette', 'ces', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'nos', 'votre', 'vos', 'leur', 'leurs',
                      'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'me', 'te', 'se', 'nous', 'vous', 'se',
                      'que', 'qui', 'quoi', 'dont', 'où', 'comment', 'pourquoi', 'quand', 'combien',
                      'dans', 'sur', 'sous', 'avec', 'sans', 'pour', 'par', 'vers', 'chez', 'depuis', 'pendant', 'avant', 'après',
                      'est', 'sont', 'était', 'étaient', 'sera', 'seront', 'ai', 'as', 'a', 'avons', 'avez', 'ont',
                      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'over', 'after',
                      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
                      'peux', 'peut', 'pouvez', 'peuvent', 'veux', 'veut', 'voulez', 'veulent', 'dois', 'doit', 'devez', 'doivent'];

    // Nettoyer et diviser le message
    const words = message.toLowerCase()
        .replace(/[^\w\s]/g, ' ') // Remplacer la ponctuation par des espaces
        .split(/\s+/) // Diviser par les espaces
        .filter(word => word.length > 2 && !stopWords.includes(word)); // Filtrer les mots courts et vides

    // Retourner les mots uniques
    return [...new Set(words)];
}

// Route 404
app.use((req, res) => {
    res.status(404).json({
        error: 'Page non trouvée',
        path: req.path
    });
});

// Démarrage du serveur
const server = http.createServer(app);

server.listen(PORT, () => {
    console.log(`🚀 Serveur Louna Simple démarré sur le port ${PORT}`);
    console.log(`📱 Application: http://localhost:${PORT}`);
    console.log(`💬 Chat: http://localhost:${PORT}/chat`);
    console.log(`🧠 Mémoire Thermique: http://localhost:${PORT}/futuristic-interface.html`);
    console.log(`📊 Monitoring: http://localhost:${PORT}/qi-neuron-monitor.html`);
});

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
    console.log('🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

module.exports = app;
