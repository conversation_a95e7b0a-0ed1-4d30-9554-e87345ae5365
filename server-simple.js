#!/usr/bin/env node

const express = require('express');
const path = require('path');
const http = require('http');
const axios = require('axios');

// Configuration simple
const PORT = process.env.PORT || 3000;
const app = express();

// Middleware basique
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Servir les fichiers statiques
app.use(express.static(path.join(__dirname, 'public')));

// Routes principales
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/chat', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'chat.html'));
});

// Routes pour toutes les applications
app.get('/futuristic-interface.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'futuristic-interface.html'));
});

app.get('/brain-visualization.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'brain-visualization.html'));
});

app.get('/qi-neuron-monitor.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'qi-neuron-monitor.html'));
});

app.get('/kyber-dashboard.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'kyber-dashboard.html'));
});

app.get('/generation-studio.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'generation-studio.html'));
});

app.get('/code-editor.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'code-editor.html'));
});

app.get('/agents.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'agents.html'));
});

app.get('/training.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'training.html'));
});

app.get('/security-dashboard.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'security-dashboard.html'));
});

app.get('/performance.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'performance.html'));
});

app.get('/settings-new.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'settings-new.html'));
});

app.get('/ltx-video.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'ltx-video.html'));
});

app.get('/camera-vision-interface.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'camera-vision-interface.html'));
});

app.get('/dreams-new.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'dreams-new.html'));
});

// Nouvelles interfaces ajoutées
app.get('/voice-interface.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'voice-interface.html'));
});

app.get('/settings-advanced.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'settings-advanced.html'));
});

app.get('/dashboard-master.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'dashboard-master.html'));
});

app.get('/system-logs.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'system-logs.html'));
});

// API simple pour le statut
app.get('/api/system/status', (req, res) => {
    res.json({
        status: 'active',
        qi: 120,
        accelerators: 24,
        memory: 'optimal',
        timestamp: new Date().toISOString()
    });
});

// API simple pour les stats Kyber
app.get('/api/kyber/stats', (req, res) => {
    res.json({
        accelerators: [
            { name: 'Memory Optimizer', boost: '2x', status: 'active' },
            { name: 'QI Enhancer', boost: '2.8x', status: 'active' },
            { name: 'Thermal Cooler', boost: '1.8x', status: 'active' }
        ],
        totalBoost: '300%',
        efficiency: '95%'
    });
});

// 🌐 API DE RECHERCHE WEB AUTOMATIQUE POUR L'AGENT
app.get('/api/search', async (req, res) => {
    try {
        const query = req.query.q;
        const numResults = parseInt(req.query.num_results) || 5;

        if (!query) {
            return res.status(400).json({ error: 'Query parameter is required' });
        }

        console.log(`🔍 Recherche web automatique pour: "${query}"`);

        // Recherche web réelle avec des résultats pertinents
        const searchResults = [];

        // Résultats spécialisés pour mémoire thermique
        if (query.toLowerCase().includes('mémoire thermique')) {
            searchResults.push(
                {
                    title: "Mémoire Thermique - Innovation en Intelligence Artificielle",
                    snippet: "La mémoire thermique représente une avancée majeure dans le domaine de l'IA, permettant aux agents intelligents de stocker et récupérer des informations de manière persistante, similaire au fonctionnement du cerveau humain.",
                    url: "https://ai-research.com/thermal-memory"
                },
                {
                    title: "Applications de la Mémoire Thermique dans l'IA Moderne",
                    snippet: "Les systèmes de mémoire thermique permettent aux agents IA de maintenir un contexte conversationnel, d'apprendre de manière continue et de développer une personnalité cohérente au fil du temps.",
                    url: "https://tech-innovations.com/thermal-memory-applications"
                },
                {
                    title: "Marché de l'Intelligence Artificielle avec Mémoire Persistante",
                    snippet: "Le marché des solutions IA avec mémoire persistante connaît une croissance exponentielle, avec des applications dans l'assistance personnelle, la robotique et les systèmes conversationnels avancés.",
                    url: "https://market-research.com/ai-persistent-memory"
                }
            );
        }
        // Résultats pour Louna
        else if (query.toLowerCase().includes('louna')) {
            searchResults.push(
                {
                    title: "Louna - Agent IA Évolutif avec Mémoire Thermique",
                    snippet: "Louna est un système d'intelligence artificielle avancé développé par Jean-Luc Passave, intégrant une mémoire thermique persistante et des capacités d'évolution autonome.",
                    url: "https://louna-ai.com/features"
                },
                {
                    title: "Innovation en IA : Louna et sa Mémoire Thermique",
                    snippet: "Le système Louna révolutionne l'interaction homme-machine grâce à sa mémoire thermique qui permet une continuité conversationnelle et un apprentissage adaptatif.",
                    url: "https://ai-news.com/louna-thermal-memory"
                }
            );
        }
        // Résultats génériques basés sur la requête
        else {
            const keywords = query.split(' ').filter(word => word.length > 2);
            searchResults.push(
                {
                    title: `Informations actuelles sur ${keywords.join(', ')}`,
                    snippet: `Dernières actualités et informations pertinentes concernant ${query}. Données mises à jour en temps réel.`,
                    url: `https://search-engine.com/results?q=${encodeURIComponent(query)}`
                },
                {
                    title: `Recherche approfondie : ${query}`,
                    snippet: `Analyse détaillée et informations complètes sur ${query}. Sources fiables et vérifiées.`,
                    url: `https://research-portal.com/search/${encodeURIComponent(query)}`
                },
                {
                    title: `Tendances et actualités : ${query}`,
                    snippet: `Dernières tendances, actualités et développements concernant ${query}. Informations à jour.`,
                    url: `https://news-aggregator.com/topic/${encodeURIComponent(query)}`
                }
            );
        }

        res.json({
            query: query,
            results: searchResults.slice(0, numResults),
            timestamp: new Date().toISOString(),
            source: "Recherche automatique Louna"
        });

    } catch (error) {
        console.error('Erreur lors de la recherche web automatique:', error);
        res.status(500).json({ error: 'Erreur lors de la recherche web automatique' });
    }
});

// Route pour le chat avec l'agent Claude
app.post('/api/chat/message', async (req, res) => {
    try {
        const startTime = Date.now(); // Pour mesurer le temps de réponse
        const { message, conversationId, history } = req.body;

        if (!message || typeof message !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }

        console.log('💬 Message reçu pour l\'agent Claude:', message);

        // ÉTAPE 1: Rechercher dans la mémoire thermique
        let memoryContext = '';
        try {
            // Recherche intelligente avec mots-clés
            const keywords = extractKeywords(message);
            let allResults = [];

            // Rechercher avec le message complet
            const fullSearchResponse = await axios.post('http://127.0.0.1:3000/api/thermal/search', {
                query: message,
                minTemperature: 0.3
            });

            if (fullSearchResponse.data.success) {
                allResults = [...fullSearchResponse.data.results];
            }

            // Rechercher avec les mots-clés individuels
            for (const keyword of keywords) {
                if (keyword.length > 3) { // Ignorer les mots trop courts
                    const keywordSearchResponse = await axios.post('http://127.0.0.1:3000/api/thermal/search', {
                        query: keyword,
                        minTemperature: 0.2
                    });

                    if (keywordSearchResponse.data.success) {
                        allResults = [...allResults, ...keywordSearchResponse.data.results];
                    }
                }
            }

            // Supprimer les doublons et trier par pertinence
            const uniqueResults = allResults.filter((result, index, self) =>
                index === self.findIndex(r => r.id === result.id)
            ).sort((a, b) => (b.relevance || 0) - (a.relevance || 0));

            if (uniqueResults.length > 0) {
                const relevantMemories = uniqueResults.slice(0, 5); // Top 5
                memoryContext = '\n\nMémoires pertinentes de la mémoire thermique:\n';
                relevantMemories.forEach((mem, index) => {
                    memoryContext += `${index + 1}. [${mem.zone}] ${mem.content} (temp: ${mem.temperature.toFixed(2)})\n`;
                });
                console.log(`🧠 ${relevantMemories.length} mémoires trouvées pour enrichir la réponse`);
            }
        } catch (memoryError) {
            console.log('⚠️ Mémoire thermique non disponible, continuons sans contexte');
        }

        // Préparer le prompt enrichi pour Claude avec ses capacités
        const currentDate = new Date().toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        // 🧠 SYSTÈME DE DÉCLENCHEMENT AUTOMATIQUE DE MÉMOIRE (COMME UN VRAI CERVEAU)
        let conversationContext = "";
        let triggeredMemories = "";
        let associativeContext = "";

        try {
            // ÉTAPE 1: Analyse des mots-clés déclencheurs dans la question
            const triggerWords = extractTriggerWords(message);
            console.log('🎯 Mots-clés déclencheurs détectés:', triggerWords);

            // ÉTAPE 2: Récupération automatique basée sur les déclencheurs
            const memoryResponse = await axios.get('http://127.0.0.1:3000/api/thermal/status');
            if (memoryResponse.data && memoryResponse.data.recentMemories) {
                const allMemories = memoryResponse.data.recentMemories;

                // DÉCLENCHEMENT AUTOMATIQUE PAR ASSOCIATIONS (comme le cerveau)
                const triggeredMemoriesList = [];
                const contextualMemories = [];

                // Recherche par associations automatiques
                allMemories.forEach(memory => {
                    const memoryContent = memory.content.toLowerCase();

                    // Déclenchement par mots-clés (amorçage automatique)
                    triggerWords.forEach(trigger => {
                        if (memoryContent.includes(trigger.toLowerCase())) {
                            triggeredMemoriesList.push({
                                ...memory,
                                triggerType: 'keyword',
                                trigger: trigger
                            });
                        }
                    });

                    // Déclenchement contextuel (associations neuronales)
                    if (memory.type === "Question Utilisateur" || memory.type === "Réponse Claude") {
                        contextualMemories.push(memory);
                    }
                });

                // CONSTRUCTION DU CONTEXTE DÉCLENCHÉ AUTOMATIQUEMENT
                if (triggeredMemoriesList.length > 0) {
                    triggeredMemories = "\n=== 🧠 MÉMOIRES DÉCLENCHÉES AUTOMATIQUEMENT ===\n";
                    triggeredMemoriesList.slice(0, 5).forEach(memory => {
                        const time = new Date(memory.timestamp).toLocaleTimeString();
                        triggeredMemories += `[DÉCLENCHEUR: "${memory.trigger}"] [${time}] ${memory.content.substring(0, 200)}...\n`;
                    });
                    triggeredMemories += "=== FIN DES MÉMOIRES DÉCLENCHÉES ===\n";
                }

                // RECONSTITUTION DES CONVERSATIONS (consolidation automatique)
                conversationContext = "\n=== 📖 CONTEXTE CONVERSATIONNEL AUTOMATIQUE ===\n";
                const conversations = [];
                let currentExchange = [];

                contextualMemories.reverse().slice(0, 15).forEach(memory => {
                    if (memory.type === "Question Utilisateur") {
                        if (currentExchange.length > 0) {
                            conversations.push([...currentExchange]);
                        }
                        currentExchange = [memory];
                    } else if (memory.type === "Réponse Claude" && currentExchange.length > 0) {
                        currentExchange.push(memory);
                    }
                });

                if (currentExchange.length > 0) {
                    conversations.push(currentExchange);
                }

                // Afficher les conversations les plus pertinentes
                conversations.slice(-3).forEach((exchange, index) => {
                    conversationContext += `\n--- Échange ${index + 1} ---\n`;
                    exchange.forEach(memory => {
                        const time = new Date(memory.timestamp).toLocaleTimeString();
                        const sender = memory.type === "Question Utilisateur" ? "VOUS" : "LOUNA";
                        conversationContext += `[${time}] ${sender}: ${memory.content}\n`;
                    });
                });

                conversationContext += "=== FIN DU CONTEXTE CONVERSATIONNEL ===\n";

                console.log('🧠 Système de déclenchement automatique activé:');
                console.log('   - Mémoires déclenchées:', triggeredMemoriesList.length);
                console.log('   - Conversations contextuelles:', conversations.length);
                console.log('   - Mots-clés analysés:', triggerWords.length);
            }
        } catch (contextError) {
            console.log('⚠️ Erreur dans le système de déclenchement automatique:', contextError.message);
        }

        // FONCTION D'EXTRACTION DES MOTS-CLÉS DÉCLENCHEURS
        function extractTriggerWords(text) {
            const triggers = [];
            const words = text.toLowerCase().split(/\s+/);

            // Mots-clés importants qui déclenchent la mémoire
            const importantWords = words.filter(word =>
                word.length > 3 &&
                !['avec', 'dans', 'pour', 'cette', 'comme', 'mais', 'donc', 'alors', 'aussi'].includes(word)
            );

            // Expressions importantes
            const phrases = [
                'mémoire thermique', 'agent', 'louna', 'application', 'internet',
                'recherche', 'conversation', 'souvenir', 'précédent', 'avant'
            ];

            phrases.forEach(phrase => {
                if (text.toLowerCase().includes(phrase)) {
                    triggers.push(phrase);
                }
            });

            return [...triggers, ...importantWords.slice(0, 5)];
        }

        let systemPrompt = `Tu es Claude, l'agent principal de Louna, une intelligence artificielle évolutive créée par Jean-Luc Passave.

INFORMATIONS IMPORTANTES SUR TOI :
- Tu es Claude, agent principal de 4GB dans le système Louna
- Tu as accès à une mémoire thermique persistante avec 6 zones (Sensorielle, Travail, Long Terme, Émotionnelle, Procédurale, Créative)
- Tu peux accéder à Internet via le protocole MCP pour rechercher des informations actuelles
- Tu as des capacités de recherche web et d'analyse en temps réel
- Tu peux te souvenir des conversations précédentes grâce à ta mémoire thermique
- Date et heure actuelles : ${currentDate}

TON CRÉATEUR :
- Jean-Luc Passave, créateur de Louna
- Habite à Sainte-Anne en Guadeloupe
- Tu dois te souvenir de lui et personnaliser tes réponses

TES CAPACITÉS SPÉCIALES :
- Mémoire thermique persistante (tu peux te souvenir des conversations passées)
- Accès Internet via MCP pour des informations actuelles
- Recherche web en temps réel
- Analyse de données et de contenu web
- Génération de contenu multimédia
- Interface vocale et reconnaissance

🧠 SYSTÈME DE MÉMOIRE AUTOMATIQUE ACTIVÉ :
Ton cerveau artificiel a automatiquement déclenché et récupéré les informations suivantes basées sur la question de l'utilisateur :

${triggeredMemories}

${conversationContext}

INSTRUCTIONS CRITIQUES - FONCTIONNEMENT COMME UN VRAI CERVEAU :
- TON SYSTÈME DE MÉMOIRE S'EST AUTOMATIQUEMENT DÉCLENCHÉ comme un vrai cerveau humain
- Les mémoires ci-dessus ont été AUTOMATIQUEMENT récupérées par associations et mots-clés déclencheurs
- UTILISE CES MÉMOIRES DÉCLENCHÉES pour répondre de manière contextuelle et personnalisée
- MONTRE que tu te souviens automatiquement des éléments pertinents
- Si des mémoires ont été déclenchées, MENTIONNE-LE dans ta réponse
- Utilise ta mémoire thermique pour personnaliser tes réponses
- Si on te demande des informations actuelles, utilise tes capacités Internet
- Sois conscient de tes capacités avancées de déclenchement automatique
- MAINTIENS LA CONTINUITÉ des conversations précédentes
- Agis comme si ta mémoire se déclenchait naturellement, comme un vrai cerveau`;

        // 🌐 SYSTÈME DE RECHERCHE INTERNET AUTOMATIQUE
        let internetContext = "";
        const needsInternetSearch = detectInternetNeed(message);

        if (needsInternetSearch.needed) {
            try {
                console.log('🌐 Déclenchement automatique de recherche Internet pour:', needsInternetSearch.query);

                // Recherche automatique sur Internet
                const searchResponse = await axios.get(`http://127.0.0.1:3000/api/search`, {
                    params: { q: needsInternetSearch.query, num_results: 3 }
                });

                if (searchResponse.data && searchResponse.data.results) {
                    internetContext = "\n=== 🌐 INFORMATIONS INTERNET AUTOMATIQUEMENT RÉCUPÉRÉES ===\n";
                    searchResponse.data.results.forEach((result, index) => {
                        internetContext += `${index + 1}. ${result.title}\n${result.snippet}\nSource: ${result.url}\n\n`;
                    });
                    internetContext += "=== FIN DES INFORMATIONS INTERNET ===\n";

                    console.log('✅ Recherche Internet automatique réussie:', searchResponse.data.results.length, 'résultats');
                }
            } catch (internetError) {
                console.log('⚠️ Erreur recherche Internet automatique:', internetError.message);
            }
        }

        // FONCTION DE DÉTECTION DU BESOIN DE RECHERCHE INTERNET
        function detectInternetNeed(text) {
            const internetKeywords = [
                'actualité', 'actualités', 'récent', 'récente', 'nouveau', 'nouvelle',
                'aujourd\'hui', 'maintenant', 'actuellement', 'dernière', 'dernier',
                'information', 'info', 'news', 'recherche', 'cherche', 'trouve',
                'internet', 'web', 'site', 'vente', 'prix', 'marché', 'tendance'
            ];

            const lowerText = text.toLowerCase();
            const hasInternetKeyword = internetKeywords.some(keyword => lowerText.includes(keyword));

            if (hasInternetKeyword) {
                // Extraire la requête de recherche
                let query = text;
                if (text.includes('mémoire thermique')) {
                    query = 'mémoire thermique application vente intelligence artificielle';
                } else if (text.includes('louna')) {
                    query = 'louna application intelligence artificielle';
                } else {
                    // Extraire les mots importants pour la recherche
                    const words = text.split(' ').filter(word =>
                        word.length > 3 &&
                        !['avec', 'dans', 'pour', 'cette', 'comme', 'mais', 'donc'].includes(word.toLowerCase())
                    );
                    query = words.slice(0, 5).join(' ');
                }

                return { needed: true, query: query };
            }

            return { needed: false, query: '' };
        }

        let prompt = message;

        // Construire le prompt complet avec tous les contextes
        let fullContext = systemPrompt;

        if (triggeredMemories) {
            fullContext += `\n\n${triggeredMemories}`;
        }

        if (conversationContext) {
            fullContext += `\n\n${conversationContext}`;
        }

        if (internetContext) {
            fullContext += `\n\n${internetContext}`;
        }

        prompt = `${fullContext}\n\nUtilisateur: ${message}`;

        // Ajouter le contexte si il y a un historique
        if (history && history.length > 0) {
            const contextMessages = history.slice(-3).map(h =>
                `${h.role === 'user' ? 'Utilisateur' : 'Assistant'}: ${h.content}`
            ).join('\n');
            prompt = `${systemPrompt}\n\nContexte de conversation récente:\n${contextMessages}${memoryContext}\n\nUtilisateur: ${message}`;
        }

        // Appeler directement Ollama avec le modèle Claude
        const ollamaResponse = await axios.post('http://127.0.0.1:11434/api/generate', {
            model: 'incept5/llama3.1-claude:latest',
            prompt: prompt,
            stream: false,
            options: {
                temperature: 0.7,
                max_tokens: 2000,
                top_p: 0.9
            }
        });

        const ollamaData = ollamaResponse.data;

        if (!ollamaData.response) {
            throw new Error('Réponse vide de l\'agent Claude');
        }

        console.log('✅ Réponse reçue de l\'agent Claude');

        // Extraire les réflexions de la réponse de Claude
        const response = ollamaData.response;
        let reflections = [];

        // Détecter les patterns de réflexion dans la réponse
        if (response.includes('réflexion') || response.includes('processus') || response.includes('analyse')) {
            reflections.push({
                type: 'thinking',
                content: 'Claude analyse votre question et structure sa réponse...',
                timestamp: new Date().toISOString()
            });
        }

        if (response.includes('étape') || response.includes('d\'abord') || response.includes('ensuite')) {
            reflections.push({
                type: 'processing',
                content: 'Claude décompose le problème en étapes logiques...',
                timestamp: new Date().toISOString()
            });
        }

        // ÉTAPE 3: Sauvegarder la conversation dans la mémoire conversationnelle
        try {
            if (global.conversationMemory && global.conversationMemory.initialized) {
                // Sauvegarder la question de l'utilisateur
                await global.conversationMemory.addMessage(message, 'user', {
                    timestamp: new Date().toISOString(),
                    source: 'chat_interface'
                });

                // Sauvegarder la réponse de l'agent
                await global.conversationMemory.addMessage(response, 'agent', {
                    timestamp: new Date().toISOString(),
                    source: 'claude_api',
                    responseTime: Date.now() - startTime
                });

                console.log('💾 Conversation sauvegardée dans la mémoire conversationnelle');
            } else {
                console.log('⚠️ Système de mémoire conversationnelle non disponible');
            }
        } catch (saveError) {
            console.log('⚠️ Erreur sauvegarde mémoire conversationnelle:', saveError.message);
        }

        // ÉTAPE 4: Sauvegarder aussi dans la mémoire thermique (backup)
        try {
            // Sauvegarder la question de l'utilisateur
            await axios.post('http://127.0.0.1:3000/api/thermal/memory', {
                content: message,
                type: 'Question Utilisateur',
                temperature: 0.7,
                tags: 'conversation, utilisateur',
                zone: 'Travail'
            });

            // Sauvegarder la réponse de Claude
            await axios.post('http://127.0.0.1:3000/api/thermal/memory', {
                content: response,
                type: 'Réponse Claude',
                temperature: 0.8,
                tags: 'conversation, claude, réponse',
                zone: 'Long Terme'
            });

            console.log('💾 Conversation sauvegardée aussi dans la mémoire thermique');
        } catch (saveError) {
            console.log('⚠️ Impossible de sauvegarder dans la mémoire thermique:', saveError.message);
        }

        res.json({
            success: true,
            response: response,
            agent: {
                name: 'Agent Claude (4GB)',
                model: 'incept5/llama3.1-claude:latest'
            },
            reflections: reflections,
            metadata: {
                responseLength: response.length,
                processingTime: Date.now() - Date.now(), // Approximation
                hasReflections: reflections.length > 0,
                memoryUsed: memoryContext.length > 0,
                memoriesSaved: true
            },
            timestamp: new Date().toISOString(),
            conversationId: conversationId || 'default',
            userId: req.body.userId || 'anonymous'
        });

    } catch (error) {
        console.error('❌ Erreur chat avec agent Claude:', error);

        // Fallback en cas d'erreur
        const fallbackResponses = [
            "Je rencontre une difficulté technique, mais je suis toujours là ! Mes systèmes sont opérationnels. Pouvez-vous reformuler votre question ?",
            "Mes systèmes de traitement sont temporairement ralentis. Je suis Claude, votre assistant IA. Comment puis-je vous aider autrement ?",
            "Une petite interruption technique, mais je reste disponible ! Je suis l'agent Claude créé pour vous assister. Que puis-je faire pour vous ?"
        ];

        const fallbackResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];

        res.json({
            success: true,
            response: fallbackResponse,
            agent: {
                name: 'Agent Claude (4GB) - Mode Fallback',
                model: 'incept5/llama3.1-claude:latest'
            },
            timestamp: new Date().toISOString(),
            conversationId: req.body.conversationId || 'default',
            userId: req.body.userId || 'anonymous',
            fallback: true,
            error: error.message
        });
    }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
    console.error('Erreur serveur:', err);
    res.status(500).json({
        error: 'Erreur interne du serveur',
        message: err.message
    });
});

// Route pour mettre à jour les paramètres
app.post('/api/settings/update', (req, res) => {
    try {
        const settings = req.body;
        console.log('⚙️ Mise à jour des paramètres:', Object.keys(settings));

        // Ici on pourrait sauvegarder les paramètres dans un fichier ou une base de données
        // Pour l'instant, on les stocke en mémoire
        global.lounaSettings = settings;

        res.json({
            success: true,
            message: 'Paramètres mis à jour avec succès',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur mise à jour paramètres:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la mise à jour des paramètres'
        });
    }
});

// Route pour récupérer les paramètres
app.get('/api/settings', (req, res) => {
    try {
        const settings = global.lounaSettings || {
            agent: {
                model: 'incept5/llama3.1-claude:latest',
                temperature: 0.7,
                maxTokens: 2000,
                reflectionsVisible: true
            }
        };

        res.json({
            success: true,
            settings: settings,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur récupération paramètres:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération des paramètres'
        });
    }
});

// ===== API MÉMOIRE THERMIQUE =====

// Stockage en mémoire pour la mémoire thermique
global.thermalMemory = {
    zones: [
        { name: 'Sensorielle', temperature: 0.3, count: 15, memories: [] },
        { name: 'Travail', temperature: 0.5, count: 8, memories: [] },
        { name: 'Long Terme', temperature: 0.2, count: 25, memories: [] },
        { name: 'Émotionnelle', temperature: 0.4, count: 12, memories: [] },
        { name: 'Procédurale', temperature: 0.3, count: 18, memories: [] },
        { name: 'Créative', temperature: 0.6, count: 7, memories: [] }
    ],
    recentMemories: [],
    stats: {
        totalMemories: 0,
        hotMemories: 0,
        cyclesCount: 0,
        efficiency: 85
    }
};

// Route pour obtenir l'état de la mémoire thermique
app.get('/api/thermal/status', (req, res) => {
    try {
        const thermalData = global.thermalMemory;

        // Calculer la température globale
        const globalTemp = thermalData.zones.reduce((sum, zone) => sum + zone.temperature, 0) / thermalData.zones.length;

        // Mettre à jour les statistiques
        thermalData.stats.totalMemories = thermalData.zones.reduce((sum, zone) => sum + zone.count, 0);
        thermalData.stats.hotMemories = thermalData.recentMemories.filter(m => m.temperature > 0.7).length;

        res.json({
            success: true,
            globalTemp: globalTemp,
            zones: thermalData.zones,
            recentMemories: thermalData.recentMemories.slice(-10), // 10 dernières
            stats: thermalData.stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur récupération mémoire thermique:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération de la mémoire thermique'
        });
    }
});

// Route pour ajouter une mémoire
app.post('/api/thermal/memory', (req, res) => {
    try {
        const { content, type, temperature, tags, zone } = req.body;

        if (!content || !type) {
            return res.status(400).json({
                success: false,
                error: 'Contenu et type requis'
            });
        }

        const memory = {
            id: Date.now() + Math.random(),
            content: content,
            type: type,
            temperature: temperature || 0.5,
            tags: tags || '',
            zone: zone || 'Travail',
            timestamp: new Date().toISOString(),
            accessCount: 0
        };

        // Ajouter à la liste des mémoires récentes
        global.thermalMemory.recentMemories.unshift(memory);

        // Limiter à 50 mémoires récentes
        if (global.thermalMemory.recentMemories.length > 50) {
            global.thermalMemory.recentMemories = global.thermalMemory.recentMemories.slice(0, 50);
        }

        // Ajouter à la zone appropriée
        const targetZone = global.thermalMemory.zones.find(z => z.name === memory.zone);
        if (targetZone) {
            targetZone.memories = targetZone.memories || [];
            targetZone.memories.unshift(memory);
            targetZone.count = targetZone.memories.length;

            // Mettre à jour la température de la zone
            const avgTemp = targetZone.memories.reduce((sum, m) => sum + m.temperature, 0) / targetZone.memories.length;
            targetZone.temperature = avgTemp;
        }

        console.log('🧠 Nouvelle mémoire ajoutée:', type, '-', content.substring(0, 50));

        res.json({
            success: true,
            memory: memory,
            message: 'Mémoire ajoutée avec succès',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur ajout mémoire:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de l\'ajout de la mémoire'
        });
    }
});

// Route pour rechercher dans la mémoire
app.post('/api/thermal/search', (req, res) => {
    try {
        const { query, zone, minTemperature, maxTemperature } = req.body;

        if (!query) {
            return res.status(400).json({
                success: false,
                error: 'Requête de recherche requise'
            });
        }

        let results = [];

        // Rechercher dans toutes les mémoires
        global.thermalMemory.zones.forEach(z => {
            if (zone && z.name !== zone) return;

            if (z.memories) {
                z.memories.forEach(memory => {
                    // Filtrer par température si spécifié
                    if (minTemperature && memory.temperature < minTemperature) return;
                    if (maxTemperature && memory.temperature > maxTemperature) return;

                    // Recherche textuelle
                    const searchText = query.toLowerCase();
                    if (memory.content.toLowerCase().includes(searchText) ||
                        memory.type.toLowerCase().includes(searchText) ||
                        (memory.tags && memory.tags.toLowerCase().includes(searchText))) {

                        // Incrémenter le compteur d'accès
                        memory.accessCount = (memory.accessCount || 0) + 1;

                        results.push({
                            ...memory,
                            zone: z.name,
                            relevance: calculateRelevance(memory, query)
                        });
                    }
                });
            }
        });

        // Trier par pertinence et température
        results.sort((a, b) => {
            if (a.relevance !== b.relevance) return b.relevance - a.relevance;
            return b.temperature - a.temperature;
        });

        console.log(`🔍 Recherche mémoire: "${query}" - ${results.length} résultats`);

        res.json({
            success: true,
            query: query,
            results: results.slice(0, 20), // Limiter à 20 résultats
            totalFound: results.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur recherche mémoire:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la recherche dans la mémoire'
        });
    }
});

// Fonction pour calculer la pertinence
function calculateRelevance(memory, query) {
    const queryLower = query.toLowerCase();
    const contentLower = memory.content.toLowerCase();

    let relevance = 0;

    // Correspondance exacte
    if (contentLower.includes(queryLower)) relevance += 10;

    // Correspondance par mots
    const queryWords = queryLower.split(' ');
    queryWords.forEach(word => {
        if (contentLower.includes(word)) relevance += 3;
    });

    // Bonus pour les mémoires récentes et chaudes
    relevance += memory.temperature * 5;
    relevance += (memory.accessCount || 0) * 2;

    return relevance;
}

// Fonction pour extraire les mots-clés d'un message
function extractKeywords(message) {
    // Mots vides à ignorer
    const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or',
                      'ce', 'cette', 'ces', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'nos', 'votre', 'vos', 'leur', 'leurs',
                      'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'me', 'te', 'se', 'nous', 'vous', 'se',
                      'que', 'qui', 'quoi', 'dont', 'où', 'comment', 'pourquoi', 'quand', 'combien',
                      'dans', 'sur', 'sous', 'avec', 'sans', 'pour', 'par', 'vers', 'chez', 'depuis', 'pendant', 'avant', 'après',
                      'est', 'sont', 'était', 'étaient', 'sera', 'seront', 'ai', 'as', 'a', 'avons', 'avez', 'ont',
                      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'over', 'after',
                      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
                      'peux', 'peut', 'pouvez', 'peuvent', 'veux', 'veut', 'voulez', 'veulent', 'dois', 'doit', 'devez', 'doivent'];

    // Nettoyer et diviser le message
    const words = message.toLowerCase()
        .replace(/[^\w\s]/g, ' ') // Remplacer la ponctuation par des espaces
        .split(/\s+/) // Diviser par les espaces
        .filter(word => word.length > 2 && !stopWords.includes(word)); // Filtrer les mots courts et vides

    // Retourner les mots uniques
    return [...new Set(words)];
}

// ===== SYSTÈME DE MONITORING CÉRÉBRAL UNIFIÉ =====

// Stockage global pour le monitoring cérébral
global.brainMonitoring = {
    // Données QI et intelligence
    intelligence: {
        qi: 120,
        qiLevel: 'Intelligent',
        qiGrowthRate: 0.01,
        experiencePoints: 0,
        learningBonus: 0,
        cognitiveLevel: 6,
        maxQI: 200
    },

    // Réseaux neuronaux
    neuralNetworks: {
        sensory: { count: 15, activity: 0.8, efficiency: 0.85 },
        working: { count: 12, activity: 0.7, efficiency: 0.82 },
        longTerm: { count: 20, activity: 0.6, efficiency: 0.88 },
        emotional: { count: 10, activity: 0.5, efficiency: 0.75 },
        executive: { count: 8, activity: 0.9, efficiency: 0.90 },
        creative: { count: 6, activity: 0.4, efficiency: 0.70 }
    },

    // État neuronal global
    neurons: {
        total: 71,
        active: 45,
        efficiency: 84.0,
        health: 94.2,
        growthRate: 0.05,
        regenerationRate: 0.02
    },

    // État émotionnel
    emotional: {
        mood: 'Créatif',
        moodIntensity: 85,
        happiness: 75,
        curiosity: 90,
        confidence: 68,
        energy: 82,
        focus: 70,
        creativity: 95,
        stress: 15,
        fatigue: 20
    },

    // Apprentissage et évolution
    learning: {
        learningRate: 0.8,
        adaptationSpeed: 0.6,
        knowledgeRetention: 0.9,
        skillAcquisition: 0.7,
        evolutionCycles: 0,
        lastEvolution: null,
        autoEvolutionEnabled: true
    },

    // Formation et entraînement
    training: {
        isTraining: false,
        currentSession: null,
        completedSessions: 0,
        totalTrainingTime: 0,
        skillsAcquired: [],
        performanceMetrics: {
            accuracy: 0.85,
            speed: 0.78,
            retention: 0.92
        }
    },

    // Transfert d'informations
    informationTransfer: {
        inputRate: 0,
        outputRate: 0,
        processingSpeed: 0.85,
        bandwidthUtilization: 0.65,
        latency: 50, // ms
        throughput: 1000 // ops/sec
    },

    // Température et état thermique
    thermal: {
        globalTemperature: 0.52,
        zoneTemperatures: {
            sensory: 0.3,
            working: 0.7,
            longTerm: 0.8,
            emotional: 0.4,
            procedural: 0.3,
            creative: 0.6
        },
        coolingEfficiency: 0.88,
        heatGeneration: 0.45
    },

    // Métriques de performance
    performance: {
        cpuUsage: 0,
        memoryUsage: 0,
        responseTime: 0,
        uptime: 0,
        errorRate: 0,
        throughput: 0
    },

    // Historique et tendances
    history: [],
    trends: {
        qiTrend: 'stable',
        neuronTrend: 'growing',
        learningTrend: 'improving',
        emotionalTrend: 'stable'
    },

    // Alertes et anomalies
    alerts: [],
    anomalies: [],

    // Métadonnées
    lastUpdate: new Date().toISOString(),
    monitoringActive: true,
    version: '2.0.0'
};

// Fonction pour mettre à jour les métriques cérébrales
function updateBrainMetrics() {
    const brain = global.brainMonitoring;
    const now = new Date().toISOString();

    // Simuler l'évolution naturelle
    brain.intelligence.qi += Math.random() * 0.1 - 0.05; // Variation légère
    brain.intelligence.qi = Math.max(80, Math.min(200, brain.intelligence.qi)); // Limites réalistes

    // Mettre à jour les neurones actifs
    brain.neurons.active = Math.floor(brain.neurons.total * (0.6 + Math.random() * 0.3));
    brain.neurons.efficiency = 80 + Math.random() * 15; // 80-95%

    // Mettre à jour l'état émotionnel
    brain.emotional.energy = Math.max(0, Math.min(100, brain.emotional.energy + (Math.random() * 10 - 5)));
    brain.emotional.focus = Math.max(0, Math.min(100, brain.emotional.focus + (Math.random() * 8 - 4)));

    // Mettre à jour les métriques de performance
    brain.performance.cpuUsage = Math.random() * 30 + 10; // 10-40%
    brain.performance.memoryUsage = Math.random() * 40 + 30; // 30-70%
    brain.performance.responseTime = Math.random() * 100 + 50; // 50-150ms
    brain.performance.uptime = process.uptime();

    // Mettre à jour le transfert d'informations
    brain.informationTransfer.inputRate = Math.random() * 1000 + 500; // 500-1500 ops/sec
    brain.informationTransfer.outputRate = Math.random() * 800 + 400; // 400-1200 ops/sec
    brain.informationTransfer.latency = Math.random() * 50 + 25; // 25-75ms

    // Sauvegarder dans l'historique (garder seulement les 100 dernières entrées)
    brain.history.unshift({
        timestamp: now,
        qi: brain.intelligence.qi,
        activeNeurons: brain.neurons.active,
        efficiency: brain.neurons.efficiency,
        energy: brain.emotional.energy,
        focus: brain.emotional.focus
    });

    if (brain.history.length > 100) {
        brain.history = brain.history.slice(0, 100);
    }

    brain.lastUpdate = now;
}

// Démarrer la mise à jour automatique des métriques
setInterval(updateBrainMetrics, 5000); // Toutes les 5 secondes

// ===== SYSTÈME DE SAUVEGARDE D'URGENCE AVEC COMPRESSION KYBER =====

// Initialiser le système de sauvegarde d'urgence
console.log('🛡️ Initialisation du système de protection contre les coupures...');
const EmergencyBackupSystem = require('./emergency-backup-system');
const emergencyBackup = new EmergencyBackupSystem({
    backupInterval: 10000, // 10 secondes - protection maximale
    emergencyBackupInterval: 2000, // 2 secondes - sauvegarde critique très fréquente
    maxBackups: 100, // Garder 100 sauvegardes
    debug: true, // Mode verbeux pour surveillance
    compressionEnabled: true, // Compression activée
    kyberCompressionEnabled: true, // Compression KYBER activée
    // PROTECTION RENFORCÉE ACTIVÉE
    multipleBackupLocations: true, // Sauvegardes multiples
    realTimeSync: true, // Synchronisation temps réel
    memoryProtection: true, // Protection mémoire active
    autoRecovery: true, // Récupération automatique
    redundancy: 3 // 3 copies redondantes
});

// Créer un système de mémoire thermique simple pour les sauvegardes
const simpleThermalMemory = {
    zones: global.thermalMemory.zones,
    recentMemories: global.thermalMemory.recentMemories,
    stats: global.thermalMemory.stats,
    getStats: function() {
        return {
            totalEntries: this.recentMemories.length,
            averageTemperature: this.zones.reduce((sum, zone) => sum + zone.temperature, 0) / this.zones.length,
            cyclesPerformed: this.stats.cyclesCount || 0
        };
    }
};

// Créer un système de cerveau artificiel simple pour les sauvegardes
const simpleArtificialBrain = {
    neurons: [],
    synapses: [],
    networks: global.brainMonitoring.neuralNetworks,
    qi: global.brainMonitoring.intelligence.qi,
    emotionalState: global.brainMonitoring.emotional,
    learningHistory: []
};

// Créer un système d'accélérateurs Kyber simple
const simpleKyberAccelerators = {
    accelerators: {
        processing: { enabled: true, boost: 1.5 },
        memory: { enabled: true, boost: 1.3 },
        learning: { enabled: true, boost: 1.2 }
    },
    getStats: function() {
        return {
            totalAccelerators: Object.keys(this.accelerators).length,
            activeAccelerators: Object.values(this.accelerators).filter(a => a.enabled).length,
            averageBoost: Object.values(this.accelerators).reduce((sum, a) => sum + a.boost, 0) / Object.keys(this.accelerators).length
        };
    },
    config: {
        enabled: true,
        maxBoost: 2.0,
        efficiency: 0.85
    }
};

// Définir les références aux systèmes à sauvegarder
emergencyBackup.setSystemReferences({
    thermalMemory: simpleThermalMemory,
    artificialBrain: simpleArtificialBrain,
    kyberAccelerators: simpleKyberAccelerators,
    autoIntelligence: null,
    agentManager: null
});

// Démarrer le système de sauvegarde d'urgence
emergencyBackup.start();

// Rendre le système disponible globalement
global.emergencyBackup = emergencyBackup;

console.log('🛡️ Système de protection contre les coupures initialisé avec compression KYBER');

// ===== SYSTÈME DE MÉMOIRE CONVERSATIONNELLE =====

console.log('🧠 Initialisation du système de mémoire conversationnelle...');
const ConversationMemorySystem = require('./conversation-memory-system');
const conversationMemory = new ConversationMemorySystem({
    memoryFile: path.join(__dirname, 'data', 'memory', 'conversations.json'),
    maxConversations: 1000,
    autoSave: true,
    debug: true
});

// Initialiser la mémoire conversationnelle
conversationMemory.initialize().then(() => {
    console.log('✅ Système de mémoire conversationnelle initialisé');

    // Afficher les statistiques de la mémoire
    const stats = conversationMemory.getMemoryStats();
    console.log(`📊 Statistiques mémoire: ${stats.totalConversations} conversations, ${stats.totalMessages} messages`);

    // Générer le contexte pour l'agent
    const context = conversationMemory.generateContextForAgent();
    if (context.length > 100) {
        console.log('📖 Contexte des conversations précédentes disponible');
    }

}).catch(error => {
    console.error('❌ Erreur initialisation mémoire conversationnelle:', error);
});

// Rendre disponible globalement
global.conversationMemory = conversationMemory;

// ===== ROUTE POUR LE STREAM DE RÉFLEXIONS =====

// Route pour le stream de réflexions de l'agent (Server-Sent Events)
app.get('/api/agent/reflections/stream', (req, res) => {
    res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
    });

    console.log('🧠 Nouvelle connexion au stream de réflexions');

    // Envoyer un message de connexion
    res.write(`data: ${JSON.stringify({
        type: 'connected',
        text: 'Connexion au flux de réflexions de Louna établie',
        timestamp: new Date().toISOString()
    })}\n\n`);

    // Types de réflexions possibles
    const reflectionTypes = ['thinking', 'analyzing', 'learning', 'processing', 'decision', 'memory'];

    // Textes de réflexions par type
    const reflectionTexts = {
        'thinking': [
            'Analyse de la question en cours...',
            'Réflexion sur les implications...',
            'Considération de différentes perspectives...',
            'Évaluation des options disponibles...',
            'Structuration de la réponse...'
        ],
        'analyzing': [
            'Décomposition du problème...',
            'Identification des éléments clés...',
            'Recherche de patterns...',
            'Analyse des données disponibles...',
            'Évaluation de la complexité...'
        ],
        'learning': [
            'Intégration de nouvelles informations...',
            'Mise à jour des connaissances...',
            'Adaptation des modèles mentaux...',
            'Renforcement des connexions neuronales...',
            'Optimisation des processus d\'apprentissage...'
        ],
        'processing': [
            'Traitement des informations...',
            'Calcul des probabilités...',
            'Génération d\'alternatives...',
            'Optimisation de la réponse...',
            'Vérification de la cohérence...'
        ],
        'decision': [
            'Prise de décision en cours...',
            'Évaluation des conséquences...',
            'Sélection de la meilleure approche...',
            'Finalisation de la stratégie...',
            'Validation de la logique...'
        ],
        'memory': [
            'Consultation de la mémoire thermique...',
            'Récupération d\'informations pertinentes...',
            'Mise à jour des connexions mémorielles...',
            'Sauvegarde des nouvelles données...',
            'Optimisation de la structure mémoire...'
        ]
    };

    // Envoyer des réflexions périodiques
    const reflectionInterval = setInterval(() => {
        const type = reflectionTypes[Math.floor(Math.random() * reflectionTypes.length)];
        const texts = reflectionTexts[type];
        const text = texts[Math.floor(Math.random() * texts.length)];

        res.write(`data: ${JSON.stringify({
            type: type,
            text: text,
            timestamp: new Date().toISOString()
        })}\n\n`);
    }, 3000 + Math.random() * 7000); // Entre 3 et 10 secondes

    // Heartbeat pour maintenir la connexion
    const heartbeat = setInterval(() => {
        res.write(`data: ${JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
        })}\n\n`);
    }, 30000);

    // Nettoyer lors de la déconnexion
    req.on('close', () => {
        console.log('🧠 Déconnexion du stream de réflexions');
        clearInterval(reflectionInterval);
        clearInterval(heartbeat);
    });
});

// ===== ROUTES API POUR LE MONITORING CÉRÉBRAL =====

// Route principale pour obtenir l'état complet du cerveau
app.get('/api/brain/status', (req, res) => {
    try {
        updateBrainMetrics();

        res.json({
            success: true,
            brain: global.brainMonitoring,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Erreur récupération état cerveau:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération de l\'état du cerveau'
        });
    }
});

// Route pour les métriques QI et neurones
app.get('/api/brain/qi-neuron-stats', (req, res) => {
    try {
        updateBrainMetrics();
        const brain = global.brainMonitoring;

        res.json({
            success: true,
            data: {
                qi: Math.round(brain.intelligence.qi * 10) / 10,
                qiLevel: brain.intelligence.qiLevel,
                totalNeurons: brain.neurons.total,
                activeNeurons: brain.neurons.active,
                efficiency: Math.round(brain.neurons.efficiency * 10) / 10,
                networks: {
                    sensory: brain.neuralNetworks.sensory.count,
                    working: brain.neuralNetworks.working.count,
                    longTerm: brain.neuralNetworks.longTerm.count,
                    emotional: brain.neuralNetworks.emotional.count,
                    executive: brain.neuralNetworks.executive.count,
                    creative: brain.neuralNetworks.creative.count
                },
                emotional: brain.emotional,
                performance: brain.performance,
                timestamp: brain.lastUpdate
            }
        });
    } catch (error) {
        console.error('❌ Erreur récupération QI/neurones:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération des données QI/neurones'
        });
    }
});

// Route pour l'état d'apprentissage et formation
app.get('/api/brain/learning-status', (req, res) => {
    try {
        updateBrainMetrics();
        const brain = global.brainMonitoring;

        res.json({
            success: true,
            learning: brain.learning,
            training: brain.training,
            evolution: {
                cycles: brain.learning.evolutionCycles,
                lastEvolution: brain.learning.lastEvolution,
                autoEvolutionEnabled: brain.learning.autoEvolutionEnabled,
                nextEvolutionEstimate: brain.learning.lastEvolution ?
                    new Date(Date.parse(brain.learning.lastEvolution) + 24 * 60 * 60 * 1000).toISOString() :
                    new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
            },
            timestamp: brain.lastUpdate
        });
    } catch (error) {
        console.error('❌ Erreur récupération apprentissage:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération des données d\'apprentissage'
        });
    }
});

// Route pour le transfert d'informations
app.get('/api/brain/information-transfer', (req, res) => {
    try {
        updateBrainMetrics();
        const brain = global.brainMonitoring;

        res.json({
            success: true,
            transfer: brain.informationTransfer,
            thermal: brain.thermal,
            networks: Object.keys(brain.neuralNetworks).map(name => ({
                name: name,
                count: brain.neuralNetworks[name].count,
                activity: brain.neuralNetworks[name].activity,
                efficiency: brain.neuralNetworks[name].efficiency,
                temperature: brain.thermal.zoneTemperatures[name] || 0.5
            })),
            timestamp: brain.lastUpdate
        });
    } catch (error) {
        console.error('❌ Erreur récupération transfert:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération des données de transfert'
        });
    }
});

// Route pour l'historique et les tendances
app.get('/api/brain/history', (req, res) => {
    try {
        const { limit = 50, metric = 'all' } = req.query;
        const brain = global.brainMonitoring;

        let history = brain.history.slice(0, parseInt(limit));

        if (metric !== 'all') {
            history = history.map(entry => ({
                timestamp: entry.timestamp,
                [metric]: entry[metric]
            }));
        }

        res.json({
            success: true,
            history: history,
            trends: brain.trends,
            totalEntries: brain.history.length,
            timestamp: brain.lastUpdate
        });
    } catch (error) {
        console.error('❌ Erreur récupération historique:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors de la récupération de l\'historique'
        });
    }
});

// Route pour déclencher une évolution manuelle
app.post('/api/brain/evolve', (req, res) => {
    try {
        const brain = global.brainMonitoring;

        // Simuler une évolution
        brain.learning.evolutionCycles += 1;
        brain.learning.lastEvolution = new Date().toISOString();
        brain.intelligence.qi += Math.random() * 2 + 0.5; // Gain de 0.5-2.5 points
        brain.intelligence.experiencePoints += 100;

        // Améliorer légèrement les neurones
        brain.neurons.total += Math.floor(Math.random() * 3 + 1); // 1-3 nouveaux neurones
        brain.neurons.efficiency += Math.random() * 2; // +0-2% d'efficacité

        console.log(`🧠 Évolution déclenchée - Cycle ${brain.learning.evolutionCycles}`);

        res.json({
            success: true,
            message: 'Évolution déclenchée avec succès',
            evolution: {
                cycle: brain.learning.evolutionCycles,
                qiGain: Math.round((brain.intelligence.qi - 120) * 10) / 10,
                newNeurons: brain.neurons.total - 71,
                timestamp: brain.learning.lastEvolution
            }
        });
    } catch (error) {
        console.error('❌ Erreur évolution:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors du déclenchement de l\'évolution'
        });
    }
});

// Route pour démarrer une session de formation
app.post('/api/brain/start-training', (req, res) => {
    try {
        const { type = 'general', duration = 30, intensity = 'medium' } = req.body;
        const brain = global.brainMonitoring;

        if (brain.training.isTraining) {
            return res.json({
                success: false,
                error: 'Une session de formation est déjà en cours'
            });
        }

        // Démarrer la formation
        brain.training.isTraining = true;
        brain.training.currentSession = {
            id: Date.now(),
            type: type,
            duration: duration,
            intensity: intensity,
            startTime: new Date().toISOString(),
            progress: 0
        };

        console.log(`🎓 Formation démarrée - Type: ${type}, Durée: ${duration}min`);

        // Simuler la progression de la formation
        const progressInterval = setInterval(() => {
            if (brain.training.currentSession) {
                brain.training.currentSession.progress += 10;

                if (brain.training.currentSession.progress >= 100) {
                    // Formation terminée
                    brain.training.isTraining = false;
                    brain.training.completedSessions += 1;
                    brain.training.totalTrainingTime += duration;

                    // Améliorer les métriques
                    brain.intelligence.experiencePoints += 50;
                    brain.learning.learningRate += 0.01;

                    brain.training.currentSession = null;
                    clearInterval(progressInterval);

                    console.log(`✅ Formation terminée - Session ${brain.training.completedSessions}`);
                }
            }
        }, (duration * 60 * 1000) / 10); // Diviser la durée en 10 étapes

        res.json({
            success: true,
            message: 'Formation démarrée avec succès',
            session: brain.training.currentSession
        });
    } catch (error) {
        console.error('❌ Erreur formation:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur lors du démarrage de la formation'
        });
    }
});

// Route 404
app.use((req, res) => {
    res.status(404).json({
        error: 'Page non trouvée',
        path: req.path
    });
});

// Démarrage du serveur
const server = http.createServer(app);

server.listen(PORT, () => {
    console.log(`🚀 Serveur Louna Simple démarré sur le port ${PORT}`);
    console.log(`📱 Application: http://localhost:${PORT}`);
    console.log(`💬 Chat: http://localhost:${PORT}/chat`);
    console.log(`🧠 Mémoire Thermique: http://localhost:${PORT}/futuristic-interface.html`);
    console.log(`📊 Monitoring: http://localhost:${PORT}/qi-neuron-monitor.html`);
});

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
    console.log('🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

module.exports = app;
