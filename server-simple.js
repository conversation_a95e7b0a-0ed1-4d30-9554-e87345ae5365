#!/usr/bin/env node

const express = require('express');
const path = require('path');
const http = require('http');

// Configuration simple
const PORT = process.env.PORT || 3000;
const app = express();

// Middleware basique
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Servir les fichiers statiques
app.use(express.static(path.join(__dirname, 'public')));

// Routes principales
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/chat', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'chat.html'));
});

// Routes pour toutes les applications
app.get('/futuristic-interface.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'futuristic-interface.html'));
});

app.get('/brain-visualization.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'brain-visualization.html'));
});

app.get('/qi-neuron-monitor.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'qi-neuron-monitor.html'));
});

app.get('/kyber-dashboard.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'kyber-dashboard.html'));
});

app.get('/generation-studio.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'generation-studio.html'));
});

app.get('/code-editor.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'code-editor.html'));
});

app.get('/agents.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'agents.html'));
});

app.get('/training.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'training.html'));
});

app.get('/security-dashboard.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'security-dashboard.html'));
});

app.get('/performance.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'performance.html'));
});

app.get('/settings-new.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'settings-new.html'));
});

app.get('/ltx-video.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'ltx-video.html'));
});

app.get('/camera-vision-interface.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'camera-vision-interface.html'));
});

app.get('/dreams-new.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'dreams-new.html'));
});

// API simple pour le statut
app.get('/api/system/status', (req, res) => {
    res.json({
        status: 'active',
        qi: 120,
        accelerators: 24,
        memory: 'optimal',
        timestamp: new Date().toISOString()
    });
});

// API simple pour les stats Kyber
app.get('/api/kyber/stats', (req, res) => {
    res.json({
        accelerators: [
            { name: 'Memory Optimizer', boost: '2x', status: 'active' },
            { name: 'QI Enhancer', boost: '2.8x', status: 'active' },
            { name: 'Thermal Cooler', boost: '1.8x', status: 'active' }
        ],
        totalBoost: '300%',
        efficiency: '95%'
    });
});

// Route pour le chat (API simple)
app.post('/api/chat/message', (req, res) => {
    const { message, userId } = req.body;

    // Réponse simple de l'IA
    const responses = [
        "Bonjour ! Je suis Louna, votre assistant IA créé par Jean-Luc Passave. Comment puis-je vous aider ?",
        "Ma mémoire thermique fonctionne parfaitement. Que souhaitez-vous savoir ?",
        "Mes accélérateurs Kyber sont optimisés à 300%. Je suis prêt à vous assister !",
        "Mon QI actuel est de 120. Je peux vous aider avec de nombreuses tâches.",
        "Système cognitif activé. En quoi puis-je vous être utile ?"
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    res.json({
        success: true,
        response: randomResponse,
        timestamp: new Date().toISOString(),
        userId: userId || 'anonymous'
    });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
    console.error('Erreur serveur:', err);
    res.status(500).json({
        error: 'Erreur interne du serveur',
        message: err.message
    });
});

// Route 404
app.use((req, res) => {
    res.status(404).json({
        error: 'Page non trouvée',
        path: req.path
    });
});

// Démarrage du serveur
const server = http.createServer(app);

server.listen(PORT, () => {
    console.log(`🚀 Serveur Louna Simple démarré sur le port ${PORT}`);
    console.log(`📱 Application: http://localhost:${PORT}`);
    console.log(`💬 Chat: http://localhost:${PORT}/chat`);
    console.log(`🧠 Mémoire Thermique: http://localhost:${PORT}/futuristic-interface.html`);
    console.log(`📊 Monitoring: http://localhost:${PORT}/qi-neuron-monitor.html`);
});

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
    console.log('🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

module.exports = app;
